<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
      <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
      <div class="col-sm-12 p-0">
        <div class="d-flex justify-content-between">
            <div>
                <h4 class="size20 mb-2 pt-2">Pricing Table</h4>                   
            </div>
          </div>   
    </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/pricing-table/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
 
    <code class="language-html" data-lang="html"><span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-primary"</span><span class="nt">&gt;</span>.text-primary<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-primary2"</span><span class="nt">&gt;</span>.text-primary2<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-primary3"</span><span class="nt">&gt;</span>.text-primary3<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-primary4"</span><span class="nt">&gt;</span>.text-primary4<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-primary5"</span><span class="nt">&gt;</span>.text-primary5<span class="nt">&lt;/p&gt;</span><br>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-secondary"</span><span class="nt">&gt;</span>.text-secondary<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-secondary2"</span><span class="nt">&gt;</span>.text-secondary2<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-secondary3"</span><span class="nt">&gt;</span>.text-secondary3<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-secondary4"</span><span class="nt">&gt;</span>.text-secondary4<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-secondary5"</span><span class="nt">&gt;</span>.text-secondary5<span class="nt">&lt;/p&gt;</span><br>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-info"</span><span class="nt">&gt;</span>.text-info<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-info2"</span><span class="nt">&gt;</span>.text-info2<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-info3"</span><span class="nt">&gt;</span>.text-info3<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-info4"</span><span class="nt">&gt;</span>.text-info4<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-info5"</span><span class="nt">&gt;</span>.text-info5<span class="nt">&lt;/p&gt;</span><br>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-light bg-dark"</span><span class="nt">&gt;</span>.text-light<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-light2 bg-dark"</span><span class="nt">&gt;</span>.text-light2<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-light3 bg-dark"</span><span class="nt">&gt;</span>.text-light3<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-light4 bg-dark"</span><span class="nt">&gt;</span>.text-light4<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-light5 bg-dark"</span><span class="nt">&gt;</span>.text-light5<span class="nt">&lt;/p&gt;</span><br>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-highlight"</span><span class="nt">&gt;</span>.text-highlight<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-highlight2"</span><span class="nt">&gt;</span>.text-highlight2<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-highlight3"</span><span class="nt">&gt;</span>.text-highlight3<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-highlight4"</span><span class="nt">&gt;</span>.text-highlight4<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-highlight5"</span><span class="nt">&gt;</span>.text-highlight5<span class="nt">&lt;/p&gt;</span><br>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-success"</span><span class="nt">&gt;</span>.text-success<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-danger"</span><span class="nt">&gt;</span>.text-danger<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-warning"</span><span class="nt">&gt;</span>.text-warning<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-dark"</span><span class="nt">&gt;</span>.text-dark<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-body"</span><span class="nt">&gt;</span>.text-body<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-muted"</span><span class="nt">&gt;</span>.text-muted<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-white bg-dark"</span><span class="nt">&gt;</span>.text-white<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-black-50"</span><span class="nt">&gt;</span>.text-black-50<span class="nt">&lt;/p&gt;</span>
    <span class="nt">&lt;p</span> <span class="na">class=</span><span class="s">"text-white-50 bg-dark"</span><span class="nt">&gt;</span>.text-white-50<span class="nt">&lt;/p&gt;</span></code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
body {
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #1a202c;
    text-align: left;
    background-color: #e2e8f0;
}
.card-header:first-child {
    border-radius: .25rem .25rem 0 0;
}
.p-3 {
    padding: 1rem!important;
}
.align-items-start {
    align-items: flex-start!important;
}
.flex-column {
    flex-direction: column!important;
}
.border-bottom {
    border-bottom: 1px solid #cbd5e0!important;
}
.card-footer, .card-header {
    display: flex;
    align-items: center;
}
.card-header {
    padding: .5rem 1rem;
    margin-bottom: 0;
    background-color: #fff;
    border-bottom: 0 solid rgba(0,0,0,.125);
}
.card-footer:last-child {
    border-radius: 0 0 .25rem .25rem;
}
.h3, h3 {
    font-size: 1.75rem;
}
.p-3 {
    padding: 1rem!important;
}
.justify-content-center {
    justify-content: center!important;
}
.card-footer, .card-header {
    display: flex;
    align-items: center;
}
.card-footer {
    padding: .5rem 1rem;
    background-color: #fff;
    border-top: 0 solid rgba(0,0,0,.125);
}
.card {
    box-shadow: 0 1px 3px 0 rgba(0,0,0,.1), 0 1px 2px 0 rgba(0,0,0,.06);
}
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 0 solid rgba(0,0,0,.125);
    border-radius: .25rem;
}
</code></pre>
        </div>
        <!-- <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">

            </code></pre>
        </div> -->
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
