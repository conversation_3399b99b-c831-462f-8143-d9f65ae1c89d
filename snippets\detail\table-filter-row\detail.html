
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    


    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
          <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
          <i class="fas fa-bars"></i>
        </span>
      </div>
<main role="main">



  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
        <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Table row filering</h4>
                  <div class="mb-2">
                      <a href="./downloads/table-filter-row.zip" class="btn btn-sm btn-outline-primary btn-dark mr-2"><i class="fas fa-download"></i>  Download zip</a> <button class="btn btn-sm btn-outline-info" id="fullScreen"><i class="fas fa-expand"></i>  View on Fullscreen</button>
                  </div>
              </div>
          </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/table-filter-row/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
&lt;div class="container-xl"&gt;
&lt;div class="table-responsive"&gt;
    &lt;div class="table-wrapper"&gt;
        &lt;div class="table-title"&gt;
            &lt;div class="row"&gt;
                &lt;div class="col-sm-6"&gt;&lt;h2&gt;Manage &lt;b&gt;Domains&lt;/b&gt;&lt;/h2&gt;&lt;/div&gt;
                &lt;div class="col-sm-6"&gt;
                    &lt;div class="btn-group" data-toggle="buttons"&gt;
                        &lt;label class="btn btn-info active"&gt;
                            &lt;input type="radio" name="status" value="all" checked="checked"&gt; All
                        &lt;/label&gt;
                        &lt;label class="btn btn-success"&gt;
                            &lt;input type="radio" name="status" value="active"&gt; Active
                        &lt;/label&gt;
                        &lt;label class="btn btn-warning"&gt;
                            &lt;input type="radio" name="status" value="inactive"&gt; Inactive
                        &lt;/label&gt;
                        &lt;label class="btn btn-danger"&gt;
                            &lt;input type="radio" name="status" value="expired"&gt; Expired
                        &lt;/label&gt;							
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;table class="table table-striped table-hover"&gt;
            &lt;thead&gt;
                &lt;tr&gt;
                    &lt;th&gt;#&lt;/th&gt;
                    &lt;th&gt;Domain&lt;/th&gt;
                    &lt;th&gt;Created&nbsp;On&lt;/th&gt;
                    &lt;th&gt;Status&lt;/th&gt;
                    &lt;th&gt;Server&nbsp;Location&lt;/th&gt;
                    &lt;th&gt;Action&lt;/th&gt;
                &lt;/tr&gt;
            &lt;/thead&gt;
            &lt;tbody&gt;
                &lt;tr data-status="active"&gt;
                    &lt;td&gt;1&lt;/td&gt;
                    &lt;td&gt;&lt;a href="#"&gt;thesmartcube.com&lt;/a&gt;&lt;/td&gt;
                    &lt;td&gt;04/10/2013&lt;/td&gt;
                    &lt;td&gt;&lt;span class="label label-success"&gt;Active&lt;/span&gt;&lt;/td&gt;
                    &lt;td&gt;Buenos Aires&lt;/td&gt;
                    &lt;td&gt;&lt;a href="#" class="btn btn-sm manage"&gt;Manage&lt;/a&gt;&lt;/td&gt;
                &lt;/tr&gt;
                &lt;tr data-status="inactive"&gt;
                    &lt;td&gt;2&lt;/td&gt;
                    &lt;td&gt;&lt;a href="#"&gt;amplipro.com&lt;/a&gt;&lt;/td&gt;
                    &lt;td&gt;05/08/2014&lt;/td&gt;
                    &lt;td&gt;&lt;span class="label label-warning"&gt;Inactive&lt;/span&gt;&lt;/td&gt;
                    &lt;td&gt;Australia&lt;/td&gt;
                    &lt;td&gt;&lt;a href="#" class="btn btn-sm manage"&gt;Manage&lt;/a&gt;&lt;/td&gt;
                &lt;/tr&gt;
                &lt;tr data-status="active"&gt;
                    &lt;td&gt;3&lt;/td&gt;
                    &lt;td&gt;&lt;a href="#"&gt;roche.com&lt;/a&gt;&lt;/td&gt;
                    &lt;td&gt;11/05/2015&lt;/td&gt;
                    &lt;td&gt;&lt;span class="label label-success"&gt;Active&lt;/span&gt;&lt;/td&gt;
                    &lt;td&gt;United Kingdom&lt;/td&gt;
                    &lt;td&gt;&lt;a href="#" class="btn btn-sm manage"&gt;Manage&lt;/a&gt;&lt;/td&gt;
                &lt;/tr&gt;
                &lt;tr data-status="expired"&gt;
                    &lt;td&gt;4&lt;/td&gt;
                    &lt;td&gt;&lt;a href="#"&gt;sansburry.org&lt;/a&gt;&lt;/td&gt;
                    &lt;td&gt;06/09/2016&lt;/td&gt;
                    &lt;td&gt;&lt;span class="label label-danger"&gt;Expired&lt;/span&gt;&lt;/td&gt;
                    &lt;td&gt;Romania&lt;/td&gt;
                    &lt;td&gt;&lt;a href="#" class="btn btn-sm manage"&gt;Manage&lt;/a&gt;&lt;/td&gt;
                &lt;/tr&gt;
                &lt;tr data-status="inactive"&gt;
                    &lt;td&gt;5&lt;/td&gt;
                    &lt;td&gt;&lt;a href="#"&gt;masters.com&lt;/a&gt;&lt;/td&gt;
                    &lt;td&gt;12/08/2017&lt;/td&gt;
                    &lt;td&gt;&lt;span class="label label-warning"&gt;Inactive&lt;/span&gt;&lt;/td&gt;
                    &lt;td&gt;Germany&lt;/td&gt;
                    &lt;td&gt;&lt;a href="#" class="btn btn-sm manage"&gt;Manage&lt;/a&gt;&lt;/td&gt;
                &lt;/tr&gt;
            &lt;/tbody&gt;
        &lt;/table&gt;
    &lt;/div&gt; 
&lt;/div&gt;   
&lt;/div&gt;   
</code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
              
body {
    color: #566787;
    background: #f5f5f5;
    
}
.table-responsive {
    margin: 30px 0;
}
.table-wrapper {
    width: 850px;
    background: #fff;
	margin: 0 auto;
    padding: 20px 30px 5px;
    box-shadow: 0 0 1px 0 rgba(0,0,0,.25);
}
.table-title .btn-group {
    float: right;
}
.table-title .btn {
    min-width: 50px;
    border: none;
    padding: 4px 10px;
    font-size: 85%;
    outline: none !important;
    height: 30px;
}
.table-title {
    min-width: 100%;
    border-bottom: 1px solid #e9e9e9;
    padding-bottom: 15px;
    margin-bottom: 5px;
    background: #455561;
    margin: -20px -31px 10px;
    padding: 15px 30px;
    color: #fff;
}
input:active, input:focus {
    outline: none;
}
.btn-group>.btn-group:not(:first-child), .btn-group>.btn:not(:first-child) {
    margin-left: -1px;
    border-radius: 0px !important;
}
.badge {
    font-weight: 400;
    padding: 6px;
}
.btn.focus, .btn:focus {
    outline: 0;
    box-shadow: none;
}
.table-title h2 {
    margin: 2px 0 0;
    font-size: 24px;
}
table.table {
    min-width: 100%;
}
table.table tr th, table.table tr td {
    border-color: #e9e9e9;
    padding: 12px 15px;
    vertical-align: middle;
}
table.table tr th:first-child {
    width: 40px;
}
table.table tr th:last-child {
    width: 100px;
}
table.table-striped tbody tr:nth-of-type(odd) {
    background-color: #fcfcfc;
}
table.table-striped.table-hover tbody tr:hover {
    background: #f5f5f5;
}
table.table td a {
    color: #00af9b;
}
table.table td .btn.manage {
    padding: 2px 10px;
    background: #007265;
    color: #fff;
    border-radius: 0;
    
}
table.table td .btn.manage:hover {
    background: #007265;		
}


          </code></pre>
        </div>
        <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">

$(document).ready(function(){
    $(".btn-group .btn").click(function(){
        var inputValue = $(this).find("input").val();
        if(inputValue != 'all'){
            var target = $('table tr[data-status="' + inputValue + '"]');
            $("table tbody tr").not(target).hide();
            target.fadeIn();
        } else {
            $("table tbody tr").fadeIn();
        }
    });
    // Changing the class of status label to support Bootstrap 4
    var bs = $.fn.tooltip.Constructor.VERSION;
    var str = bs.split(".");
    if(str[0] == 4){
        $(".label").each(function(){
            var classStr = $(this).attr("class");
            var newClassStr = classStr.replace(/label/g, "badge");
            $(this).removeAttr("class").addClass(newClassStr);
        });
    }
});
            </code></pre>
        </div>
       
      </div>
    </div></div>
  </div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});




      </script>
     <script src="./js/iframe.js"></script>
    </body>
</html>
