function runSpeechRecognition(row,line) {
    var row = row;
    var line = line;
    // get output div reference
    var output = document.getElementById("output"+row+'_'+line);
    // get action element reference
    var action = document.getElementById("action"+row+'_'+line);
    // new speech recognition object
    var SpeechRecognition = SpeechRecognition || webkitSpeechRecognition;
    var recognition = new SpeechRecognition();

    // This runs when the speech recognition service starts
    recognition.onstart = function() {
        // action.innerHTML = "<small>listening, please speak...</small>";
        $("#micIconA"+row+'_'+line).css('display','none');
        $("#micIconB"+row+'_'+line).css('display','inline-table');
    };
    
    recognition.onspeechend = function() {
        // action.innerHTML = "<small>stopped listening, hope you are done...</small>";
        recognition.stop();
        $("#micIconA"+row+'_'+line).css('display','inline-table');
        $("#micIconB"+row+'_'+line).css('display','none');
    }
  
    // This runs when the speech recognition service returns result
    recognition.onresult = function(event) {
        var transcript = event.results[0][0].transcript;
        // var confidence = event.results[0][0].confidence;

        var item = document.getElementById("output"+row+'_'+line).innerHTML;
        output.innerHTML =  item + " " + transcript;
        
        

        // output.classList.remove("hide");
    };
  
     // start recognition
     recognition.start();
}