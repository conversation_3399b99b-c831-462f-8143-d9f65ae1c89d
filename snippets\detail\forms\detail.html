
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

<main role="main">



  <div class="container-fluid mt-1">
    <div class="row">
      <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
      <div class="col-sm-12 p-0">
        <div class="d-flex justify-content-between">
            <div>
                <h4 class="size20 mb-2 pt-2">Forms</h4>                   
            </div>
          </div>   
    </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/forms/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
  &lt;h5 class="mt-0"&gt;Basic forms&lt;/h5&gt;
&lt;div class="bd-example"&gt;
  &lt;form&gt;
    &lt;div class="form-group"&gt;
    &lt;label for="exampleInputEmail1"&gt;Email address&lt;/label&gt;
    &lt;input type="email" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="Enter email"&gt;
    &lt;/div&gt;
    &lt;div class="form-group"&gt;
    &lt;label for="exampleInputPassword1"&gt;Password&lt;/label&gt;
    &lt;input type="password" class="form-control" id="exampleInputPassword1" placeholder="Password"&gt;
    &lt;/div&gt;
    &lt;div class="form-check"&gt;
    &lt;input type="checkbox" class="form-check-input" id="exampleCheck1"&gt;
    &lt;label class="form-check-label" for="exampleCheck1"&gt;Check me out&lt;/label&gt;
    &lt;/div&gt;
    &lt;br&gt;
    &lt;button type="submit" class="btn btn-primary"&gt;Submit&lt;/button&gt;
  &lt;/form&gt;
&lt;/div&gt;


&lt;h5 class="mt-4"&gt;Form Controls&lt;/h5&gt;
&lt;div class="bd-example"&gt;
&lt;form&gt;
&lt;div class="form-group"&gt;
  &lt;label for="exampleFormControlInput1"&gt;Email address&lt;/label&gt;
  &lt;input type="email" class="form-control" id="exampleFormControlInput1" placeholder="<EMAIL>"&gt;
&lt;/div&gt;
&lt;div class="form-group"&gt;
  &lt;label for="exampleFormControlSelect1"&gt;Example select&lt;/label&gt;
  &lt;select class="form-control" id="exampleFormControlSelect1"&gt;
    &lt;option&gt;1&lt;/option&gt;
    &lt;option&gt;2&lt;/option&gt;
    &lt;option&gt;3&lt;/option&gt;
    &lt;option&gt;4&lt;/option&gt;
    &lt;option&gt;5&lt;/option&gt;
  &lt;/select&gt;
&lt;/div&gt;
&lt;div class="form-group"&gt;
  &lt;label for="exampleFormControlSelect2"&gt;Example multiple select&lt;/label&gt;
  &lt;select multiple="" class="form-control" id="exampleFormControlSelect2"&gt;
    &lt;option&gt;1&lt;/option&gt;
    &lt;option&gt;2&lt;/option&gt;
    &lt;option&gt;3&lt;/option&gt;
    &lt;option&gt;4&lt;/option&gt;
    &lt;option&gt;5&lt;/option&gt;
  &lt;/select&gt;
&lt;/div&gt;
&lt;div class="form-group"&gt;
  &lt;label for="exampleFormControlTextarea1"&gt;Example textarea&lt;/label&gt;
  &lt;textarea class="form-control" id="exampleFormControlTextarea1" rows="3"&gt;&lt;/textarea&gt;
&lt;/div&gt;
&lt;/form&gt;
&lt;/div&gt;


&lt;h5  class="mt-4"&gt;Form Sizing&lt;/h5&gt;
&lt;p&gt;Set heights using classes like .form-control-lg and .form-control-sm.&lt;/p&gt;
&lt;div class="bd-example"&gt;
&lt;input class="form-control form-control-lg" type="text" placeholder=".form-control-lg"&gt;
&lt;br&gt;
&lt;input class="form-control" type="text" placeholder="Default input"&gt;
&lt;br&gt;
&lt;input class="form-control form-control-sm" type="text" placeholder=".form-control-sm"&gt;
&lt;/div&gt;
&lt;div class="highlight"&gt;&lt;pre&gt;&lt;code class="language-html" data-lang="html"&gt;&lt;span class="nt"&gt;&lt;input&lt;/span&gt; &lt;span class="na"&gt;class=&lt;/span&gt;&lt;span class="s"&gt;"form-control form-control-lg"&lt;/span&gt; &lt;span class="na"&gt;type=&lt;/span&gt;&lt;span class="s"&gt;"text"&lt;/span&gt; &lt;span class="na"&gt;placeholder=&lt;/span&gt;&lt;span class="s"&gt;".form-control-lg"&lt;/span&gt;&lt;span class="nt"&gt;&gt;&lt;/span&gt;
&lt;span class="nt"&gt;&lt;input&lt;/span&gt; &lt;span class="na"&gt;class=&lt;/span&gt;&lt;span class="s"&gt;"form-control"&lt;/span&gt; &lt;span class="na"&gt;type=&lt;/span&gt;&lt;span class="s"&gt;"text"&lt;/span&gt; &lt;span class="na"&gt;placeholder=&lt;/span&gt;&lt;span class="s"&gt;"Default input"&lt;/span&gt;&lt;span class="nt"&gt;&gt;&lt;/span&gt;
&lt;span class="nt"&gt;&lt;input&lt;/span&gt; &lt;span class="na"&gt;class=&lt;/span&gt;&lt;span class="s"&gt;"form-control form-control-sm"&lt;/span&gt; &lt;span class="na"&gt;type=&lt;/span&gt;&lt;span class="s"&gt;"text"&lt;/span&gt; &lt;span class="na"&gt;placeholder=&lt;/span&gt;&lt;span class="s"&gt;".form-control-sm"&lt;/span&gt;&lt;span class="nt"&gt;&gt;&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&lt;/div&gt;
&lt;div class="bd-example"&gt;
&lt;select class="form-control form-control-lg"&gt;
&lt;option&gt;Large select&lt;/option&gt;
&lt;/select&gt;
&lt;br&gt;
&lt;select class="form-control"&gt;
&lt;option&gt;Default select&lt;/option&gt;
&lt;/select&gt;
&lt;br&gt;
&lt;select class="form-control form-control-sm"&gt;
&lt;option&gt;Small select&lt;/option&gt;
&lt;/select&gt;
&lt;/div&gt;


&lt;h5  class="mt-4"&gt;Choose file input&lt;/h5&gt;
&lt;div class="bd-example"&gt;
&lt;form&gt;
&lt;div class="form-group"&gt;
  &lt;label for="exampleFormControlFile1"&gt;Example file input&lt;/label&gt;
  &lt;input type="file" class="form-control-file" id="exampleFormControlFile1"&gt;
&lt;/div&gt;
&lt;/form&gt;
&lt;/div&gt;



&lt;h5  class="mt-4"&gt;Read Only&lt;/h5&gt;
&lt;p&gt;Add the &lt;code class="highlighter-rouge"&gt;readonly&lt;/code&gt; boolean attribute on an input to prevent modification of the input’s value. Read-only inputs appear lighter (just like disabled inputs), but retain the standard cursor.&lt;/p&gt;
&lt;div class="bd-example"&gt;
&lt;input class="form-control" type="text" placeholder="Readonly input here…" readonly=""&gt;
&lt;/div&gt;




&lt;h4 class="mt-5 heading"&gt;Input groups&lt;/h4&gt;
&lt;p&gt;Easily extend form controls by adding text, buttons, or button groups on either side of textual inputs, custom selects, and custom file inputs.&lt;/p&gt;
&lt;h5  class="mt-4" id="basic-example"&gt;&lt;div&gt;Basic example&lt;a class="anchorjs-link " href="#basic-example" aria-label="Anchor" data-anchorjs-icon="#" style="padding-left: 0.375em;"&gt;&lt;/a&gt;&lt;/div&gt;&lt;/h5&gt;
&lt;p&gt;Place one add-on or button on either side of an input. You may also place one on both sides of an input. &lt;strong&gt;We do not support multiple form-controls in a single input group&lt;/strong&gt; and &lt;code class="highlighter-rouge"&gt;&lt;label&gt;&lt;/code&gt;s must come outside the input group.&lt;/p&gt;
&lt;div class="bd-example"&gt;
&lt;div class="input-group mb-3"&gt;
&lt;div class="input-group-prepend"&gt;
  &lt;span class="input-group-text" id="basic-addon1"&gt;@&lt;/span&gt;
&lt;/div&gt;
&lt;input type="text" class="form-control" placeholder="Username" aria-label="Username" aria-describedby="basic-addon1"&gt;
&lt;/div&gt;

&lt;div class="input-group mb-3"&gt;
&lt;input type="text" class="form-control" placeholder="Recipient's username" aria-label="Recipient's username" aria-describedby="basic-addon2"&gt;
&lt;div class="input-group-append"&gt;
  &lt;span class="input-group-text" id="basic-addon2"&gt;@example.com&lt;/span&gt;
&lt;/div&gt;
&lt;/div&gt;

&lt;label for="basic-url"&gt;Your vanity URL&lt;/label&gt;
&lt;div class="input-group mb-3"&gt;
&lt;div class="input-group-prepend"&gt;
  &lt;span class="input-group-text" id="basic-addon3"&gt;https://example.com/users/&lt;/span&gt;
&lt;/div&gt;
&lt;input type="text" class="form-control" id="basic-url" aria-describedby="basic-addon3"&gt;
&lt;/div&gt;

&lt;div class="input-group mb-3"&gt;
&lt;div class="input-group-prepend"&gt;
  &lt;span class="input-group-text"&gt;$&lt;/span&gt;
&lt;/div&gt;
&lt;input type="text" class="form-control" aria-label="Amount (to the nearest dollar)"&gt;
&lt;div class="input-group-append"&gt;
  &lt;span class="input-group-text"&gt;.00&lt;/span&gt;
&lt;/div&gt;
&lt;/div&gt;

&lt;div class="input-group"&gt;
&lt;div class="input-group-prepend"&gt;
  &lt;span class="input-group-text"&gt;With textarea&lt;/span&gt;
&lt;/div&gt;
&lt;textarea class="form-control" aria-label="With textarea"&gt;&lt;/textarea&gt;
&lt;/div&gt;
&lt;/div&gt;

&lt;h5 id="checkboxes-and-radios" class="mt-4"&gt;&lt;div&gt;Checkboxes and radios&lt;a class="anchorjs-link " href="#checkboxes-and-radios" aria-label="Anchor" data-anchorjs-icon="#" style="padding-left: 0.375em;"&gt;&lt;/a&gt;&lt;/div&gt;&lt;/h5&gt;
&lt;p&gt;Place any checkbox or radio option within an input group’s addon instead of text.&lt;/p&gt;
&lt;div class="bd-example"&gt;
&lt;div class="input-group mb-3"&gt;
&lt;div class="input-group-prepend"&gt;
  &lt;div class="input-group-text"&gt;
    &lt;input type="checkbox" aria-label="Checkbox for following text input"&gt;
  &lt;/div&gt;
&lt;/div&gt;
&lt;input type="text" class="form-control" aria-label="Text input with checkbox"&gt;
&lt;/div&gt;

&lt;div class="input-group"&gt;
&lt;div class="input-group-prepend"&gt;
  &lt;div class="input-group-text"&gt;
  &lt;input type="radio" aria-label="Radio button for following text input"&gt;
  &lt;/div&gt;
&lt;/div&gt;
&lt;input type="text" class="form-control" aria-label="Text input with radio button"&gt;
&lt;/div&gt;
&lt;/div&gt;



&lt;h5 id="multiple-inputs" class="mt-4"&gt;&lt;div&gt;Multiple inputs&lt;a class="anchorjs-link " href="#multiple-inputs" aria-label="Anchor" data-anchorjs-icon="#" style="padding-left: 0.375em;"&gt;&lt;/a&gt;&lt;/div&gt;&lt;/h5&gt;
&lt;p&gt;While multiple &lt;code class="highlighter-rouge"&gt;&lt;input&gt;&lt;/code&gt;s are supported visually, validation styles are only available for input groups with a single &lt;code class="highlighter-rouge"&gt;&lt;input&gt;&lt;/code&gt;.&lt;/p&gt;
&lt;div class="bd-example"&gt;
&lt;div class="input-group"&gt;
&lt;div class="input-group-prepend"&gt;
  &lt;span class="input-group-text"&gt;First and last name&lt;/span&gt;
&lt;/div&gt;
&lt;input type="text" class="form-control"&gt;
&lt;input type="text" class="form-control"&gt;
&lt;/div&gt;
&lt;/div&gt;


&lt;h5 id="buttons-with-dropdowns" class="mt-4"&gt;&lt;div&gt;Buttons with dropdowns&lt;a class="anchorjs-link " href="#buttons-with-dropdowns" aria-label="Anchor" data-anchorjs-icon="#" style="padding-left: 0.375em;"&gt;&lt;/a&gt;&lt;/div&gt;&lt;/h5&gt;
&lt;div class="bd-example"&gt;
&lt;div class="input-group mb-3"&gt;
&lt;div class="input-group-prepend"&gt;
  &lt;button class="btn btn-outline-secondary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"&gt;Dropdown&lt;/button&gt;
  &lt;div class="dropdown-menu"&gt;
    &lt;a class="dropdown-item" href="#"&gt;Action&lt;/a&gt;
    &lt;a class="dropdown-item" href="#"&gt;Another action&lt;/a&gt;
    &lt;a class="dropdown-item" href="#"&gt;Something else here&lt;/a&gt;
    &lt;div role="separator" class="dropdown-divider"&gt;&lt;/div&gt;
    &lt;a class="dropdown-item" href="#"&gt;Separated link&lt;/a&gt;
  &lt;/div&gt;
&lt;/div&gt;
&lt;input type="text" class="form-control" aria-label="Text input with dropdown button"&gt;
&lt;/div&gt;

&lt;div class="input-group"&gt;
&lt;input type="text" class="form-control" aria-label="Text input with dropdown button"&gt;
&lt;div class="input-group-append"&gt;
  &lt;button class="btn btn-outline-secondary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"&gt;Dropdown&lt;/button&gt;
  &lt;div class="dropdown-menu"&gt;
    &lt;a class="dropdown-item" href="#"&gt;Action&lt;/a&gt;
    &lt;a class="dropdown-item" href="#"&gt;Another action&lt;/a&gt;
    &lt;a class="dropdown-item" href="#"&gt;Something else here&lt;/a&gt;
    &lt;div role="separator" class="dropdown-divider"&gt;&lt;/div&gt;
    &lt;a class="dropdown-item" href="#"&gt;Separated link&lt;/a&gt;
  &lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;


&lt;h5 id="custom-select"  class="mt-4"&gt;&lt;div&gt;Custom select&lt;a class="anchorjs-link " href="#custom-select" aria-label="Anchor" data-anchorjs-icon="#" style="padding-left: 0.375em;"&gt;&lt;/a&gt;&lt;/div&gt;&lt;/h5&gt;
&lt;div class="bd-example"&gt;
&lt;div class="input-group mb-3"&gt;
&lt;div class="input-group-prepend"&gt;
  &lt;label class="input-group-text" for="inputGroupSelect01"&gt;Options&lt;/label&gt;
&lt;/div&gt;
&lt;select class="custom-select" id="inputGroupSelect01"&gt;
  &lt;option selected=""&gt;Choose...&lt;/option&gt;
  &lt;option value="1"&gt;One&lt;/option&gt;
  &lt;option value="2"&gt;Two&lt;/option&gt;
  &lt;option value="3"&gt;Three&lt;/option&gt;
&lt;/select&gt;
&lt;/div&gt;

&lt;div class="input-group mb-3"&gt;
&lt;select class="custom-select" id="inputGroupSelect02"&gt;
  &lt;option selected=""&gt;Choose...&lt;/option&gt;
  &lt;option value="1"&gt;One&lt;/option&gt;
  &lt;option value="2"&gt;Two&lt;/option&gt;
  &lt;option value="3"&gt;Three&lt;/option&gt;
&lt;/select&gt;
&lt;div class="input-group-append"&gt;
  &lt;label class="input-group-text" for="inputGroupSelect02"&gt;Options&lt;/label&gt;
&lt;/div&gt;
&lt;/div&gt;

&lt;div class="input-group mb-3"&gt;
&lt;div class="input-group-prepend"&gt;
  &lt;button class="btn btn-outline-secondary" type="button"&gt;Button&lt;/button&gt;
&lt;/div&gt;
&lt;select class="custom-select" id="inputGroupSelect03"&gt;
  &lt;option selected=""&gt;Choose...&lt;/option&gt;
  &lt;option value="1"&gt;One&lt;/option&gt;
  &lt;option value="2"&gt;Two&lt;/option&gt;
  &lt;option value="3"&gt;Three&lt;/option&gt;
&lt;/select&gt;
&lt;/div&gt;

&lt;div class="input-group"&gt;
&lt;select class="custom-select" id="inputGroupSelect04"&gt;
  &lt;option selected=""&gt;Choose...&lt;/option&gt;
  &lt;option value="1"&gt;One&lt;/option&gt;
  &lt;option value="2"&gt;Two&lt;/option&gt;
  &lt;option value="3"&gt;Three&lt;/option&gt;
&lt;/select&gt;
&lt;div class="input-group-append"&gt;
  &lt;button class="btn btn-outline-secondary" type="button"&gt;Button&lt;/button&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;

&lt;h5 id="custom-file-input"  class="mt-4"&gt;&lt;div&gt;Custom file input&lt;a class="anchorjs-link " href="#custom-file-input" aria-label="Anchor" data-anchorjs-icon="#" style="padding-left: 0.375em;"&gt;&lt;/a&gt;&lt;/div&gt;&lt;/h5&gt;
&lt;div class="bd-example"&gt;
&lt;div class="input-group mb-3"&gt;
&lt;div class="input-group-prepend"&gt;
  &lt;span class="input-group-text"&gt;Upload&lt;/span&gt;
&lt;/div&gt;
&lt;div class="custom-file"&gt;
  &lt;input type="file" class="custom-file-input" id="inputGroupFile01"&gt;
  &lt;label class="custom-file-label" for="inputGroupFile01"&gt;Choose file&lt;/label&gt;
&lt;/div&gt;
&lt;/div&gt;

&lt;div class="input-group mb-3"&gt;
&lt;div class="custom-file"&gt;
  &lt;input type="file" class="custom-file-input" id="inputGroupFile02"&gt;
  &lt;label class="custom-file-label" for="inputGroupFile02"&gt;Choose file&lt;/label&gt;
&lt;/div&gt;
&lt;div class="input-group-append"&gt;
  &lt;span class="input-group-text"&gt;Upload&lt;/span&gt;
&lt;/div&gt;
&lt;/div&gt;

&lt;div class="input-group mb-3"&gt;
&lt;div class="input-group-prepend"&gt;
  &lt;button class="btn btn-outline-secondary" type="button"&gt;Button&lt;/button&gt;
&lt;/div&gt;
&lt;div class="custom-file"&gt;
  &lt;input type="file" class="custom-file-input" id="inputGroupFile03"&gt;
  &lt;label class="custom-file-label" for="inputGroupFile03"&gt;Choose file&lt;/label&gt;
&lt;/div&gt;
&lt;/div&gt;

&lt;div class="input-group"&gt;
&lt;div class="custom-file"&gt;
  &lt;input type="file" class="custom-file-input" id="inputGroupFile04"&gt;
  &lt;label class="custom-file-label" for="inputGroupFile04"&gt;Choose file&lt;/label&gt;
&lt;/div&gt;
&lt;div class="input-group-append"&gt;
  &lt;button class="btn btn-outline-secondary" type="button"&gt;Button&lt;/button&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;




</code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"></pre>
        </div>
        <!-- <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">

            </code></pre>
        </div> -->
       
      </div>
    </div>
  </div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
