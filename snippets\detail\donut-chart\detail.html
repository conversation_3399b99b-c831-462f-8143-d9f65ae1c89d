<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Component Library</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">
      <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Animated donut chart</h4>
              <div class="mb-2">
                <a
                  href="./downloads/donut-chart.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/donut-chart/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
              <pre class="line-numbers">
<code class="language-markup">&lt;div id="chartdiv"&gt;&lt;/div&gt;</code>
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">#chartdiv {
  width: 100%;
  height: 500px;
}

</code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">
                // Define data for each year
                var chartData = {
                    "1995": [
                        { sector: "Agriculture", size: 6.6 },
                        { sector: "Mining and Quarrying", size: 0.6 },
                        { sector: "Manufacturing", size: 23.2 },
                        { sector: "Electricity and Water", size: 2.2 },
                        { sector: "Construction", size: 4.5 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 14.6 },
                        { sector: "Transport and Communication", size: 9.3 },
                        { sector: "Finance, real estate and business services", size: 22.5 }],
                    "1996": [
                        { sector: "Agriculture", size: 6.4 },
                        { sector: "Mining and Quarrying", size: 0.5 },
                        { sector: "Manufacturing", size: 22.4 },
                        { sector: "Electricity and Water", size: 2 },
                        { sector: "Construction", size: 4.2 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 14.8 },
                        { sector: "Transport and Communication", size: 9.7 },
                        { sector: "Finance, real estate and business services", size: 22 }],
                    "1997": [
                        { sector: "Agriculture", size: 6.1 },
                        { sector: "Mining and Quarrying", size: 0.2 },
                        { sector: "Manufacturing", size: 20.9 },
                        { sector: "Electricity and Water", size: 1.8 },
                        { sector: "Construction", size: 4.2 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 13.7 },
                        { sector: "Transport and Communication", size: 9.4 },
                        { sector: "Finance, real estate and business services", size: 22.1 }],
                    "1998": [
                        { sector: "Agriculture", size: 6.2 },
                        { sector: "Mining and Quarrying", size: 0.3 },
                        { sector: "Manufacturing", size: 21.4 },
                        { sector: "Electricity and Water", size: 1.9 },
                        { sector: "Construction", size: 4.2 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 14.5 },
                        { sector: "Transport and Communication", size: 10.6 },
                        { sector: "Finance, real estate and business services", size: 23 }],
                    "1999": [
                        { sector: "Agriculture", size: 5.7 },
                        { sector: "Mining and Quarrying", size: 0.2 },
                        { sector: "Manufacturing", size: 20 },
                        { sector: "Electricity and Water", size: 1.8 },
                        { sector: "Construction", size: 4.4 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 15.2 },
                        { sector: "Transport and Communication", size: 10.5 },
                        { sector: "Finance, real estate and business services", size: 24.7 }],
                    "2000": [
                        { sector: "Agriculture", size: 5.1 },
                        { sector: "Mining and Quarrying", size: 0.3 },
                        { sector: "Manufacturing", size: 20.4 },
                        { sector: "Electricity and Water", size: 1.7 },
                        { sector: "Construction", size: 4 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 16.3 },
                        { sector: "Transport and Communication", size: 10.7 },
                        { sector: "Finance, real estate and business services", size: 24.6 }],
                    "2001": [
                        { sector: "Agriculture", size: 5.5 },
                        { sector: "Mining and Quarrying", size: 0.2 },
                        { sector: "Manufacturing", size: 20.3 },
                        { sector: "Electricity and Water", size: 1.6 },
                        { sector: "Construction", size: 3.1 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 16.3 },
                        { sector: "Transport and Communication", size: 10.7 },
                        { sector: "Finance, real estate and business services", size: 25.8 }],
                    "2002": [
                        { sector: "Agriculture", size: 5.7 },
                        { sector: "Mining and Quarrying", size: 0.2 },
                        { sector: "Manufacturing", size: 20.5 },
                        { sector: "Electricity and Water", size: 1.6 },
                        { sector: "Construction", size: 3.6 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 16.1 },
                        { sector: "Transport and Communication", size: 10.7 },
                        { sector: "Finance, real estate and business services", size: 26 }],
                    "2003": [
                        { sector: "Agriculture", size: 4.9 },
                        { sector: "Mining and Quarrying", size: 0.2 },
                        { sector: "Manufacturing", size: 19.4 },
                        { sector: "Electricity and Water", size: 1.5 },
                        { sector: "Construction", size: 3.3 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 16.2 },
                        { sector: "Transport and Communication", size: 11 },
                        { sector: "Finance, real estate and business services", size: 27.5 }],
                    "2004": [
                        { sector: "Agriculture", size: 4.7 },
                        { sector: "Mining and Quarrying", size: 0.2 },
                        { sector: "Manufacturing", size: 18.4 },
                        { sector: "Electricity and Water", size: 1.4 },
                        { sector: "Construction", size: 3.3 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 16.9 },
                        { sector: "Transport and Communication", size: 10.6 },
                        { sector: "Finance, real estate and business services", size: 28.1 }],
                    "2005": [
                        { sector: "Agriculture", size: 4.3 },
                        { sector: "Mining and Quarrying", size: 0.2 },
                        { sector: "Manufacturing", size: 18.1 },
                        { sector: "Electricity and Water", size: 1.4 },
                        { sector: "Construction", size: 3.9 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 15.7 },
                        { sector: "Transport and Communication", size: 10.6 },
                        { sector: "Finance, real estate and business services", size: 29.1 }],
                    "2006": [
                        { sector: "Agriculture", size: 4 },
                        { sector: "Mining and Quarrying", size: 0.2 },
                        { sector: "Manufacturing", size: 16.5 },
                        { sector: "Electricity and Water", size: 1.3 },
                        { sector: "Construction", size: 3.7 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 14.2 },
                        { sector: "Transport and Communication", size: 12.1 },
                        { sector: "Finance, real estate and business services", size: 29.1 }],
                    "2007": [
                        { sector: "Agriculture", size: 4.7 },
                        { sector: "Mining and Quarrying", size: 0.2 },
                        { sector: "Manufacturing", size: 16.2 },
                        { sector: "Electricity and Water", size: 1.2 },
                        { sector: "Construction", size: 4.1 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 15.6 },
                        { sector: "Transport and Communication", size: 11.2 },
                        { sector: "Finance, real estate and business services", size: 30.4 }],
                    "2008": [
                        { sector: "Agriculture", size: 4.9 },
                        { sector: "Mining and Quarrying", size: 0.3 },
                        { sector: "Manufacturing", size: 17.2 },
                        { sector: "Electricity and Water", size: 1.4 },
                        { sector: "Construction", size: 5.1 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 15.4 },
                        { sector: "Transport and Communication", size: 11.1 },
                        { sector: "Finance, real estate and business services", size: 28.4 }],
                    "2009": [
                        { sector: "Agriculture", size: 4.7 },
                        { sector: "Mining and Quarrying", size: 0.3 },
                        { sector: "Manufacturing", size: 16.4 },
                        { sector: "Electricity and Water", size: 1.9 },
                        { sector: "Construction", size: 4.9 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 15.5 },
                        { sector: "Transport and Communication", size: 10.9 },
                        { sector: "Finance, real estate and business services", size: 27.9 }],
                    "2010": [
                        { sector: "Agriculture", size: 4.2 },
                        { sector: "Mining and Quarrying", size: 0.3 },
                        { sector: "Manufacturing", size: 16.2 },
                        { sector: "Electricity and Water", size: 2.2 },
                        { sector: "Construction", size: 4.3 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 15.7 },
                        { sector: "Transport and Communication", size: 10.2 },
                        { sector: "Finance, real estate and business services", size: 28.8 }],
                    "2011": [
                        { sector: "Agriculture", size: 4.1 },
                        { sector: "Mining and Quarrying", size: 0.3 },
                        { sector: "Manufacturing", size: 14.9 },
                        { sector: "Electricity and Water", size: 2.3 },
                        { sector: "Construction", size: 5 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 17.3 },
                        { sector: "Transport and Communication", size: 10.2 },
                        { sector: "Finance, real estate and business services", size: 27.2 }],
                    "2012": [
                        { sector: "Agriculture", size: 3.8 },
                        { sector: "Mining and Quarrying", size: 0.3 },
                        { sector: "Manufacturing", size: 14.9 },
                        { sector: "Electricity and Water", size: 2.6 },
                        { sector: "Construction", size: 5.1 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 15.8 },
                        { sector: "Transport and Communication", size: 10.7 },
                        { sector: "Finance, real estate and business services", size: 28 }],
                    "2013": [
                        { sector: "Agriculture", size: 3.7 },
                        { sector: "Mining and Quarrying", size: 0.2 },
                        { sector: "Manufacturing", size: 14.9 },
                        { sector: "Electricity and Water", size: 2.7 },
                        { sector: "Construction", size: 5.7 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 16.5 },
                        { sector: "Transport and Communication", size: 10.5 },
                        { sector: "Finance, real estate and business services", size: 26.6 }],
                    "2014": [
                        { sector: "Agriculture", size: 3.9 },
                        { sector: "Mining and Quarrying", size: 0.2 },
                        { sector: "Manufacturing", size: 14.5 },
                        { sector: "Electricity and Water", size: 2.7 },
                        { sector: "Construction", size: 5.6 },
                        { sector: "Trade (Wholesale, Retail, Motor)", size: 16.6 },
                        { sector: "Transport and Communication", size: 10.5 },
                        { sector: "Finance, real estate and business services", size: 26.5 }]
                };
        
                var root = am5.Root.new("chartdiv");
                root.setThemes([
                    am5themes_Animated.new(root)
                ]);
                var chart = root.container.children.push(am5percent.PieChart.new(root, {
                    innerRadius: 100,
                    layout: root.verticalLayout
                }));
                chart.children.unshift(
                    am5.Label.new(root, {
                        text: "Animated timeline series using donut chart",
                        fontSize: 16,
                        fontWeight: "bold",
                        textAlign: "center",
                        x: am5.percent(50),
                        centerX: am5.percent(50),
                    })
                );
                var series = chart.series.push(am5percent.PieSeries.new(root, {
                    valueField: "size",
                    categoryField: "sector",
                    colors: am5.ColorSet.new(root, {
                        colors: [
                            am5.color(0x00b19c),
                            am5.color(0x3bcd3f),
                            am5.color(0x007365),
                            am5.color(0x8dbac4),
                            am5.color(0x002035),
                            am5.color(0x33c1b0),
                            am5.color(0x62d765),
                            am5.color(0x338f84),
                            am5.color(0xa4c8d0),
                            am5.color(0x334d5d),
                            am5.color(0x89e18c),
                            am5.color(0x66aba3),
                            am5.color(0xbbd6dc),
                        ]
                    })
                }));
                series.data.setAll([
                    { sector: "Agriculture", size: 6.6 },
                    { sector: "Mining and Quarrying", size: 0.6 },
                    { sector: "Manufacturing", size: 23.2 },
                    { sector: "Electricity and Water", size: 2.2 },
                    { sector: "Construction", size: 4.5 },
                    { sector: "Trade (Wholesale, Retail, Motor)", size: 14.6 },
                    { sector: "Transport and Communication", size: 9.3 },
                    { sector: "Finance, real estate and business services", size: 22.5 }
                ]);
                series.appear(1000, 100);
        
                // Add label
                var label = root.tooltipContainer.children.push(am5.Label.new(root, {
                    x: am5.p50,
                    y: am5.p50,
                    centerX: am5.p50,
                    centerY: am5.p50,
                    fill: am5.color(0x000000),
                    fontSize: 50
                }));
                // Animate chart data
                var currentYear = 1995;
                function getCurrentData() {
                    var data = chartData[currentYear];
                    currentYear++;
                    if (currentYear > 2014)
                        currentYear = 1995;
                    return data;
                }
                function loop() {
                    label.set("text", currentYear);
                    var data = getCurrentData();
                    for (var i = 0; i < data.length; i++) {
                        series.data.setIndex(i, data[i]);
                    }
                    chart.setTimeout(loop, 1000);
                }
        
                loop();
            </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
