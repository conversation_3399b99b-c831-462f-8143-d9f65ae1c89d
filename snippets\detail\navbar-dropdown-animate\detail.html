
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}


    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
          <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
          <i class="fas fa-bars"></i>
        </span>
      </div>
<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
        <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
                <div>
                    <h4 class="size20 mb-2 pt-2">Animated Navbar</h4>                   
                </div>
              </div>   
        </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/navbar-dropdown-animate/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
    &lt;header class="header-area overlay"&gt;
    &lt;nav class="navbar navbar-expand-md navbar-dark"&gt;
        &lt;div class="container"&gt;
            &lt;a href="#" class="navbar-brand"&gt;The Smart Cube&lt;/a&gt;
            
            &lt;button type="button" class="navbar-toggler collapsed" data-toggle="collapse" data-target="#main-nav"&gt;
                &lt;span class="menu-icon-bar"&gt;&lt;/span&gt;
                &lt;span class="menu-icon-bar"&gt;&lt;/span&gt;
                &lt;span class="menu-icon-bar"&gt;&lt;/span&gt;
            &lt;/button&gt;
            
            &lt;div id="main-nav" class="collapse navbar-collapse"&gt;
                &lt;ul class="navbar-nav ml-auto"&gt;
                    &lt;li&gt;&lt;a href="#" class="nav-item nav-link active"&gt;Home&lt;/a&gt;&lt;/li&gt;
                    &lt;li&gt;&lt;a href="#" class="nav-item nav-link"&gt;About Us&lt;/a&gt;&lt;/li&gt;
                    &lt;li class="dropdown"&gt;
                        &lt;a href="#" class="nav-item nav-link" data-toggle="dropdown"&gt;Services&lt;/a&gt;
                        &lt;div class="dropdown-menu"&gt;
                            &lt;a href="#" class="dropdown-item"&gt;Dropdown Item 1&lt;/a&gt;
                            &lt;a href="#" class="dropdown-item"&gt;Dropdown Item 2&lt;/a&gt;
                            &lt;a href="#" class="dropdown-item"&gt;Dropdown Item 3&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/li&gt;
                    &lt;li class="dropdown"&gt;
                        &lt;a href="#" class="nav-item nav-link" data-toggle="dropdown"&gt;Portfolio&lt;/a&gt;
                        &lt;div class="dropdown-menu"&gt;
                            &lt;a href="#" class="dropdown-item"&gt;Dropdown Item 1&lt;/a&gt;
                            &lt;a href="#" class="dropdown-item"&gt;Dropdown Item 2&lt;/a&gt;
                            &lt;a href="#" class="dropdown-item"&gt;Dropdown Item 3&lt;/a&gt;
                            &lt;a href="#" class="dropdown-item"&gt;Dropdown Item 4&lt;/a&gt;
                            &lt;a href="#" class="dropdown-item"&gt;Dropdown Item 5&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/li&gt;
                    &lt;li&gt;&lt;a href="#" class="nav-item nav-link"&gt;Contact&lt;/a&gt;&lt;/li&gt;
                &lt;/ul&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/nav&gt;
    
    &lt;div class="banner"&gt;
        &lt;div class="container"&gt;
            &lt;h1&gt;Animated Drop Down Menu&lt;/h1&gt;
            &lt;p&gt;Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer nec elit ex. Etiam elementum lectus et tempor molestie.&lt;/p&gt;
            &lt;a href="#content" class="button btn-primary"&gt;Learn More&lt;/a&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/header&gt;

&lt;main&gt;
    &lt;section id="content" class="content"&gt;
        &lt;div class="container"&gt;
            &lt;div class="row"&gt;
                &lt;div class="col-md-4"&gt;
                    &lt;p&gt;Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer nec elit ex. Etiam elementum lectus et tempor molestie. Pellentesque vestibulum dui sit amet dui volutpat sollicitudin. Etiam non erat finibus, iaculis nunc vel, convallis eros. Etiam efficitur tempor dui, vitae fringilla ipsum tristique quis. Aliquam erat volutpat. Cras ullamcorper ex et viverra vulputate. Nam lectus ligula, pretium nec risus nec, ultricies fringilla mauris. Proin quis venenatis neque, iaculis porta nulla. &lt;/p&gt;
                &lt;/div&gt;
                &lt;div class="col-md-4"&gt;
                    &lt;p&gt;Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer nec elit ex. Etiam elementum lectus et tempor molestie. Pellentesque vestibulum dui sit amet dui volutpat sollicitudin. Etiam non erat finibus, iaculis nunc vel, convallis eros. Etiam efficitur tempor dui, vitae fringilla ipsum tristique quis. Aliquam erat volutpat. Cras ullamcorper ex et viverra vulputate. Nam lectus ligula, pretium nec risus nec, ultricies fringilla mauris. Proin quis venenatis neque, iaculis porta nulla. &lt;/p&gt;
                &lt;/div&gt;
                &lt;div class="col-md-4"&gt;
                    &lt;p&gt;Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer nec elit ex. Etiam elementum lectus et tempor molestie. Pellentesque vestibulum dui sit amet dui volutpat sollicitudin. Etiam non erat finibus, iaculis nunc vel, convallis eros. Etiam efficitur tempor dui, vitae fringilla ipsum tristique quis. Aliquam erat volutpat. Cras ullamcorper ex et viverra vulputate. Nam lectus ligula, pretium nec risus nec, ultricies fringilla mauris. Proin quis venenatis neque, iaculis porta nulla. &lt;/p&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/section&gt;
&lt;/main&gt;
</code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
html {
    font-size: 62.5%;
    }
    body {
    font-family: 'Fira Sans', sans-serif;
    font-size: 1.6rem;
    font-weight: 400;
    }
    h1 {
    margin-bottom: 0.5em;
    font-size: 3.6rem;
    }
    p {
    margin-bottom: 0.5em;
    font-size: 1.6rem;
    line-height: 1.6;
    }
    .button {
    display: inline-block;
    margin-top: 20px;
    padding: 8px 25px;
    border-radius: 0px;
    }
    
    .button-primary::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    background-color: #fff;
    border-radius: 0px;
    opacity: 0;
    -webkit-transform: scaleX(0.8);
    -ms-transform: scaleX(0.8);
    transform: scaleX(0.8);
    transition: all 0.3s ease-in;
    z-index: -1;
    }
    .button-primary:hover::after {
    opacity: 1;
    -webkit-transform: scaleX(1);
    -ms-transform: scaleX(1);
    transform: scaleX(1);
    }
    .overlay::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    background-color: rgba(0, 0, 0, .3);
    }
    .header-area {
    position: relative;
    height: 100vh;
    background: #455561;
    background-attachment: fixed;
    background-position: center center;
    background-repeat: no-repear;
    background-size: cover;
    }
    .banner {
    display: flex;
    align-items: center;
    position: relative;
    height: 100%;
    color: #fff;
    text-align: center;
    z-index: 1;
    }
    .banner h1 {
    font-weight: 800;
    }
    .banner p {
    font-weight: 700;
    }
    .navbar {
    position: absolute;
    left: 0;
    top: 0;
    padding: 0;
    width: 100%;
    transition: background 0.6s ease-in;
    z-index: 99999;
    }
    .navbar .navbar-brand {
    font-family: 'Fira Sans', cursive;
    font-size: 2.5rem;
    }
    .navbar .navbar-toggler {
    position: relative;
    height: 50px;
    width: 50px;
    border: none;
    cursor: pointer;
    outline: none;
    }
    .navbar .navbar-toggler .menu-icon-bar {
    position: absolute;
    left: 15px;
    right: 15px;
    height: 2px;
    background-color: #fff;
    opacity: 0;
    -webkit-transform: translateY(-1px);
    -ms-transform: translateY(-1px);
    transform: translateY(-1px);
    transition: all 0.3s ease-in;
    }
    .navbar .navbar-toggler .menu-icon-bar:first-child {
    opacity: 1;
    -webkit-transform: translateY(-1px) rotate(45deg);
    -ms-sform: translateY(-1px) rotate(45deg);
    transform: translateY(-1px) rotate(45deg);
    }
    .navbar .navbar-toggler .menu-icon-bar:last-child {
    opacity: 1;
    -webkit-transform: translateY(-1px) rotate(135deg);
    -ms-sform: translateY(-1px) rotate(135deg);
    transform: translateY(-1px) rotate(135deg);
    }
    .navbar .navbar-toggler.collapsed .menu-icon-bar {
    opacity: 1;
    }
    .navbar .navbar-toggler.collapsed .menu-icon-bar:first-child {
    -webkit-transform: translateY(-7px) rotate(0);
    -ms-sform: translateY(-7px) rotate(0);
    transform: translateY(-7px) rotate(0);
    }
    .navbar .navbar-toggler.collapsed .menu-icon-bar:last-child {
    -webkit-transform: translateY(5px) rotate(0);
    -ms-sform: translateY(5px) rotate(0);
    transform: translateY(5px) rotate(0);
    }
    .navbar-dark .navbar-nav .nav-link {
    position: relative;
    color: #fff;
    font-size: 1.6rem;
    }
    .navbar-dark .navbar-nav .nav-link:focus, .navbar-dark .navbar-nav .nav-link:hover {
    color: #fff;
    }
    .navbar .dropdown-menu {
    padding: 0;
    background-color: rgba(0, 0, 0, .9);
    }
    .navbar .dropdown-menu .dropdown-item {
    position: relative;
    padding: 10px 20px;
    color: #fff;
    font-size: 1.4rem;
    border-bottom: 1px solid rgba(255, 255, 255, .1);
    transition: color 0.2s ease-in;
    }
    .navbar .dropdown-menu .dropdown-item:last-child {
    border-bottom: none;
    }
    .navbar .dropdown-menu .dropdown-item:hover {
    background: transparent;
    color: #fff;
    }
    .navbar .dropdown-menu .dropdown-item::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    top: 0;
    width: 5px;
    background-color: #fff;
    opacity: 0;
    transition: opacity 0.2s ease-in;
    }
    #tsc_nav_1 .navbar .nav-link:focus, .navbar .nav-link:hover, .navbar .nav-item:hover {
        background: transparent;
        color: white !important;
    }
    .navbar .dropdown-menu .dropdown-item:hover::before {
    opacity: 1;
    }
    .navbar.fixed-top {
    position: fixed;
    -webkit-animation: navbar-animation 0.6s;
    animation: navbar-animation 0.6s;
    background-color: rgba(0, 0, 0, .9);
    }
    .navbar.fixed-top.navbar-dark .navbar-nav .nav-link.active {
    color: #fff;
    }
    .navbar.fixed-top.navbar-dark .navbar-nav .nav-link::after {
    background-color: #fff;
    }
    .content {
    padding: 120px 0;
    }
    @media screen and (max-width: 768px) {
    .navbar-brand {
        margin-left: 20px;
    }
    .navbar-nav {
        padding: 0 20px;
        background-color: rgba(0, 0, 0, .9);
    }
    .navbar.fixed-top .navbar-nav {
        background: transparent;
    }
    }
    @media screen and (min-width: 767px) {
    .banner {
        padding: 0 150px;
    }
    .banner h1 {
        font-size: 5rem;
    }
    .banner p {
        font-size: 2rem;
    }
    .navbar-dark .navbar-nav .nav-link {
        padding: 23px 15px;
    }
    .navbar-dark .navbar-nav .nav-link::after {
        content: '';
        position: absolute;
        bottom: 15px;
        left: 30%;
        right: 30%;
        height: 1px;
        background-color: #fff;
        -webkit-transform: scaleX(0);
        -ms-transform: scaleX(0);
        transform: scaleX(0);
        transition: transform 0.1s ease-in;
    }
    .navbar-dark .navbar-nav .nav-link:hover::after {
        -webkit-transform: scaleX(1);
        -ms-transform: scaleX(1);
        transform: scaleX(1);
    }
    .dropdown-menu {
        min-width: 200px;
        -webkit-animation: dropdown-animation 0.3s;
        animation: dropdown-animation 0.3s;
        -webkit-transform-origin: top;
        -ms-transform-origin: top;
        transform-origin: top;
    }
    }
    @-webkit-keyframes navbar-animation {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-100%);
        -ms-transform: translateY(-100%);
        transform: translateY(-100%);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
    }
    @keyframes navbar-animation {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-100%);
        -ms-transform: translateY(-100%);
        transform: translateY(-100%);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
    }
    @-webkit-keyframes dropdown-animation {
    0% {
        -webkit-transform: scaleY(0);
        -ms-transform: scaleY(0);
        transform: scaleY(0);
    }
    75% {
        -webkit-transform: scaleY(1.1);
        -ms-transform: scaleY(1.1);
        transform: scaleY(1.1);
    }
    100% {
        -webkit-transform: scaleY(1);
        -ms-transform: scaleY(1);
        transform: scaleY(1);
    }
    }
    @keyframes dropdown-animation {
    0% {
        -webkit-transform: scaleY(0);
        -ms-transform: scaleY(0);
        transform: scaleY(0);
    }
    75% {
        -webkit-transform: scaleY(1.1);
        -ms-transform: scaleY(1.1);
        transform: scaleY(1.1);
    }
    100% {
        -webkit-transform: scaleY(1);
        -ms-transform: scaleY(1);
        transform: scaleY(1);
    }
    }
              
</code></pre>
        </div>
        <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">
jQuery(function($) {
    $(window).on('scroll', function() {
        if ($(this).scrollTop() >= 200) {
            $('.navbar').addClass('fixed-top');
        } else if ($(this).scrollTop() == 0) {
            $('.navbar').removeClass('fixed-top');
        }
    });
    
    function adjustNav() {
        var winWidth = $(window).width(),
            dropdown = $('.dropdown'),
            dropdownMenu = $('.dropdown-menu');
        
        if (winWidth >= 768) {
            dropdown.on('mouseenter', function() {
                $(this).addClass('show')
                    .children(dropdownMenu).addClass('show');
            });
            
            dropdown.on('mouseleave', function() {
                $(this).removeClass('show')
                    .children(dropdownMenu).removeClass('show');
            });
        } else {
            dropdown.off('mouseenter mouseleave');
        }
    }
    
    $(window).on('resize', adjustNav);
    
    adjustNav();
});
            </code></pre>
        </div>
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
