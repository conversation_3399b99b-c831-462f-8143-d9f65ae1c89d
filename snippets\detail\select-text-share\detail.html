
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Select text and share</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Select text and share</h4>
              <div class="mb-2">
                <a
                  href="./downloads/select-text-share.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <!-- <li><a href="#tab03">CSS</a></li> -->
                <!-- <li><a href="#tab04">Javascript</a></li> -->
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/select-text-share/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
<pre class="line-numbers">
<code class="language-markup">
    &lt;div id="article-cover"&gt;
    &lt;div id="app-title"&gt;Select and share&lt;/div&gt;
    &lt;div id="article-content"&gt;
      &lt;article id="article"&gt;
        &lt;header&gt;The 25 Most Read Articles&lt;/header&gt;
        &lt;section&gt;
          &lt;h1&gt;Heading 1&lt;/h1&gt;
          &lt;p&gt;&lt;b&gt;11&lt;/b&gt; Lorem, ipsum dolor sit amet consectetur adipisicing elit. Perspiciatis, labore.&lt;/p&gt;
        &lt;/section&gt;
        &lt;section&gt;
          &lt;h1&gt;Heading 2&lt;/h1&gt;
          &lt;p&gt;&lt;b&gt;1&lt;/b&gt; Lorem, ipsum dolor sit amet consectetur adipisicing elit. Culpa autem suscipit ad amet rem non quae inventore aspernatur aliquam maiores corporis quam vero reiciendis et esse eum voluptate, incidunt illum, ullam aperiam repudiandae architecto, labore modi nostrum? Commodi repudiandae accusantium doloremque, consequatur, temporibus reprehenderit explicabo numquam, ratione a soluta illum!&lt;/p&gt;
        &lt;/section&gt;
        &lt;section&gt;
          &lt;h1&gt;Heading 3&lt;/h1&gt;
          &lt;p&gt;&lt;b&gt;4&lt;/b&gt; Lorem ipsum dolor sit amet consectetur adipisicing elit. Modi totam accusamus doloremque soluta alias blanditiis quasi, veritatis recusandae, deleniti voluptatum amet iure illum. Repellendus voluptate pariatur, quam consequuntur doloribus laudantium labore reiciendis, a quas adipisci ad ipsa quae beatae exercitationem, in explicabo at nisi distinctio! Nemo, non maiores. Eaque sit recusandae unde sint eos, possimus perferendis vitae asperiores impedit. Facere?&lt;/p&gt;
        &lt;/section&gt;
        &lt;section&gt;
          &lt;h1&gt;Heading 4&lt;/h1&gt;
          &lt;p&gt;&lt;b&gt;13&lt;/b&gt; Lorem ipsum dolor sit amet consectetur adipisicing elit. Totam esse cupiditate assumenda voluptatibus harum porro.&lt;/p&gt;
        &lt;/section&gt;
    &lt;/article&gt;
    
    &lt;/div&gt;
  &lt;/div&gt;
  &lt;div id="share-box-cover"&gt;
    &lt;div id="share-box"&gt;
      &lt;div id="share-links"&gt;
        &lt;a class="share-btn" target="_blank" id="share-twitter" title="Share on Twitter" tabindex="0"&gt;&lt;i class="fab fa-twitter"&gt;&lt;/i&gt;&lt;/a&gt;
        &lt;a class="share-btn" target="_blank" id="share-whatsapp" title="Share on WhatsApp"&gt;&lt;i class="fab fa-whatsapp"&gt;&lt;/i&gt;&lt;/a&gt;
        &lt;a class="share-btn" target="_blank" id="share-telegram" title="Share on Telegram"&gt;&lt;i class="fab fa-telegram"&gt;&lt;/i&gt;&lt;/a&gt;
        &lt;a class="share-btn" id="share-email" title="Share via Email"&gt;&lt;i class="fas fa-envelope"&gt;&lt;/i&gt;&lt;/a&gt;
      &lt;/div&gt;
      &lt;div id="selected-text" class="telegram"&gt;Lorem ipsum dolor sit amet consectetur adipisicing elit. Laborum, voluptates nisi. Perferendis tenetur vel nemo consequuntur ab repellat consectetur veniam, tempora officiis a distinctio cupiditate.&lt;/div&gt;
    &lt;/div&gt;
    &lt;div id="close-share-box"&gt;&lt;i class="fas fa-times"&gt;&lt;/i&gt;&lt;/div&gt;
  &lt;/div&gt;    
</code>  
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">
* {
outline: none;
}

html,
body {
height: 100%;
}

body {
margin: 0;
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
font-family: sans-serif, Arial, Helvetica, sans-serif;
background-color: #f9f9f9;
}

body.ovh {
overflow: hidden;
}

#article-cover {
position: relative;
max-width: 800px;
margin: 100px auto;
background-color: #fff;
}

#article-cover.faded {
filter: blur(5px);
}

#app-title {
position: absolute;
top: -15px;
font-size: 14px;
background: #007266;
color: #fff;
padding: 6px 9px;
left: 60px;
}

#article-content {
padding: 60px;
}

article header {
font-size: 30px;
text-align: center;
font-weight: bold;
color: #272726;
margin-bottom: 50px;
}

article h1 {
margin: 0 0 20px 0;
display: inline-block;
color: #007266;
padding: 10px 0;
border-bottom: 1px solid #fee7e5;
}

article p {
font-size: 17px;
line-height: 32px;
margin: 0 0 40px 0;
color: #272726;
}

article p b {
color: #7ccc4e;
margin-right: 5px;
}



#share-box-cover {
position: fixed;
top: 0;
right: 0;
bottom: 0;
left: 0;
z-index: 1;
display: none;
}

#share-box-cover.active {
display: block;
}

#share-box-cover:before {
content: "";
position: absolute;
top: 0;
right: 0;
bottom: 0;
left: 0;
background-color: #fff;
opacity: 0.7;
z-index: 1;
}

#share-box {
position: absolute;
top: 50%;
right: 0;
left: 0;
max-width: 600px;
height: 240px;
margin: 0 auto;
transform: translateY(-50%);
border-radius: 2px;
overflow: hidden;
box-shadow: 0 10px 20px -3px #bdbdbd;
z-index: 2;
}

#share-links {
position: absolute;
width: 60px;
}

.share-btn {
display: block;
width: 40px;
height: 40px;
background-color: #007266;
padding: 10px;
cursor: pointer;
text-decoration: none;
}

.share-btn i {
font-size: 30px;
color: #fff;
display: block;
text-align: center;
position: relative;
top: 50%;
transform: translateY(-50%);
}

#share-twitter i,
#share-email i {
font-size: 26px;
}

#selected-text {
margin-left: 60px;
padding: 20px;
color: #fff;
font-size: 14px;
line-height: 24px;
overflow-y: auto;
height: 200px;
background: #343434;
}

#close-share-box {
position: fixed;
top: 0;
right: 0;
padding: 10px;
cursor: pointer;
color: #929292;
z-index: 2;
font-size: 20px;
line-height: 1;
}
              </code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">
$(function () {
    var body = $("body"),
        articleCover = $("#article-cover"),
        shareBox = $("#share-box-cover"),
        shareBtn = $(".share-btn"),
        selectedTextBox = $("#selected-text"),
        closeShareBoxBtn = $("#close-share-box"),
        twitterShareBtn = $("#share-twitter"),
        whatsappSharBtn = $("#share-whatsapp"),
        telegramShareBtn = $("#share-telegram"),
        emailShareBtn = $("#share-email");
    
    function resetMe() {
        articleCover.removeClass("faded");
        shareBox.removeClass("active");
        body.removeClass("ovh");
        shareBtn.removeAttr("href");
        selectedTextBox.text("");
    }
    
    function makeLinks(text) {
        text = encodeURIComponent(text);
        url = window.location.href;
    
        twitterShareBtn.attr(
        "href",
        "https://twitter.com/intent/tweet?text=" + text
        );
        whatsappSharBtn.attr("href", "https://api.whatsapp.com/send?text=" + text);
        telegramShareBtn.attr(
        "href",
        "https://telegram.me/share/url?url=" + url + "&text=" + text
        );
        emailShareBtn.attr("href", "mailto:?body=" + text);
    }
    
    function getSelectionText() {
        var text = "";
        if (window.getSelection) {
        text = window.getSelection().toString();
        } else if (document.selection && document.selection.type != "Control") {
        text = document.selection.createRange().text;
        }
    
        if (text.trim().length > 0) {
        articleCover.addClass("faded");
        shareBox.addClass("active");
        body.addClass("ovh");
        selectedTextBox.text(text);
        makeLinks(text);
        } else resetMe();
    }
    
    $("#article").on("mouseup", getSelectionText);
        closeShareBoxBtn.on("click", resetMe);
        });
            </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
