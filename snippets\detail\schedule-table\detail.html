
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}


    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
          <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
          <i class="fas fa-bars"></i>
        </span>
      </div>

<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
        <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
                <div>
                    <h4 class="size20 mb-2 pt-2">Schedule Table</h4>                   
                </div>
              </div>   
        </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/schedule-table/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
   
&lt;link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css" rel="stylesheet"&gt;

&lt;div class="timeTable"&gt;
    &lt;div class="schedule content-block"&gt;
        &lt;div class="container"&gt;
            &lt;h2 data-aos="zoom-in-up" class="aos-init aos-animate"&gt;Schedule Table&lt;/h2&gt;
        
            &lt;div class="timetable"&gt;
        
                &lt;!-- Schedule Top Navigation --&gt;
                &lt;nav class="nav nav-tabs"&gt;
                &lt;a class="nav-link active"&gt;Mon&lt;/a&gt;
                &lt;a class="nav-link"&gt;Tue&lt;/a&gt;
                &lt;a class="nav-link"&gt;Wed&lt;/a&gt;
                &lt;a class="nav-link"&gt;Thu&lt;/a&gt;
                &lt;a class="nav-link"&gt;Fri&lt;/a&gt;
                &lt;a class="nav-link"&gt;Sat&lt;/a&gt;
                &lt;a class="nav-link"&gt;Sun&lt;/a&gt;
                &lt;/nav&gt;
        
                &lt;div class="tab-content"&gt;
                &lt;div class="tab-pane show active"&gt;
                    &lt;div class="row"&gt;
        
                    &lt;!-- Schedule Item 1 --&gt;
                    &lt;div class="col-md-6"&gt;
                        &lt;div class="timetable-item"&gt;
                        &lt;div class="timetable-item-img"&gt;
                            &lt;img src="https://picsum.photos/100/80?random=5" alt="Office Meeting"&gt;
                        &lt;/div&gt;
                        &lt;div class="timetable-item-main"&gt;
                            &lt;div class="timetable-item-time"&gt;4:00pm - 5:00pm&lt;/div&gt;
                            &lt;div class="timetable-item-name"&gt;Office Meeting&lt;/div&gt;
                            &lt;a href="#" class="btn btn-primary btn-book"&gt;Book&lt;/a&gt;
                            &lt;div class="timetable-item-like"&gt;
                            &lt;i class="fa fa-heart-o" aria-hidden="true"&gt;&lt;/i&gt;
                            &lt;i class="fa fa-heart" aria-hidden="true"&gt;&lt;/i&gt;
                            &lt;div class="timetable-item-like-count"&gt;11&lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
        
                    &lt;!-- Schedule Item 2 --&gt;
                    &lt;div class="col-md-6"&gt;
                        &lt;div class="timetable-item"&gt;
                        &lt;div class="timetable-item-img"&gt;
                            &lt;img src="https://picsum.photos/100/80?random=6" alt="Office Party"&gt;
                        &lt;/div&gt;
                        &lt;div class="timetable-item-main"&gt;
                            &lt;div class="timetable-item-time"&gt;5:00pm - 6:00pm&lt;/div&gt;
                            &lt;div class="timetable-item-name"&gt;Office Party&lt;/div&gt;
                            &lt;a href="#" class="btn btn-primary btn-book"&gt;Book&lt;/a&gt;
                            &lt;div class="timetable-item-like"&gt;
                            &lt;i class="fa fa-heart-o" aria-hidden="true"&gt;&lt;/i&gt;
                            &lt;i class="fa fa-heart" aria-hidden="true"&gt;&lt;/i&gt;
                            &lt;div class="timetable-item-like-count"&gt;28&lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
        
                    &lt;!-- Schedule Item 3 --&gt;
                    &lt;div class="col-md-6"&gt;
                        &lt;div class="timetable-item"&gt;
                        &lt;div class="timetable-item-img"&gt;
                            &lt;img src="https://picsum.photos/100/80?random=1" alt="Self Training"&gt;
                        &lt;/div&gt;
                        &lt;div class="timetable-item-main"&gt;
                            &lt;div class="timetable-item-time"&gt;5:00pm - 6:00pm&lt;/div&gt;
                            &lt;div class="timetable-item-name"&gt;Self Training&lt;/div&gt;
                            &lt;a href="#" class="btn btn-primary btn-book"&gt;Book&lt;/a&gt;
                            &lt;div class="timetable-item-like"&gt;
                            &lt;i class="fa fa-heart-o" aria-hidden="true"&gt;&lt;/i&gt;
                            &lt;i class="fa fa-heart" aria-hidden="true"&gt;&lt;/i&gt;
                            &lt;div class="timetable-item-like-count"&gt;28&lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
        
                    &lt;!-- Schedule Item 4 --&gt;
                    &lt;div class="col-md-6"&gt;
                        &lt;div class="timetable-item"&gt;
                        &lt;div class="timetable-item-img"&gt;
                            &lt;img src="https://picsum.photos/100/80?random=2" alt="Other Activites"&gt;
                        &lt;/div&gt;
                        &lt;div class="timetable-item-main"&gt;
                            &lt;div class="timetable-item-time"&gt;7:00pm - 8:00pm&lt;/div&gt;
                            &lt;div class="timetable-item-name"&gt;Other Activites&lt;/div&gt;
                            &lt;a href="#" class="btn btn-primary btn-book"&gt;Book&lt;/a&gt;
                            &lt;div class="timetable-item-like"&gt;
                            &lt;i class="fa fa-heart-o" aria-hidden="true"&gt;&lt;/i&gt;
                            &lt;i class="fa fa-heart" aria-hidden="true"&gt;&lt;/i&gt;
                            &lt;div class="timetable-item-like-count"&gt;23&lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
        
                    &lt;!-- Schedule Item 5 --&gt;
                    &lt;div class="col-md-6"&gt;
                        &lt;div class="timetable-item"&gt;
                        &lt;div class="timetable-item-img"&gt;
                            &lt;img src="https://picsum.photos/100/80?random=3" alt="Exercise"&gt;
                        &lt;/div&gt;
                        &lt;div class="timetable-item-main"&gt;
                            &lt;div class="timetable-item-time"&gt;6:00pm - 7:00pm&lt;/div&gt;
                            &lt;div class="timetable-item-name"&gt;Exercise&lt;/div&gt;
                            &lt;a href="#" class="btn btn-primary btn-book"&gt;Book&lt;/a&gt;
                            &lt;div class="timetable-item-like"&gt;
                            &lt;i class="fa fa-heart-o" aria-hidden="true"&gt;&lt;/i&gt;
                            &lt;i class="fa fa-heart" aria-hidden="true"&gt;&lt;/i&gt;
                            &lt;div class="timetable-item-like-count"&gt;14&lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
        
                    &lt;!-- Schedule Item 6 --&gt;
                    &lt;div class="col-md-6"&gt;
                        &lt;div class="timetable-item"&gt;
                        &lt;div class="timetable-item-img"&gt;
                            &lt;img src="https://picsum.photos/100/80?random=4" alt="Self Training"&gt;
                        &lt;/div&gt;
                        &lt;div class="timetable-item-main"&gt;
                            &lt;div class="timetable-item-time"&gt;8:00pm - 9:00pm&lt;/div&gt;
                            &lt;div class="timetable-item-name"&gt;Self Training&lt;/div&gt;
                            &lt;a href="#" class="btn btn-primary btn-book"&gt;Book&lt;/a&gt;
                            &lt;div class="timetable-item-like"&gt;
                            &lt;i class="fa fa-heart-o" aria-hidden="true"&gt;&lt;/i&gt;
                            &lt;i class="fa fa-heart" aria-hidden="true"&gt;&lt;/i&gt;
                            &lt;div class="timetable-item-like-count"&gt;9&lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt; 
</code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
body{margin-top:20px;}
.timeTable .classes-details ul.timetable {
    margin: 0 0 22px;
    padding: 0;
}
.timeTable .classes-details ul.timetable li {
    list-style: none;
    font-size: 15px;
    color: #7f7f7f;
}
timeTable .timetable {
    max-width: 900px;
    margin: 0 auto;
}
.timeTable .timetable-item {
    border: 1px solid #d8e3eb;
    padding: 15px;
    margin-top: 20px;
    position: relative;
    display: block;
}
@media (min-width: 768px) {
    .timeTable .timetable-item {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
    }
}
.timeTable .timetable-item-img {
    overflow: hidden;
    height: 100px;
    width: 100px;
    display: none;
}
.timeTable .timetable-item-img img {
    height: 100%;
}
@media (min-width: 768px) {
    .timeTable .timetable-item-img {
        display: block;
    }
}
.timeTable .timetable-item-main {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    position: relative;
    margin-top: 12px;
}
@media (min-width: 768px) {
    .timeTable .timetable-item-main {
        margin-top: 0;
        padding-left: 15px;
    }
}
.timeTable .timetable-item-time {
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 4px;
}
.timeTable .timetable-item-name {
    font-size: 14px;
    margin-bottom: 19px;
}
.timeTable .btn-book {
    padding: 6px 30px;
    width: 100%;
}
.timeTable .timetable-item-like {
    position: absolute;
    top: 3px;
    right: 3px;
    font-size: 20px;
    cursor: pointer;
}
.timeTable .timetable-item-like .fa-heart-o {
    display: block;
    color: #b7b7b7;
}
.timeTable .timetable-item-like .fa-heart {
    display: none;
    color: #f15465;
}
.timeTable .timetable-item-like:hover .fa-heart {
    display: block;
}
.timeTable .timetable-item-like:hover .fa-heart-o {
    display: none;
}
.timeTable .timetable-item-like-count {
    font-size: 12px;
    text-align: center;
    padding-top: 5px;
    color: #7f7f7f;
}
.timeTable .timetable-item-book {
    width: 200px;
}
.timeTable .timetable-item-book .btn {
    width: 100%;
}
.timeTable .schedule .nav-tabs {
    border-bottom: 2px solid #104455;
}
.timeTable .schedule .nav-link {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    font-size: 12px;
    text-align: center;
    text-transform: uppercase;
    color: #3d3d3d;
    font-weight: 500;
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
    border-radius: 2px 2px 0 0;
    padding-left: 0;
    padding-right: 0;
    cursor: pointer;
    border-left: none;
    border-right: none;
}
@media (min-width: 768px) {
    .timeTable .schedule .nav-link {
        font-size: 16px;
    }
}
.timeTable .schedule .nav-link.active {
    background: #104455;
    border-color: #104455;
    color: #fff;
}
.timeTable .schedule .nav-link.active:focus {
    border-color: #104455;
}
.timeTable .schedule .nav-link:hover:not(.active) {
    background: #46c1be;
    border-color: #46c1be;
    color: #fff;
}
.timeTable .schedule .tab-pane {
    padding-top: 10px;
}
</code></pre>
        </div>
        <!-- <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">

            </code></pre>
        </div> -->
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
