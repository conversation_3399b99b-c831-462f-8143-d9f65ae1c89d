<script src="header.js"></script>

<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 66px;
    }
    


    </style>
  </head>
  <body>
    <header>

<script src="../../../header.js"></script>
<div class="hamSection">
    <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
      <i class="fas fa-bars"></i>
    </span>
    <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
      <i class="fas fa-bars"></i>
    </span>
  </div>

<main role="main">

  

  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
        <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
                <div>
                    <h4 class="size20 mb-2 pt-2">Booking Request</h4>                   
                </div>
              </div>   
        </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/booking-request/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
&lt;div class="container"&gt;
&lt;div class="row"&gt;
    &lt;div class="col-md-12"&gt;
        &lt;div class="card card-white mb-5"&gt;
            &lt;div class="card-heading clearfix border-bottom mb-4"&gt;
                &lt;h4 class="card-title"&gt;Booking Requests&lt;/h4&gt;
            &lt;/div&gt;
            &lt;div class="card-body"&gt;
                &lt;ul class="list-unstyled"&gt;
                    &lt;li class="position-relative booking"&gt;
                        &lt;div class="media"&gt;
                            &lt;div class="msg-img"&gt;
                                &lt;img src="https://picsum.photos/250/250?random=1" alt=""&gt;
                            &lt;/div&gt;
                            &lt;div class="media-body"&gt;
                                &lt;h5 class="mb-4"&gt;Sunny Apartment &lt;span class="badge badge-primary mx-3"&gt;Pending&lt;/span&gt;&lt;span class="badge badge-danger"&gt;Unpaid&lt;/span&gt;&lt;/h5&gt;
                                &lt;div class="mb-3"&gt;
                                    &lt;span class="mr-2 d-block d-sm-inline-block mb-2 mb-sm-0"&gt;Booking Date:&lt;/span&gt;
                                    &lt;span class="bg-light-blue"&gt;02.03.2020 - 04.03.2020&lt;/span&gt;
                                &lt;/div&gt;
                                &lt;div class="mb-3"&gt;
                                    &lt;span class="mr-2 d-block d-sm-inline-block mb-2 mb-sm-0"&gt;Booking Details:&lt;/span&gt;
                                    &lt;span class="bg-light-blue"&gt;2 Adults&lt;/span&gt;
                                &lt;/div&gt;
                                &lt;div class="mb-3"&gt;
                                    &lt;span class="mr-2 d-block d-sm-inline-block mb-2 mb-sm-0"&gt;Price:&lt;/span&gt;
                                    &lt;span class="bg-light-blue"&gt;$147&lt;/span&gt;
                                &lt;/div&gt;
                                &lt;div class="mb-5"&gt;
                                    &lt;span class="mr-2 d-block d-sm-inline-block mb-1 mb-sm-0"&gt;Clients:&lt;/span&gt;
                                    &lt;span class="border-right pr-2 mr-2"&gt;Sneha Issar&lt;/span&gt;
                                    &lt;span class="border-right pr-2 mr-2"&gt; <EMAIL>&lt;/span&gt;
                                    &lt;span&gt;123-563-789&lt;/span&gt;
                                &lt;/div&gt;
                                &lt;a href="#" class="btn-gray"&gt;Send Message&lt;/a&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="buttons-to-right"&gt;
                            &lt;a href="#" class="btn-gray mr-2"&gt;&lt;i class="far fa-times-circle mr-2"&gt;&lt;/i&gt; Reject&lt;/a&gt;
                            &lt;a href="#" class="btn-gray"&gt;&lt;i class="far fa-check-circle mr-2"&gt;&lt;/i&gt; Approve&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/li&gt;

                    &lt;li class="position-relative booking"&gt;
                        &lt;div class="media"&gt;
                            &lt;div class="msg-img"&gt;
                                &lt;img src="https://picsum.photos/250/250?random=2" alt=""&gt;
                            &lt;/div&gt;
                            &lt;div class="media-body"&gt;
                                &lt;h5 class="mb-4"&gt;Burger House &lt;span class="badge badge-success ml-3"&gt;Approved&lt;/span&gt;&lt;/h5&gt;
                                &lt;div class="mb-3"&gt;
                                    &lt;span class="mr-2 d-block d-sm-inline-block mb-2 mb-sm-0"&gt;Booking Date:&lt;/span&gt;
                                    &lt;span class="bg-light-green"&gt;06.03.2020 - 07.03.2020&lt;/span&gt;
                                &lt;/div&gt;
                                &lt;div class="mb-3"&gt;
                                    &lt;span class="mr-2 d-block d-sm-inline-block mb-2 mb-sm-0"&gt;Booking Details:&lt;/span&gt;
                                    &lt;span class="bg-light-green"&gt;2 Adults, 2 Children&lt;/span&gt;
                                &lt;/div&gt;

                                &lt;div class="mb-5"&gt;
                                    &lt;span class="mr-2 d-block d-sm-inline-block mb-1 mb-sm-0"&gt;Clients:&lt;/span&gt;
                                    &lt;span class="border-right pr-2 mr-2"&gt;Vimal Thapliyal&lt;/span&gt;
                                    &lt;span class="border-right pr-2 mr-2"&gt; <EMAIL>&lt;/span&gt;
                                    &lt;span&gt;355-456-789&lt;/span&gt;
                                &lt;/div&gt;
                                &lt;a href="#" class="btn-gray"&gt;Send Message&lt;/a&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="buttons-to-right"&gt;
                            &lt;a href="#" class="btn-gray mr-2"&gt;&lt;i class="far fa-times-circle mr-2"&gt;&lt;/i&gt;Canceled&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/li&gt;

                    &lt;li class="position-relative booking"&gt;
                        &lt;div class="media"&gt;
                            &lt;div class="msg-img"&gt;
                                &lt;img src="https://picsum.photos/250/250?random=3" alt=""&gt;
                            &lt;/div&gt;
                            &lt;div class="media-body"&gt;
                                &lt;h5 class="mb-4"&gt;Modern Hotel &lt;span class="badge badge-danger ml-3"&gt;Canceled&lt;/span&gt;&lt;/h5&gt;
                                &lt;div class="mb-3"&gt;
                                    &lt;span class="mr-2 d-block d-sm-inline-block mb-2 mb-sm-0"&gt;Booking Date:&lt;/span&gt;
                                    &lt;span class="btn-gray"&gt;20.03.2020 - 24.03.2020&lt;/span&gt;
                                &lt;/div&gt;
                                &lt;div class="mb-3"&gt;
                                    &lt;span class="mr-2 d-block d-sm-inline-block mb-2 mb-sm-0"&gt;Booking Details:&lt;/span&gt;
                                    &lt;span class="btn-gray"&gt;2 Adults&lt;/span&gt;
                                &lt;/div&gt;
                                &lt;div&gt;
                                    &lt;span class="mr-2 d-block d-sm-inline-block mb-1 mb-sm-0"&gt;Clients:&lt;/span&gt;
                                    &lt;span class="border-right pr-2 mr-2"&gt;Pankaj Sharma&lt;/span&gt;
                                    &lt;span class="border-right pr-2 mr-2"&gt; <EMAIL>&lt;/span&gt;
                                    &lt;span&gt;123-456-684&lt;/span&gt;
                                &lt;/div&gt;

                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/li&gt;
                &lt;/ul&gt;

            &lt;/div&gt;
        &lt;/div&gt;

    &lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
   </code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
body{
    background: #f6f9fc;
    margin-top:20px;}
/* booking */

.bg-light-blue {
    background-color: #e9f7fe !important;
    color: #3184ae;
    padding: 7px 18px;
    border-radius: 4px;
}

.bg-light-green {
    background-color: rgba(40, 167, 69, 0.2) !important;
    padding: 7px 18px;
    border-radius: 4px;
    color: #28a745 !important;
}

.buttons-to-right {
    position: absolute;
    right: 0;
    top: 40%;
}

.btn-gray {
    color: #666;
    background-color: #eee;
    padding: 7px 18px;
    border-radius: 4px;
}

.booking:hover .buttons-to-right .btn-gray {
    opacity: 1;
    transition: .3s;
}

.buttons-to-right .btn-gray {
    opacity: 0;
    transition: .3s;
}

.btn-gray:hover {
    background-color: #36a3f5;
    color: #fff;
}

.booking {
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 30px;
}

.booking:last-child {
    margin-bottom: 0px;
    border-bottom: none;
    padding-bottom: 0px;
}

@media screen and (max-width: 575px) {
    .buttons-to-right {
        top: 10%;
    }
    .buttons-to-right a {
        display: block;
        margin-bottom: 20px;
    }
    .buttons-to-right a:last-child {
        margin-bottom: 0px;
    }
    .bg-light-blue,
    .bg-light-green,
    .btn-gray {
        padding: 7px;
    }
}

.card {
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    box-shadow: none;
    border: none;
    padding: 25px;
}
.mb-5, .my-5 {
    margin-bottom: 3rem!important;
}
.msg-img {
    margin-right: 20px;
}
.msg-img img {
    width: 60px;
    border-radius: 50%;
}
img {
    max-width: 100%;
    height: auto;
}
</code></pre>
        </div>
        <!-- <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">

            </code></pre>
        </div> -->
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
