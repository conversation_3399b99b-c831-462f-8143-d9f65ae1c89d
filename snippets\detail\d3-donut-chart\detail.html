<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
          <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
          <i class="fas fa-bars"></i>
        </span>
      </div>
<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
        <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
                <div>
                    <h4 class="size20 mb-2 pt-2">D3 animated colorful donut chart visualization.</h4>                   
                </div>
                <div class="mb-2">
                    <a
                      href="./downloads/d3-donut-chart.zip"
                      class="btn btn-sm btn-outline-primary btn-dark mr-2"
                      ><i class="fas fa-download"></i> Download zip</a
                    >
                    <button class="btn btn-sm btn-outline-info" id="fullScreen">
                      <i class="fas fa-expand"></i> View on Fullscreen
                    </button>
                  </div>  
              </div>   
        </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/d3-donut-chart/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">&lt;main&gt;
    &lt;nav&gt;
        &lt;h2&gt;Donut Chart&lt;/h2&gt;
    &lt;/nav&gt;

    &lt;section&gt;
        &lt;h1&gt;Funding Structure&lt;/h1&gt;
        &lt;svg viewBox="0 0 400 250"&gt;
        &lt;/svg&gt;
    &lt;/section&gt;
&lt;/main&gt;

</code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
            @import url("https://fonts.googleapis.com/css?family=Fira+Sans:600,800");
    
    * {
      box-sizing: border-box;
      padding: 0;
      margin: 0;
    }
    body {
      min-height: 100vh;
      color: #434248;
      background: linear-gradient(to bottom, #002035 , #00b19c);
      font-family: "Fira Sans", sans-serif;
    }
    
    /* horizontally center the container making up the monitor */
    main {
      background: #ffffff;
      width: 80vw;
      max-width: 750px;
      margin: 1.5rem auto;
      box-shadow: 0 2px 15px hsla(0, 0%, 0%, 0.2);
      border-radius: 0;
    }
    nav {
      text-align: center;
      background: hsla(0, 0%, 0%, 0.015);
      padding: 0.75rem 1rem;
      /* position relative for the pseudo element */
      position: relative;
    }
    nav h2 {
      font-weight: 600;
      letter-spacing: -0.05rem;
      font-size: 0.9rem;
    }
    
    /* display the heading and svg in a centered column */
    section {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 3rem 1rem;
    }
    section h1 {
      font-weight: 800;
      font-size: 1.8rem;
    }
    /* have the svg occupy a sizeable portion of the wrapping container */
    section svg {
      margin-top: 2.5rem;
      width: 90%;
      height: auto;
    }</code></pre>
        </div>
        <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">const data = [
                {
                  name: 'Public Sale',
                  percentage: 20, // percentage
                  value: 80, // millions
                  color: '#00b19c',
                },
                {
                  name: 'Reserved',
                  percentage: 35,
                  value: 140,
                  color: '#3bcd3f',
                },
                {
                  name: 'Advisors',
                  percentage: 10,
                  value: 40,
                  color: '#007365',
                },
                {
                  name: 'Foundation',
                  percentage: 10,
                  value: 40,
                  color: '#8dbac4',
                },
                {
                  name: 'Option pool',
                  percentage: 12.5,
                  value: 50,
                  color: '#002035',
                },
                {
                  name: 'Team Tokens',
                  percentage: 12.5,
                  value: 50,
                  color: '#33c1b0',
                },
              ];
              
              // retrieve the svg in which to plot the viz
              const svg = d3
                .select('svg');
              
              // identify the dimensions of the viewBox to establish the svg canvas
              const viewBox = svg.attr('viewBox');
              const regexViewBox = /\d+ \d+ (\d+) (\d+)/;
              // ! .match() returns string values
              const [, viewBoxWidth, viewBoxHeight] = viewBox.match(regexViewBox).map(item => Number.parseInt(item, 10));
              
              // with the margin convention include a group element translated within the svg canvas
              const margin = {
                top: 20,
                right: 20,
                bottom: 20,
                left: 20,
              };
              // compute the width and height of the actual viz from the viewBox dimensions and considering the margins
              // this to later work with width and height attributes directly through the width and height variables
              const width = viewBoxWidth - (margin.left + margin.right);
              const height = viewBoxHeight - (margin.top + margin.bottom);
              
              // compute the radius as half the minor size between the width and height
              const radius = Math.min(width, height) / 2;
              // initialize a variable to have the multiple elements share the same stroke-width property
              const strokeWidth = 10;
              
              const group = svg
                .append('g')
                .attr('transform', `translate(${margin.left} ${margin.top})`);
              
              
              // DEFAULT CIRCLE
              // circle used as a background for the colored donut chart
              // add a group to center the circle in the canvas (this to rotate the circle from the center)
              const groupDefault = group
                .append('g')
                .attr('transform', `translate(${width / 2} ${height / 2})`);
              
              // append the circle showing only the stroke
              groupDefault
                .append('circle')
                .attr('cx', 0)
                .attr('cy', 0)
                .attr('r', radius)
                .attr('transform', 'rotate(-90)')
                .attr('fill', 'none')
                .attr('stroke', 'hsla(0, 0%, 0%, 0.08')
                .attr('stroke-width', strokeWidth)
                .attr('stroke-linecap', 'round')
                // hide the stroke of the circle using the radius
                // this to compute the circumference of the shape
                .attr('stroke-dasharray', radius * 3.14 * 2)
                .attr('stroke-dashoffset', radius * 3.14 * 2);
              
              
              // COLORED CIRCLES
              // pie function to compute the arcs
              const pie = d3
                .pie()
                .sort(null)
                .padAngle(0.12)
                // use either the value or the percentage in the dataset
                .value(d => d.value);
              
              // arc function to create the d attributes for the path elements
              const arc = d3
                .arc()
                // have the arc overlaid on top of the stroke of the circle
                .innerRadius(radius)
                .outerRadius(radius);
              
              /* for each data point include the following structure
              g             // wrapping all arcs
                g           // wrapping each arc
                  arc       // actual shape
                  line      // connecting line
                  text      // text label
                g
                  arc
                  ...
              */
              // wrapping group, horizontally centered
              const groupArcs = group
                .append('g')
                .attr('transform', `translate(${width / 2} ${height / 2})`);
              
              const groupsArcs = groupArcs
                .selectAll('g')
                .data(pie(data))
                .enter()
                .append('g');
              
              // include the arcs specifying the stroke with the same width of the circle element
              groupsArcs
                .append('path')
                .attr('d', arc)
                .attr('fill', 'none')
                .attr('stroke', d => d.data.color)
                .attr('stroke-width', strokeWidth * 0.8)
                .attr('stroke-linecap', 'round')
                .attr('stroke-linejoin', 'round')
                // hide the segments by applying a stroke-dasharray/stroke-dashoffset equal to the circle circumference
                // ! the length of the element varies, and it considered afterwords
                // for certain the paths are less than the circumference of the entire circle
                .attr('stroke-dasharray', radius * 3.14 * 2)
                .attr('stroke-dashoffset', radius * 3.14 * 2);
              
              // include line elements visually connecting the text labels with the arcs
              groupsArcs
                .append('line')
                .attr('x1', 0)
                .attr('x2', (d) => {
                  const [x] = arc.centroid(d);
                  return x > 0 ? '25' : '-25';
                })
                .attr('y1', 0)
                .attr('y2', 0)
                .attr('stroke', ({ data: d }) => d.color)
                .attr('stroke-width', 1.5)
                .attr('transform', (d) => {
                  const [x, y] = arc.centroid(d);
                  const offset = x > 0 ? 20 : -20;
                  return `translate(${x + offset} ${y})`;
                })
                .attr('stroke-dasharray', 25)
                .attr('stroke-dashoffset', 25);
              
              // include text elements associated with the arcs
              groupsArcs
                .append('text')
                .attr('x', 0)
                .attr('y', 0)
                .attr('font-size', 8)
                .attr('text-anchor', (d) => {
                  const [x] = arc.centroid(d);
                  return x > 0 ? 'start' : 'end';
                })
                .attr('transform', (d) => {
                  const [x, y] = arc.centroid(d);
                  const offset = x > 0 ? 50 : -50;
                  return `translate(${x + offset} ${y})`;
                })
                .html(({ data: d }) => `
                  <tspan x="0">${d.name}:</tspan><tspan x="0" dy="10" font-size="6">${d.percentage}% / ${d.value}M</tspan>
                `)
                .style('opacity', 0)
                .style('visibility', 'hidden');
              
              
              // TRANSITIONS
              // once the elements are set up
              // draw the stroke of the larger circle element
              groupDefault
                .select('circle')
                .transition()
                .ease(d3.easeExp)
                .delay(200)
                .duration(2000)
                .attr('stroke-dashoffset', '0')
                // once the transition is complete
                // draw the smaller strokes one after the other
                .on('end', () => {
                  // immediately set the stroke-dasharray and stroke-dashoffset properties to match the length of the path elements
                  // using vanilla JavaScript
                  const paths = document.querySelectorAll('svg g g path');
                  paths.forEach((path) => {
                    const length = path.getTotalLength();
                    path.setAttribute('stroke-dasharray', length);
                    path.setAttribute('stroke-dashoffset', length);
                  });
              
                  const duration = 1000;
                  // transition the path elements to stroke-dashoffset 0
                  d3
                    .selectAll('svg g g path')
                    .transition()
                    .ease(d3.easeLinear)
                    .delay((d, i) => i * duration)
                    .duration(duration)
                    .attr('stroke-dashoffset', 0);
              
                  // transition the line elements elements to stroke-dashoffset 0
                  d3
                    .selectAll('svg g g line')
                    .transition()
                    .ease(d3.easeLinear)
                    .delay((d, i) => i * duration + duration / 2.5)
                    .duration(duration / 3)
                    .attr('stroke-dashoffset', 0);
              
                  // transition the text elements to opacity 1 and visibility visible
                  d3
                    .selectAll('svg g g text')
                    .transition()
                    .ease(d3.easeLinear)
                    .delay((d, i) => i * duration + duration / 2)
                    .duration(duration / 2)
                    .style('opacity', 1)
                    .style('visibility', 'visible');
                });</code></pre>
        </div>
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <script src="./js/iframe.js"></script>

      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
