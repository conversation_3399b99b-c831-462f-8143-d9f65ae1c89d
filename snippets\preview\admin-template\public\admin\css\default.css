html{position: relative; min-height: 100%;}
body{font-family: "Fira Sans";}

/*CSS animation*/
.HomeTile, .HomeTile *, .zoomHover, .BoxShadowHover, #tsc_nav_1 .MegaMenu > .dropdown-menu a:after, #tsc_nav_1 .MegaMenu > .dropdown-menu a, #tsc_nav_1 .navbar .nav-link:focus, .navbar .nav-link:hover, .navbar .nav-item:hover, .search .search-input, .tsc-tabs a, .module-search, .CI-Text>p, .CI-icons, .CI-tile:before, .CI-tile:after, .CI-Text, .imgZoom{transition: all ease-in-out 200ms}
/*CSS animation ends*/
.beforeAfter:before, .beforeAfter:after{content: ""; clear: both; display: block; position: absolute}

.themeBorderBottom {border-bottom: 3px solid #00ae9b;background: white; min-height: 40px;}
#tsc_nav_1 .nav-link {
    padding: 0.5rem;
    font-size: 0.75rem;
}
#tsc_nav_1 .nav-link i{font-size: 1rem; margin-bottom: 5px;}
.MegaMenu{position: static}
#tsc_nav_1 .MegaMenu > .dropdown-menu{width: 100%;}
/*#tsc_nav_1 .MegaMenu > .dropdown-menu .col-sm-3:not(:nth-child(4n+0)){border-right: 1px solid #cbebb8}*/
#tsc_nav_1 .MegaMenu > .dropdown-menu a{position:relative;}
#tsc_nav_1 .MegaMenu > .dropdown-menu a:after{content: ""; display: block;  clear: both; width: 0; top: 100%; left: 0; height: 2px; background: #99dfd7}
#tsc_nav_1 .MegaMenu > .dropdown-menu a:hover:after{width: 100%;}
.search{width: 50px; overflow: hidden}
.search .search-input{ border-left: 0;  border-right: 0;  border-top: 0; opacity: 0; width: 0}
.search:hover, .search:focus, .search:active{width: auto}
.search:hover .search-input, .search:focus .search-input, .search:active .search-input{opacity: 1; width: 35%}
#readingSearch.search-input:hover{ opacity: 1;width: 100% !important;}

.rowHT{margin-left: -0.5rem; margin-right: -0.5rem;}
.HomeTile{min-height: 340px; position: relative; overflow: hidden}
.HomeTile>img.img-fluid{position: relative; z-index: 1}
.HomeTile:hover>img.img-fluid, .zoomHover:hover .imgZoom{transform: scale(1.3)}


.SetHome{position: absolute; top: 0; left: 0; background: url("../images/fav-bg.png") top left no-repeat; min-height: 39px; min-width: 136px; z-index: 2}
.SetHome:hover{text-shadow: 0 0 5px rgba(255,255,255,0.6); background-position: -75px 0px}
.TileContent{background:url("../images/TileCut.png") top right no-repeat; position: absolute; z-index: 2; position: absolute; top: 140px; right: 0; min-height: 300px; width: 100%; padding:25% 15px 10px}
.TileIcons{position: absolute; z-index: 2; right: -15px; top: -15px; opacity: 0}
.HomeTile:hover .TileIcons{opacity: 1; right: 15px; top: 0; }

.RL{overflow: hidden; height: 150px;}
.RL-img{position: relative; z-index: 1}
.RL-title{position: absolute; z-index: 2; bottom: 0; left: 0; width: 90%; background: url("../images/ReadListMain-bg.png") bottom right no-repeat;}
.RL-list-img{overflow: hidden; flex: 0 0 17%; max-width: 17%;}

.tsc-tabs{border-bottom: 2px solid #82ce58}
.tsc-tabs a:not(.active){color: #787f88; background: #ecf1f3;box-shadow: 0 -4px 5px rgba(0, 0, 0, 0.07) inset}
.tsc-tabs a:not(:last-child){margin-right: 1px;}
.tsc-tabs a:hover{background:#dae3e6 }
.tsc-tabs.nav-pills .nav-link{border-radius: 0;}
.ms-icon{right: 0; top: 4px;}
.module-search{border: 0 none; border-bottom: 1px solid #c7d4da; min-width: 225px; max-width: 100%}
.module-search:focus, .module-search:active{outline: none; border-color: #787f88}

.TealGreenGradient, .tsc-tabs a.TealGreenGradient/*, .tsc-tabs a:hover*/{background: rgb(0,174,155);background: linear-gradient(165deg, rgba(0,174,155,1) 23%, rgba(116,196,82,1) 100%);}
.zoomHover:hover{transform: scale(1.05); z-index:9;}

.BoxShadowHover:hover{box-shadow: 1px 3px 20px rgba(0,0,0,0.30)}

.CI-tile{max-height: 140px; overflow: hidden; z-index: 1}
.CI-tile:before, .CI-tile:after{position: absolute; bottom: 0; right: 60px; content: ""; clear: both; display: block; background: url("../images/TileCut2.png") top right no-repeat; z-index: 2; width: 100%; height: 140px;}
.CI-tile:after{background: none; width: auto; height: auto; border-bottom: 45px solid #1e2a39; border-left: 0 solid transparent; border-right: 90px solid transparent; right: auto; left: 0; opacity: 0.3}
.CI-Text{top: 0; left: 0; z-index: 3; width: 100%; padding: 40px 85px 15px 15px; }
.CI-icons{top: 5px; right: 15px; z-index: 3; }
.CI-Text>p, .CI-icons{opacity: 0}
.CI-icons>.rounded-circle{width: 35px; height: 35px;}
.CI-tile img.img-fluid{z-index: 1}

.CI-tile:hover{z-index: 2}
.CI-tile:hover .CI-Text>p, .CI-tile:hover .CI-icons{opacity: 1}
.CI-tile:hover:before{background-position: -75px 0; right: 0}
.CI-tile:hover .CI-Text{padding-top: 15px;}

.RaiseRequest{overflow: hidden; max-height: 365px; max-width: 100%}
.RaiseRequest:before{top:0; left: 0; width: 100%; height: 100%; z-index: 2; background: rgba(30,42,57,0.6)}
.RR-img{z-index: 1}
.RR-head{z-index: 3; background: url("../images/ReadListMain-bg.png") right bottom no-repeat; top: 35%; left: 0}
.RR-text{top: 50%; z-index: 3; left: 4rem}

.gr-legend{background: rgba(37,187,62,1);background: linear-gradient(to right, rgba(37,187,62,1) 0%, rgba(251,195,13,1) 50%, rgba(187,53,9,1) 100%); width: 60px; height: 10px;}
.s-icon{width: 12px; height: 12px;}
.f-icon{width: 25px; height: 25px; line-height: 25px;}
.sample-logo{background: url("../images/logo-bg.jpg") center center no-repeat; background-size: cover; height: 150px;}
.sample-round-indicator{left: auto}
.carousel-indicators.sample-round-indicator li{ height: 9px; width: 9px; background: #a5aab0; position: relative}
.carousel-indicators.sample-round-indicator li.active{background: #1e2a39;}
.carousel-indicators.sample-round-indicator li.active:before{width: 15px; height: 15px; top: -3px; left: -3px; border: 1px solid #000000; border-radius:15px;}

.v-tablist{display: block}
.v-tablist .d-flex{border-left: 1px solid #d8e1e5; border-right: 1px solid #ffffff;  border-top: 1px solid #f3f6f7}
.v-tablist .d-flex:hover{background: #f9fbfb;}
.v-tablist .d-flex.active{border-color: #dce4e8; border-left: 0; background: #f9fbfb; color: #1e2a39}
.v-tablist .d-flex.active+.d-flex{border-top-color: #dce4e8}
.v-tablist-right .d-flex{border-left: 1px solid #ffffff; border-right: 1px solid #d8e1e5;}
.v-tablist-right .d-flex.active{ border-right: 0; border-left: 1px solid #dce4e8}

.TM-imgWrap{height: 350px; overflow: hidden; z-index: 1; max-width: 60%}
.TM-imgWrap:before{border-bottom: 350px solid #fff;border-left: 193px solid transparent;right: 0;top: 0;}
.TM-imgWrap:after{top:0; left: 0; background: url("../images/ToolsCut.png") bottom left no-repeat; width: 184px; height: 350px; z-index: 1}
.TM-content{top: 77px; right: 0; z-index: 2; height: 217px;}
.TM-content:before{border-bottom: 217px solid #ecf1f3;border-left: 112px solid transparent;left: -111px;top: 0;}
.TM-btn{bottom: -20px; left: 1.5rem}

.TM-right .TM-imgWrap:before{right: auto; left: 0; border-right: 193px solid transparent; border-left: 0;}
.TM-right .TM-imgWrap:after{background: url("../images/ToolsCut-right.png") bottom left no-repeat; left: auto; right: 0}
.TM-right .TM-content{right: auto; left: 0}
.TM-right .TM-content:before{left: auto; right: -111px;border-left: 0;border-right: 112px solid transparent;}

.RCR{bottom: 0; right: 15px; z-index: 999}

.font-size22{font-size: 1.4rem;}
.text-shadow{text-shadow: 1px 1px 1px rgba(0,0,0,0.35)}
.line-height{line-height: 1.4}
.line-height2{line-height: 2}
.border-green{border-color:#cbebb8 !important}
.border-light-grey{border-color:#ecf1f3 !important}
.border-head-green{border-color:#727e6c !important}



/*VImmy*/
.hideIT{
    display: none !important;
}
.news-item{
    padding-left: 0px !important;
    padding-right: 0px !important;
}
.multiselect-container>li>a>label {
    padding: 0px 0px 0px 10px;
    outline: none;
}
.multiselect-container.with-inputType-none input[type="radio"]{
    display: none;
 }
 .multiselect.dropdown-toggle.btn.btn-default{
    background: #fff;
     border: 1px solid #ddd;
     border-radius: 0;
 }
 #introVideo .modal-footer{
     display: block !important;
     padding: 5px;
 }
 #introVideo .modal-footer p{
     font-size: 14px;
     padding:10px;
     margin-bottom: 0px;

 }
 #introVideo .modal-footer p .btn{
    float: right;
    margin-top: -6px;
 }
 .questionRequest li{
     cursor: pointer;
 }
 #filterID{
     cursor: pointer;
 }
 .filterBody{
    font-size: 12px;
    width: 250px;
    height: auto;
    display: block;
    border: 1px solid #ddd;
    padding: 8px;
    margin-top: 5px;
    padding-top: 0px;
    position: absolute;
    right: 0;
    z-index: 101;
    background: #fff;
 }
 .multiselect-clear-filter{
     margin-right: 10px;
 }
 .btn.focus, .btn:focus {
    box-shadow: none !important;
}
.multiselect.btn{
    text-align: left;
}
.multiselect-container>li>a {
    padding: 5px 0px;
    display: block;
    border-bottom: 1px solid #ddd;
}
:focus {
    outline: none !important;
}
.fas.fa-filter{
    cursor: pointer;
}
/* .loader-info{ */
    /* margin: 0 auto; */
/* } */
.slimScrollDiv {
    padding-right: 10px !important;
}
.logo-text{
    float:right;
}
#raiseARequest{
    cursor: pointer;
}
.lineNormal {
    line-height: 1.4;
}
.size10 {
    font-size: 10px;
}
.filerSection{
    position: relative;
}
.btn-primary.disabled, .btn-primary:disabled,.btn-primary.disabled:hover, .btn-primary:disabled:hover {
    color: #fff;
    background-color: #ccc;
    border-color: #ccc;
}


.absolute-center {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
     width: 100%;
    flex-direction: column;
	min-height: 285px;
  }
.absolute-center i {
    font-size: 45px;
    margin-bottom: 20px;
    color: #6C757D;
    display: block;
}
.absolute-center p{
    font-size: 18px;
}

 .loader-info{
	 position: absolute;
    left: 50%;
    top: 36%;
	transform: translate(0, -50%);
 }
 .newsSection{
    position: relative;
 }
.newsSection .filerSection {
    position: static;
}
.newsSection .filterBody {
    margin-top: 3px;
}
.tsc-tab-new li.nav-item.first {
    margin-bottom: -1px;
    margin-left: 10px;
}
.tsc-tab-new.nav-tabs .nav-item.show .nav-link, .tsc-tab-new.nav-tabs .nav-link.active{
    border: 1px solid #96d671;
    border-bottom: 1px solid transparent;
}
.nav-tabs.tsc-tab-new{
    border-bottom: 1px solid #96d671;
    margin-top: -8px;

}
.nav-tabs.tsc-tab-new .nav-item {
    margin-bottom: -3px;
}
.nav-tabs.tsc-tab-new .nav-link {
    padding: .3rem 1rem;
}

li.nav-item:first-child {
    margin-left: 10px;
}
/* .nav-tabs.tsc-tab-new .nav-link:focus, .nav-tabs.tsc-tab-new .nav-link:hover {
    border-color: white #96d671 white;
    border-top: 1px solid #96d671;
} */

.filterScroll select{
    font-size: 12px;
}
.multiselect-container>li>a>label.checkbox, .multiselect-container>li>a>label.radio{
    font-size: 12px;
}
.multiselect-container>li>a>label {
    padding: 3px 20px 3px 7px;
}
span.multiselect-selected-text {
    font-size: 12px;
}
.form-control.multiselect-search::placeholder{
    font-size: 12px;
}
.closeBtn{
    font-size: 23px;
    position: absolute;
    right: 26px;
    top: 8px;
    cursor: pointer;
    color: #fff;
    background: #c7d4da;
    height: 25px;
    width: 25px;
    line-height: 25px;
    border-radius: 50px;
    text-align: center;
}

.multiselect-container>li>a>label {
    padding: 3px 20px 3px 10px !important;
}
.CategoryInsightScroll .table thead th{
    border-top: 0px;
}
#downloadModal{
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
#downloadModal .modal-header{
    padding: 10px;
}
select#costStructureFilter {
    font-size: 12px;
}
#RCRbox input,#RCRbox textarea {
    font-size: 12px;
}
li.export-main span {
    font-size: 12px;
}

.amcharts-export-menu li > a {
    padding: 5px 5px !important;
}

.amChartsLegend.amcharts-legend-div svg{
     width: 100% !important;
 }
 .amcharts-export-menu .export-main > a,
 .amcharts-export-menu .export-drawing > a, .amcharts-export-menu .export-delayed-capturing > a {
    width: 30px !important;
    height: 30px  !important;
    border-radius: 30px  !important;
    margin-right: 0px !important;
    line-height: 30px !important;
 }
 .loader-info{
	left: calc(100%/2 - 37.5px);
    top: calc(100%/2 - 37.5px);
	position: absolute;
}
#carouselExampleIndicators{
    min-height: 285px;
}
footer a.btn{
    height:28px !important;
}

a.text-primary:focus, a.text-primary:hover {
    color: #1e2a39 !important;
}

.show-ol{
    margin-top: -12px;
}
#loadCategory .TileContent h3 a:nth-child(2) {
    pointer-events: none;
}
.TM-content1{
    height: 288px !important;
    top: 30px !important;
}
.TM-content1:before {
    border-bottom: 287px solid #ecf1f3 !important;
}
.TM-right1 .TM-imgWrap:before {
    right: auto;
    left: 0;
    border-right: 138px  solid transparent !important;
    border-left: 0;
}
.TM-right1 .TM-content:before {
    left: auto;
    right: -144px !important;
    border-left: 0;
    border-right: 144px solid transparent !important;
}
.loader-info {
    border: 5px solid #ecf1f3;
    border-radius: 50%;
    border-top: 5px solid #00ae9b;
    width: 45px;
    height: 45px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
    z-index: 9999;
}
.search .search-input{
    opacity: 1;
    width: 35%;
}
#readingSearch{
    opacity:1;
    width: 100%;
}
.sample-logo{background: transparent; background-size: cover;}

.supplierrisk-logo{
    background: url("../images/supplierriskslide.jpg") center center no-repeat; background-size: cover;
}

.innerTextShouldCost{
    position: absolute;
    top: 58%;
    color: #fff;
    z-index: 999;
    left: 2%;
}
.innerTextShouldCost h2{
    color: #00ae9b;
    font-size: 1.3em;
    margin-bottom: 0px;
    text-align: left;
}
.innerTextShouldCost p{
    font-size: 1.0em;
}
.innerDivText {
    position: absolute;
    z-index: 999;
    top: 32%;
    color: #fff;
    left: 44%;
}
.innerDivText h2{
    color: #00ae9b;
    font-size: 2.3em;
}
.innerDivText p{
    font-size: 1.4em;
    width: 69%;
}
#confirmHomePage {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}


.underlineAnchor{
    border-bottom: 1px solid #fff;
}

#redirectPop .modal-content{
    border-radius: 0px;
    background: #00ae9b;
}
#redirectPop .modal-header{
    min-height: 70px;
    background: #00ae9b;
}
#redirectPop .modal-header a img.img1{
    width: 90px;
    float: left;
    margin-right: 8px;

}
#redirectPop .modal-header a img.img2{
    width: 111px;
    margin-left: 0px;
}
#redirectPop .modal-body{
    background: #fff;
}
#redirectPop .p1{
    font-size: 1.2em;
}
#redirectPop .p2{
    font-size: 1.0em;
}
#redirectPop #checkUserAction{
    font-size: 14px;
    line-height: 31px;
}
#redirectPop .modal-footer{
    background: #fff;
}
#redirectPop .modal-content{
    border: inherit !important;
}
#tsc_nav_1 .nav-link{
    color: #1e2a39 !important;
}
#tsc_nav_1 .navbar .nav-link:focus, .navbar .nav-link:hover, .navbar .nav-item:hover {
    background: white !important;
    color: white !important;
}
#tsc_nav_1 .nav-link {
    padding-top: 5px;
    padding-bottom: 5px;
}
body{
    font-size: 12px !important;
}
.form-control, .btn, table{
    font-size: 12px !important;
}
.mSelect{
    width: 100%;
}
.fSelect{
    width: 100%;
}
.red{
    color: red;
}
.mSelect + div.btn-group{
     display: flex;
}
.mSelect + div.btn-group  button.multiselect {
    width: 100%;
    min-height: 38px;
    white-space: normal;
}
.fSelect + div.btn-group{
    display: flex;
}
.fSelect + div.btn-group  button.multiselect {
   width: 100%;
   min-height: 38px;
   white-space: normal;
}
.multiselect-filter .input-group span {
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}
.multiselect-container > li > a{
    margin-left: 0px;
}
.multiselect-clear-filter{
    margin-right:0;
}
.paginate_button:hover,.paginate_button:active{
    background: #00ae9b !important;
    border: 1px solid #00ae9b !important;
}
form {
    margin-top: 10px;
    margin-bottom: 10px;
}
.modal-body{
    height: 500px;
    overflow-y: auto;
}
.alert-danger {
    background: #BB3609;
    border-radius: 0px;
    border-color: transparent;
    color: white;
}
.navbar-nav .fa-user{
    font-size:12px !important;
}
.form-control, .btn, table{
    border-radius: 0px !important;
}
.toggleForm,.toggleForm1{
    cursor: pointer;
}
table.dataTable thead th, table.dataTable thead td {
    padding: 5px 18px !important;
    border-bottom: 1px solid #111;
}
.dataTables_wrapper .dataTables_paginate .paginate_button {
    min-width: 1.2em !important;
    padding: 0.2em 0.5em !important;
    border:1px solid  #ddd !important;

}
.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 0;
    margin-left: -1px;
}
.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover{
    border-top: 1px solid #ddd !important;
    border-bottom: 1px solid #ddd !important;
    border-left: none !important;
    border-right: none !important;
}
#collapseFilter .form-group,#collapseFilter1 .form-group{
    margin-bottom:0px!important;
}
div#collapseFilter, #collapseFilter1 {
    border: 1px solid #a2b8c1;
    padding-left: 10px !important;
    padding-right: 10px !important;
    margin-left: 1px;
    width: 99.87%;
}

table.dataTable thead th, table.dataTable thead td {
    padding: 5px 10px !important;
    border-bottom: 1px solid #111;
}
.multiselect-container input[type="radio"] {
    display: none !important;
}
.modal-header {
    padding-top: 10px;
    padding-bottom: 10px;
}
h4.modal-title {
    font-size: 18px;

}
.alert {
    margin: 5px;
    padding: 10px;
}
.refBtn{
    margin-top: -9px;
    margin-right: -14px;
}
/********* For banner form fields validation class ********/

input.error,select.error {
    border: 1px solid red;
}

.error{
color:red;
}

.navbar-dark .navbar-brand, .navbar-dark .navbar-brand:hover {
    color: #00ae9b;
}
#tsc_nav_1 .nav-link {
    padding-right: .5rem;
    padding-left: .5rem;
}
#tsc_nav_1  .dropdown-submenu > a:after {
    display: block;
    content: " ";
    float: right;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 5px 0 5px 5px;
    border-left-color: #343a40;
    margin-top: 5px;
    margin-right: -10px;
}

/* Sidebar */
#sidebar {
    overflow: hidden;
    z-index: 3;
    background-color: #1e2a39;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
}
#sidebar .list-group {
    min-width: 240px;
    background-color: #1e2a39;;
    min-height: 100vh;
    margin-right: 0;
}
#sidebar i {
    margin-right: 6px;
}

#sidebar .list-group-item {
    border-radius: 0px;
    background-color: #1e2a39;
    color: rgb(204, 204, 204);
    border-left: 0px #1e2a39;
    border-right: 0px #1e2a39;
    border-top-color: #1e2a39;
    border-bottom-color: #1e2a39;
    white-space: nowrap;
}

/* highlight active menu */
#sidebar .list-group-item:not(.collapsed) {
    background-color: #5f6a76;
}

/* closed state */
#sidebar .list-group .list-group-item[aria-expanded="false"]::after {
  content: " \f0d7";
  font-family: FontAwesome;
  display: inline;
  text-align: right;
  padding-left: 5px;
}

/* open state */
#sidebar .list-group .list-group-item[aria-expanded="true"] {
  background-color: #1e2a39;
}
#sidebar .list-group .list-group-item[aria-expanded="true"]::after {
  content: " \f0da";
  font-family: FontAwesome;
  display: inline;
  text-align: right;
  padding-left: 5px;
}

/* level 1*/
#sidebar .list-group .collapse .list-group-item,
#sidebar .list-group .collapsing .list-group-item  {
  padding-left: 20px;
}

/* level 2*/
#sidebar .list-group .collapse > .collapse .list-group-item,
#sidebar .list-group .collapse > .collapsing .list-group-item {
  padding-left: 30px;
}

/* level 3*/
#sidebar .list-group .collapse > .collapse > .collapse .list-group-item {
  padding-left: 40px;
}

@media (max-width:768px) {
    #sidebar {
        min-width: 35px;
        max-width: 40px;
        overflow-y: auto;
        overflow-x: visible;
        transition: all 0.25s ease;
        transform: translateX(-45px);
        position: fixed;
    }

    #sidebar.show {
        transform: translateX(0);
    }

    #sidebar::-webkit-scrollbar{ width: 0px; }

    #sidebar, #sidebar .list-group {
        min-width: 35px;
        overflow: visible;
    }
    /* overlay sub levels on small screens */
    #sidebar .list-group .collapse.show, #sidebar .list-group .collapsing {
        position: relative;
        z-index: 1;
        width: 190px;
        top: 0;
    }
    #sidebar .list-group > .list-group-item {
        text-align: center;
        padding: .75rem .5rem;
    }
    /* hide caret icons of top level when collapsed */
    #sidebar .list-group > .list-group-item[aria-expanded="true"]::after,
    #sidebar .list-group > .list-group-item[aria-expanded="false"]::after {
        display:none;
    }
}

.collapse.show {
  visibility: visible;
}
.collapsing {
  visibility: visible;
  height: 0;
  -webkit-transition-property: height, visibility;
  transition-property: height, visibility;
  -webkit-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
}
.collapsing.width {
  -webkit-transition-property: width, visibility;
  transition-property: width, visibility;
  width: 0;
  height: 100%;
  -webkit-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
}
.navbar-brand, .navbar-brand:hover {
    text-align: left;
    color: #fff;
    font-size: 26px;
    font-weight: normal;
    line-height: 31px;
    padding-top: 10px;
    padding-bottom: 20px;
    padding: .75rem 1.25rem;
    padding-bottom: 2px;
}

.dataTables_wrapper {
    margin-top: 10px;
    margin-bottom: 10px;
}
#tsc_nav_1 .dropdown-submenu > a:after {
    display: block;
    content: " ";
    float: right;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 4px 0 4px 4px;
    border-left-color: #343a40;
    margin-top: 5px;
    margin-right: -10px;
}
#sidebar {
    background: #1e2a39 !important;
}
#mainSection{
    padding-left: 10px !important;
}
.widget-header {
    margin-bottom: 10px;
}
.modal-body p {
    margin-bottom: 0px;
}
.modal-content {
    border-radius: 0px !important;
}
.modal-title {
    font-size: 16px !important;
}


.modal-body .table {
    margin-bottom: 0px !important;
}
#sidebar a[data-toggle="collapse"] {
    font-weight: 600;
}
.dataTable {
    width: 100% !important;
}
.alert {
    margin: 5px;
    padding: 5px;
    margin-left: 0px;
    margin-right: 0px;
}
button.close {
    padding: 0;
    background-color: transparent;
    border: 0;
    -webkit-appearance: none;
    opacity: 1;
    color: #fff !important;
    font-weight: normal;
}
.dataTables_wrapper .dataTables_processing {
    background-color: transparent !important;
    background: transparent !important;
    top: 20%;
}
.modal-header button.close {
    color: #000 !important;
}

.dataTable td:last-child {
    min-width: 110px !important;
}