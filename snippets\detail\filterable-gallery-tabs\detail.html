
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}


    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
          <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
          <i class="fas fa-bars"></i>
        </span>
      </div>
<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
        <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Filtered Gallery Tabs via CSS</h4>
              <div class="mb-2">
                <a
                  href="./downloads/filterable-gallery-tabs.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/filterable-gallery-tabs/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup"> &lt;h1&gt;Example of a Filterable Gallery with Tabs Implemented Solely Using CSS&lt;/h1&gt;
    &lt;p class="lead"&gt;Construct a sophisticated gallery that applies CSS classes as filters, allowing the grouping of items
      and implementing tabs for seamless navigation.&lt;/p&gt;
    &lt;div class="wrapper"&gt;
  
      &lt;div class="container"&gt;
        &lt;input class="checkbox-all" id="all" type="radio" name="checkbox" checked="" /&gt;
        &lt;label for="all"&gt;all&lt;/label&gt;
        &lt;input class="checkbox-js" id="js" type="radio" name="checkbox" /&gt;
        &lt;label for="js"&gt;javascript&lt;/label&gt;
        &lt;input class="checkbox-react" id="react" type="radio" name="checkbox" /&gt;
        &lt;label for="react"&gt;react&lt;/label&gt;
        &lt;input class="checkbox-html" id="html" type="radio" name="checkbox" /&gt;
        &lt;label for="html"&gt;HTML/CSS&lt;/label&gt;
        &lt;input class="checkbox-vue" id="vue" type="radio" name="checkbox" /&gt;
        &lt;label for="vue"&gt;vue&lt;/label&gt;
        &lt;input class="checkbox-angular" id="angular" type="radio" name="checkbox" /&gt;
        &lt;label for="angular"&gt;angular&lt;/label&gt;
        &lt;div class="seperator"&gt;&lt;/div&gt;
        &lt;!-- project cards--&gt;
        &lt;div class="cards"&gt;
          &lt;div class="project js"&gt;&lt;/div&gt;
          &lt;div class="project html"&gt;&lt;/div&gt;
          &lt;div class="project react"&gt;&lt;/div&gt;
          &lt;div class="project vue"&gt;&lt;/div&gt;
          &lt;div class="project angular"&gt;&lt;/div&gt;
          &lt;div class="project js"&gt;&lt;/div&gt;
          &lt;div class="project html"&gt;&lt;/div&gt;
          &lt;div class="project react"&gt;&lt;/div&gt;
          &lt;div class="project vue"&gt;&lt;/div&gt;
          &lt;div class="project angular"&gt;&lt;/div&gt;
          &lt;div class="project js"&gt;&lt;/div&gt;
          &lt;div class="project html"&gt;&lt;/div&gt;
          &lt;div class="project react"&gt;&lt;/div&gt;
          &lt;div class="project vue"&gt;&lt;/div&gt;
          &lt;div class="project angular"&gt;&lt;/div&gt;
          &lt;div class="project js"&gt;&lt;/div&gt;
          &lt;div class="project html"&gt;&lt;/div&gt;
          &lt;div class="project react"&gt;&lt;/div&gt;
          &lt;div class="project vue"&gt;&lt;/div&gt;
          &lt;div class="project angular"&gt;&lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;</code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
body {
margin: 0;
padding: 0;
background-color: rgba(0, 0, 0, 0.85);
color: whitesmoke;
font-weight: 400;
font-size: 15px;
line-height: 1.7;
font-family: 'Fira Sans' !important;
}
      
h1,
.lead {
text-align: center;
}

h1 {
margin-top: 150px;
}

.wrapper {
width: 100%;
padding: 60px 0;
position: relative;
display: flex;
justify-content: center;
}

[type="radio"]:checked,
[type="radio"]:not(:checked) {
position: absolute;
left: -9999px;
}

[type="radio"]:checked+label,
[type="radio"]:not(:checked)+label {
position: relative;
padding-left: 12px;
padding-right: 12px;
letter-spacing: 3px;
cursor: pointer;
line-height: 30px;
font-size: 15px;
text-transform: uppercase;
font-weight: 500;
color: #fff;
margin-right: 10px;
margin-left: 10px;
-webkit-transition: all 0.2s ease;
transition: all 0.2s ease;
}

[type="radio"]:checked+label:after,
[type="radio"]:not(:checked)+label:after {
content: "";
border-radius: 0px;
height: 20px;
padding: 5px;
background-color: #00b19c;
position: absolute;
bottom: 0;
left: 6px;
top: -4px;
z-index: -1;
-webkit-transition: all 0.2s ease;
transition: all 0.2s ease;
}

[type="radio"]:not(:checked)+label:after {
opacity: 0;
width: 0;
}

[type="radio"]:checked+label:after {
opacity: 1;
width: calc(100% - 24px);
}

.checkbox-all:checked+label:after,
.checkbox-all:not(:checked)+label:after {
background-color: #00b19c;
}

.checkbox-js:checked+label:after,
.checkbox-js:not(:checked)+label:after {
background-color: #f1c40f;
}

.checkbox-html:checked+label:after,
.checkbox-html:not(:checked)+label:after {
background-color: #ff932b;
}

.checkbox-react:checked+label:after,
.checkbox-react:not(:checked)+label:after {
background-color: #64e1fe;
}

.checkbox-vue:checked+label:after,
.checkbox-vue:not(:checked)+label:after {
background-color: #6edbbe;
}

.checkbox-angular:checked+label:after,
.checkbox-angular:not(:checked)+label:after {
background-color: #e75139;
}

.checkbox-all:checked+label {
margin-left: 17px;
}

.seperator {
width: 100%;
height: 30px;
}

.cards {
display: flex;
flex-wrap: wrap;
width: 800px;
}

.project {
border-radius: 0px;
box-shadow: 6px 7px 28px 0 rgba(16, 16, 16, 0.7);
-webkit-transition: all 0.2s linear;
transition: all 0.3s linear;
}

.project.js {
border: 3px solid #f1c40f;
}

.project.html {
border: 3px solid #ff932b;
}

.project.react {
border: 3px solid #64e1fe;
}

.project.vue {
border: 3px solid #6edbbe;
}

.project.angular {
border: 3px solid #e75139;
}

.project.js,
.project.html,
.project.react,
.project.vue,
.project.angular {
opacity: 0;
transform: scale(0);
padding: 0;
margin: 0;
visibility: hidden;
border-width: 0;
}

.checkbox-all:checked~.cards .project.js,
.checkbox-all:checked~.cards .project.html,
.checkbox-all:checked~.cards .project.react,
.checkbox-all:checked~.cards .project.vue,
.checkbox-all:checked~.cards .project.angular,
.checkbox-js:checked~.cards .project.js,
.checkbox-html:checked~.cards .project.html,
.checkbox-react:checked~.cards .project.react,
.checkbox-vue:checked~.cards .project.vue,
.checkbox-angular:checked~.cards .project.angular {
opacity: 1;
min-height: 140px;
min-width: calc(22% - 28px);
padding: 0 10px;
margin-left: 10px;
margin-right: 10px;
margin-top: 20px;
visibility: visible;
transform: scale(1);
border-width: 3px;
}

@media only screen and (max-width: 650px) {
.container {
    width: 100%;
}

label {
    display: block;
}

.cards {
    flex-direction: column;
    width: auto;
}
}</code></pre>
        </div>
        <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">
</code></pre>
        </div>
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
      <script src="./js/iframe.js"></script>
    </body>
</html>
