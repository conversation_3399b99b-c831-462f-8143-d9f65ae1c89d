<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />
     <meta name="author" content="Vimal Thapliyal" />

    <title>Video screen recorder</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Video screen recorder</h4>
              <div class="mb-2">
                <a
                  href="./downloads/video-screen-recorder.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li> 
               <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <!-- <li><a href="#tab03">CSS</a></li> -->
                <!-- <li><a href="#tab04">Javascript</a></li> -->
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/video-screen-recorder/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
<pre class="line-numbers">
<code class="language-markup">&lt;!DOCTYPE html&gt;
    &lt;html&gt;
      &lt;head&gt;
        &lt;meta charset="UTF-8"&gt;
        &lt;meta http-equiv="Content-Type" content="text/html; charset=utf-8"&gt;
        &lt;meta http-equiv="X-UA-Compatible" content="IE=edge"&gt;
        &lt;title&gt;Screen Recorder&lt;/title&gt;
        &lt;meta name="MobileOptimized" content="176"&gt;
        &lt;meta name="HandheldFriendly" content="True"&gt;
        &lt;meta name="viewport" content="width=device-width, initial-scale=0.8, maximum-scale=0.8, user-scalable=no"&gt;
        &lt;link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous"&gt;
        &lt;link rel="stylesheet" href="./assets/css/style.css"&gt;
      &lt;/head&gt;
      &lt;body&gt;
        &lt;video src="" class="hide" id="video" width="600px" controls autoplay loop&gt;&lt;/video&gt;
        &lt;button class="record-btn btn btn-success"&gt;Record your screen&lt;/button&gt;
        &lt;button class="stop-btn btn btn-danger hide"&gt;stop&lt;/button&gt;
        &lt;script defer="true" type="text/javascript" src="./assets/js/recorder.js"&gt;&lt;/script&gt;    
      &lt;/body&gt;
    &lt;/html&gt;
</code>  
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">@import url('https://fonts.googleapis.com/css?family=Fira+Sans');
body{
    font-family: 'Fira Sans' !important;
    padding: 0;margin: 0;
    width: 100vw;height: 100vh;
    background: linear-gradient(  33deg,  #040408 ,  #0d0d0f ,  #040408 );
    color: rgba(243 236 236 / 97%);
    display: flex;
    flex-flow: column wrap;
    justify-content: space-evenly;
    align-items: center;
    overflow: hidden;
    
}

#video{
    border-radius: 0;
    height: auto;
    width: calc(320px + 25vw);
}

#video:fullscreen{
    border-radius: 0 !important;
}

              </code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">let record_btn = document.querySelector(".record-btn")
let stop_btn = document.querySelector(".stop-btn")

record_btn.addEventListener("click", async function () {

    try {

    window.stream = await navigator.mediaDevices.getDisplayMedia({
        video: true
    })

    stop_btn.onclick = () => {
        stream.getTracks().forEach(track => track.stop());
        document.querySelector('.stop-btn').classList.toggle('hide');
        document.querySelector('.record-btn').classList.toggle('hide');
    };

    //needed for better browser support
    const mime = MediaRecorder.isTypeSupported("video/webm; codecs=vp9") 
                ? "video/webm; codecs=vp9" 
                : "video/webm"
        let mediaRecorder = new MediaRecorder(stream, {
            mimeType: mime
        })

        let chunks = []
        mediaRecorder.addEventListener('dataavailable', function(e) {
            chunks.push(e.data)
        })

        mediaRecorder.addEventListener('stop', function(){
        let blob = new Blob(chunks, {
            type: chunks[0].type
        })
        let url = URL.createObjectURL(blob)

        let video = document.querySelector("#video")
        video.src = url

        video.style.display = 'block';

        let a = document.createElement('a')
        a.href = url
        a.download = 'video.webm'
        a.click()
    })

    mediaRecorder.start()
    document.querySelector('.record-btn').classList.toggle('hide');
    document.querySelector('.stop-btn').classList.toggle('hide');
    
    } catch(err) {
    console.error("Error: " + err);
    }
});
            </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>
    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
