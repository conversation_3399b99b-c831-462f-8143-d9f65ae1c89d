{"name": "bootstrap-multiselect", "description": "Twitter Bootstrap plugin to make selects user friendly.", "homepage": "http://davidstutz.de/bootstrap-multiselect/", "version": "0.9.15", "keywords": ["js", "css", "less", "bootstrap", "j<PERSON>y", "multiselect"], "main": ["./dist/js/bootstrap-multiselect.js", "./dist/css/bootstrap-multiselect.css", "./dist/less/bootstrap-multiselect.less"], "dependencies": {"jquery": ">= 1.11.0", "bootstrap": ">= 2.3.2"}, "ignore": ["docs/.*", "tests/.*", "*.html", "*.git*", "*.md", "*.png", "*.php"]}