
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
      <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
      <div class="col-sm-12 p-0">
        <div class="d-flex justify-content-between">
            <div>
                <h4 class="size20 mb-2 pt-2">TSC Theme Colors</h4>                   
            </div>
          </div>   
    </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/colors/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
  &lt;div class="p-3 mb-2 bg-primary text-white"&gt;.bg-primary&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-primary2 text-white"&gt;.bg-primary2&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-primary3 text-white"&gt;.bg-primary3&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-primary4 text-white"&gt;.bg-primary4&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-primary5 text-white"&gt;.bg-primary5&lt;/div&gt;
  
  &lt;div class="p-3 mb-2 bg-secondary text-white"&gt;.bg-secondary&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-secondary2 text-white"&gt;.bg-secondary2&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-secondary3 text-white"&gt;.bg-secondary3&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-secondary4 text-white"&gt;.bg-secondary4&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-secondary5 text-white"&gt;.bg-secondary5&lt;/div&gt;
  
  &lt;div class="p-3 mb-2 bg-info text-white"&gt;.bg-info&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-info2 text-white"&gt;.bg-info2&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-info3 text-white"&gt;.bg-info3&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-info4 text-white"&gt;.bg-info4&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-info5 text-white"&gt;.bg-info5&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-light text-white"&gt;.bg-light&lt;/div&gt;
  
  &lt;div class="p-3 mb-2 bg-light2 text-white"&gt;.bg-light2&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-light3 text-white"&gt;.bg-light3&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-light4 text-white"&gt;.bg-light4&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-light5 text-white"&gt;.bg-light5&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-highlight text-white"&gt;.bg-highlight&lt;/div&gt;
  
  &lt;div class="p-3 mb-2 bg-highlight2 text-white"&gt;.bg-highlight2&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-highlight3 text-white"&gt;.bg-highlight3&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-highlight4 text-white"&gt;.bg-highlight4&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-highlight5 text-white"&gt;.bg-highlight5&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-success text-white"&gt;.bg-success&lt;/div&gt;
  
  &lt;div class="p-3 mb-2 bg-danger text-white"&gt;.bg-danger&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-warning text-dark"&gt;.bg-warning&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-dark text-white"&gt;.bg-dark&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-white text-dark"&gt;.bg-white&lt;/div&gt;
  &lt;div class="p-3 mb-2 bg-transparent text-dark"&gt;.bg-transparent&lt;/div&gt;

Text colors

&lt;p class="text-primary"&gt;.text-primary&lt;/p&gt;
&lt;p class="text-primary2"&gt;.text-primary2&lt;/p&gt;
&lt;p class="text-primary3"&gt;.text-primary3&lt;/p&gt;
&lt;p class="text-primary4"&gt;.text-primary4&lt;/p&gt;
&lt;p class="text-primary5"&gt;.text-primary5&lt;/p&gt;

&lt;p class="text-secondary"&gt;.text-secondary&lt;/p&gt;
&lt;p class="text-secondary2"&gt;.text-secondary2&lt;/p&gt;
&lt;p class="text-secondary3"&gt;.text-secondary3&lt;/p&gt;
&lt;p class="text-secondary4"&gt;.text-secondary4&lt;/p&gt;
&lt;p class="text-secondary5"&gt;.text-secondary5&lt;/p&gt;

&lt;p class="text-info"&gt;.text-info&lt;/p&gt;
&lt;p class="text-info2"&gt;.text-info2&lt;/p&gt;
&lt;p class="text-info3"&gt;.text-info3&lt;/p&gt;
&lt;p class="text-info4"&gt;.text-info4&lt;/p&gt;
&lt;p class="text-info5"&gt;.text-info5&lt;/p&gt;

&lt;p class="text-light bg-dark"&gt;.text-light&lt;/p&gt;
&lt;p class="text-light2 bg-dark"&gt;.text-light2&lt;/p&gt;
&lt;p class="text-light3 bg-dark"&gt;.text-light3&lt;/p&gt;
&lt;p class="text-light4 bg-dark"&gt;.text-light4&lt;/p&gt;
&lt;p class="text-light5 bg-dark"&gt;.text-light5&lt;/p&gt;

&lt;p class="text-highlight"&gt;.text-highlight&lt;/p&gt;
&lt;p class="text-highlight2"&gt;.text-highlight2&lt;/p&gt;
&lt;p class="text-highlight3"&gt;.text-highlight3&lt;/p&gt;
&lt;p class="text-highlight4"&gt;.text-highlight4&lt;/p&gt;
&lt;p class="text-highlight5"&gt;.text-highlight5&lt;/p&gt;

&lt;p class="text-success"&gt;.text-success&lt;/p&gt;
&lt;p class="text-danger"&gt;.text-danger&lt;/p&gt;
&lt;p class="text-warning"&gt;.text-warning&lt;/p&gt;
&lt;p class="text-dark"&gt;.text-dark&lt;/p&gt;
&lt;p class="text-body"&gt;.text-body&lt;/p&gt;
&lt;p class="text-muted"&gt;.text-muted&lt;/p&gt;
&lt;p class="text-white bg-dark"&gt;.text-white&lt;/p&gt;
&lt;p class="text-black-50"&gt;.text-black-50&lt;/p&gt;
&lt;p class="text-white-50 bg-dark"&gt;.text-white-50&lt;/p&gt;
 
Borders

&lt;span class="border"&gt;&lt;/span&gt;
&lt;span class="border-top"&gt;&lt;/span&gt;
&lt;span class="border-right"&gt;&lt;/span&gt;
&lt;span class="border-bottom"&gt;&lt;/span&gt;
&lt;span class="border-left"&gt;&lt;/span&gt;

</code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"></pre>
        </div>
        <!-- <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">

            </code></pre>
        </div> -->
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
