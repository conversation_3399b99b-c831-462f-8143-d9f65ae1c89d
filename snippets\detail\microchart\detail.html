<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>In-line Charts</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Microcharts - In-line Charts</h4>
              <div class="mb-2">
                <a
                  href="./downloads/microchart.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/microchart/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
              <pre class="line-numbers">
<code class="language-markup">&lt;div id="chartdiv"&gt;&lt;/div&gt;
    &lt;hr /&gt;
    &lt;div id="chartdiv1"&gt;&lt;/div&gt;</code>
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">#chartdiv {
                width: 100%;
                height: 100px;
                background-color: #eee;
            }
            #chartdiv1 {
                width: 100%;
                height: 100px;
                background-color: #eee;
            }</code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">
                function createVolumeChart() {
                    var data = [
                        {
                            "date": 1700073000000,
                            "value": 40,
                            "volume": 9940
                        },
                        {
                            "date": 1700159400000,
                            "value": 37,
                            "volume": 9478
                        },
                        {
                            "date": 1700245800000,
                            "value": 38,
                            "volume": 9874
                        },
                        {
                            "date": 1700332200000,
                            "value": 41,
                            "volume": 9784
                        },
                        {
                            "date": 1700418600000,
                            "value": 38,
                            "volume": 10228
                        },
                        {
                            "date": 1700505000000,
                            "value": 36,
                            "volume": 10406
                        },
                        {
                            "date": 1700591400000,
                            "value": 35,
                            "volume": 10411
                        },
                        {
                            "date": 1700677800000,
                            "value": 32,
                            "volume": 10458
                        },
                        {
                            "date": 1700764200000,
                            "value": 30,
                            "volume": 10503
                        },
                        {
                            "date": 1700850600000,
                            "value": 32,
                            "volume": 10905
                        },
                        {
                            "date": 1700937000000,
                            "value": 35,
                            "volume": 10569
                        },
                        {
                            "date": 1701023400000,
                            "value": 33,
                            "volume": 10552
                        },
                        {
                            "date": 1701109800000,
                            "value": 32,
                            "volume": 10644
                        },
                        {
                            "date": 1701196200000,
                            "value": 31,
                            "volume": 10645
                        },
                        {
                            "date": 1701282600000,
                            "value": 29,
                            "volume": 10559
                        },
                        {
                            "date": 1701369000000,
                            "value": 31,
                            "volume": 10714
                        },
                        {
                            "date": 1701455400000,
                            "value": 33,
                            "volume": 10268
                        },
                        {
                            "date": 1701541800000,
                            "value": 35,
                            "volume": 10169
                        },
                        {
                            "date": 1701628200000,
                            "value": 35,
                            "volume": 9713
                        },
                        {
                            "date": 1701714600000,
                            "value": 34,
                            "volume": 9330
                        }
                    ]
                    var root = am5.Root.new("chartdiv");
                    root.setThemes([
                        am5themes_Micro.new(root)
                    ]);
                    var chart = root.container.children.push(am5xy.XYChart.new(root, {
                        panX: false,
                        panY: false,
                        wheelX: "none",
                        wheelY: "none"
                    }));
                    chart.plotContainer.set("wheelable", false);
                    chart.zoomOutButton.set("forceHidden", true);
        
                    var xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
                        maxDeviation: 0,
                        baseInterval: { timeUnit: "day", count: 1 },
                        renderer: am5xy.AxisRendererX.new(root, {})
                    }));
        
                    var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                        renderer: am5xy.AxisRendererY.new(root, {})
                    }));
        
                    var series = chart.series.push(am5xy.ColumnSeries.new(root, {
                        xAxis: xAxis,
                        yAxis: yAxis,
                        valueYField: "volume",
                        valueXField: "date",
                        fill: am5.color(0x999999)
                    }));
        
                    series.data.setAll(data);
        
                }
                function createValueChart() {
                    var data = [
            {
                "date": 1700073000000,
                "value": 25,
                "volume": 7955
            },
            {
                "date": 1700159400000,
                "value": 24,
                "volume": 8368
            },
            {
                "date": 1700245800000,
                "value": 23,
                "volume": 7954
            },
            {
                "date": 1700332200000,
                "value": 23,
                "volume": 7511
            },
            {
                "date": 1700418600000,
                "value": 24,
                "volume": 7767
            },
            {
                "date": 1700505000000,
                "value": 27,
                "volume": 7774
            },
            {
                "date": 1700591400000,
                "value": 24,
                "volume": 7891
            },
            {
                "date": 1700677800000,
                "value": 25,
                "volume": 8228
            },
            {
                "date": 1700764200000,
                "value": 26,
                "volume": 8610
            },
            {
                "date": 1700850600000,
                "value": 27,
                "volume": 8549
            },
            {
                "date": 1700937000000,
                "value": 30,
                "volume": 8838
            },
            {
                "date": 1701023400000,
                "value": 28,
                "volume": 8745
            },
            {
                "date": 1701109800000,
                "value": 25,
                "volume": 8331
            },
            {
                "date": 1701196200000,
                "value": 27,
                "volume": 8632
            },
            {
                "date": 1701282600000,
                "value": 24,
                "volume": 9044
            },
            {
                "date": 1701369000000,
                "value": 26,
                "volume": 9050
            },
            {
                "date": 1701455400000,
                "value": 24,
                "volume": 8942
            },
            {
                "date": 1701541800000,
                "value": 25,
                "volume": 8784
            },
            {
                "date": 1701628200000,
                "value": 26,
                "volume": 8607
            },
            {
                "date": 1701714600000,
                "value": 29,
                "volume": 8380
            }
        ];
                    var root = am5.Root.new("chartdiv1");
                    root.setThemes([
                        am5themes_Micro.new(root)
                    ]);
                    var chart = root.container.children.push(am5xy.XYChart.new(root, {
                        panX: false,
                        panY: false,
                        wheelX: "none",
                        wheelY: "none"
                    }));
                    chart.plotContainer.set("wheelable", false);
                    chart.zoomOutButton.set("forceHidden", true);
                    var xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
                        maxDeviation: 0,
                        baseInterval: { timeUnit: "day", count: 1 },
                        renderer: am5xy.AxisRendererX.new(root, {})
                    }));
                    var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                        strictMinMax: true,
                        extraMax:0.02,
                        extraMin:0.02,
                        renderer: am5xy.AxisRendererY.new(root, {})
                    }));
                    var series = chart.series.push(am5xy.LineSeries.new(root, {
                        xAxis: xAxis,
                        yAxis: yAxis,
                        valueYField: "value",
                        valueXField: "date",
                        stroke: am5.color(0x00af9b)
                    }));
                    series.strokes.template.setAll({
                        strokeWidth: 2
                    });
                    series.data.setAll(data);
                }
                createVolumeChart();
                createValueChart();
            </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
