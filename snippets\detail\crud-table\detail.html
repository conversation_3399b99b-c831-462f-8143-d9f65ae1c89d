<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
          <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
          <i class="fas fa-bars"></i>
        </span>
      </div>
<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
        <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
                <div>
                    <h4 class="size20 mb-2 pt-2">CRUD Table</h4>                   
                </div>
              </div>   
        </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/crud-table/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
&lt;div class="container-xl"&gt;
&lt;div class="table-responsive"&gt;
    &lt;div class="table-wrapper"&gt;
        &lt;div class="table-title"&gt;
            &lt;div class="row"&gt;
                &lt;div class="col-sm-6"&gt;
                    &lt;h2&gt;Manage &lt;b&gt;Employees&lt;/b&gt;&lt;/h2&gt;
                &lt;/div&gt;
                &lt;div class="col-sm-6"&gt;
                    &lt;a href="#addEmployeeModal" class="btn btn-success" data-toggle="modal"&gt;&lt;i class="far fa-edit"&gt;&lt;/i&gt; &lt;span&gt;Add New Employee&lt;/span&gt;&lt;/a&gt;
                    &lt;a href="#deleteEmployeeModal" class="btn btn-danger" data-toggle="modal"&gt;&lt;i class="far fa-trash-alt"&gt;&lt;/i&gt; &lt;span&gt;Delete&lt;/span&gt;&lt;/a&gt;						
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;table class="table table-striped table-hover table-sm"&gt;
            &lt;thead&gt;
                &lt;tr&gt;
                    &lt;th&gt;
                        &lt;span class="custom-checkbox"&gt;
                            &lt;input type="checkbox" id="selectAll"&gt;
                            &lt;label for="selectAll"&gt;&lt;/label&gt;
                        &lt;/span&gt;
                    &lt;/th&gt;
                    &lt;th&gt;Name&lt;/th&gt;
                    &lt;th&gt;Email&lt;/th&gt;
                    &lt;th&gt;Address&lt;/th&gt;
                    &lt;th&gt;Phone&lt;/th&gt;
                    &lt;th&gt;Actions&lt;/th&gt;
                &lt;/tr&gt;
            &lt;/thead&gt;
            &lt;tbody&gt;
                &lt;tr&gt;
                    &lt;td&gt;
                        &lt;span class="custom-checkbox"&gt;
                            &lt;input type="checkbox" id="checkbox1" name="options[]" value="1"&gt;
                            &lt;label for="checkbox1"&gt;&lt;/label&gt;
                        &lt;/span&gt;
                    &lt;/td&gt;
                    &lt;td&gt;Vimal Thapliyal&lt;/td&gt;
                    &lt;td&gt;<EMAIL>&lt;/td&gt;
                    &lt;td&gt;89 Noida Rd, UP, INDIA&lt;/td&gt;
                    &lt;td&gt;(91) xxx-xxxx&lt;/td&gt;
                    &lt;td&gt;
                        &lt;span class="d-flex"&gt;
                            &lt;a href="#editEmployeeModal" class="edit" data-toggle="modal"&gt;&lt;i class="far fa-edit" data-toggle="tooltip" title="Edit"&gt;&lt;/i&gt;&lt;/a&gt;
                        &lt;a href="#deleteEmployeeModal" class="delete" data-toggle="modal"&gt;&lt;i class="far fa-trash-alt" data-toggle="tooltip" title="Delete"&gt;&#xE872;&lt;/i&gt;&lt;/a&gt;
                        &lt;/span&gt;
                    &lt;/td&gt;
                &lt;/tr&gt;
                &lt;tr&gt;
                    &lt;td&gt;
                        &lt;span class="custom-checkbox"&gt;
                            &lt;input type="checkbox" id="checkbox2" name="options[]" value="1"&gt;
                            &lt;label for="checkbox2"&gt;&lt;/label&gt;
                        &lt;/span&gt;
                    &lt;/td&gt;
                    &lt;td&gt;Sneha Issar&lt;/td&gt;
                    &lt;td&gt;<EMAIL>&lt;/td&gt;
                    &lt;td&gt;19 Noida Rd, UP, INDIA&lt;/td&gt;
                    &lt;td&gt;(91) xxx-xxxx&lt;/td&gt;
                    &lt;td&gt;
                        &lt;span class="d-flex"&gt;
                            &lt;a href="#editEmployeeModal" class="edit" data-toggle="modal"&gt;&lt;i class="far fa-edit" data-toggle="tooltip" title="Edit"&gt;&lt;/i&gt;&lt;/a&gt;
                        &lt;a href="#deleteEmployeeModal" class="delete" data-toggle="modal"&gt;&lt;i class="far fa-trash-alt" data-toggle="tooltip" title="Delete"&gt;&#xE872;&lt;/i&gt;&lt;/a&gt;
                        &lt;/span&gt;
                    &lt;/td&gt;
                &lt;/tr&gt;
                &lt;tr&gt;
                    &lt;td&gt;
                        &lt;span class="custom-checkbox"&gt;
                            &lt;input type="checkbox" id="checkbox3" name="options[]" value="1"&gt;
                            &lt;label for="checkbox3"&gt;&lt;/label&gt;
                        &lt;/span&gt;
                    &lt;/td&gt;
                    &lt;td&gt;Pankaj Sharma&lt;/td&gt;
                    &lt;td&gt;<EMAIL>&lt;/td&gt;
                    &lt;td&gt;91 Noida Rd, UP, INDIA&lt;/td&gt;
                    &lt;td&gt;(91) xxx-xxxx&lt;/td&gt;
                    &lt;td&gt;
                        &lt;span class="d-flex"&gt;
                            &lt;a href="#editEmployeeModal" class="edit" data-toggle="modal"&gt;&lt;i class="far fa-edit" data-toggle="tooltip" title="Edit"&gt;&lt;/i&gt;&lt;/a&gt;
                        &lt;a href="#deleteEmployeeModal" class="delete" data-toggle="modal"&gt;&lt;i class="far fa-trash-alt" data-toggle="tooltip" title="Delete"&gt;&#xE872;&lt;/i&gt;&lt;/a&gt;
                        &lt;/span&gt;
                    &lt;/td&gt;
                &lt;/tr&gt;
                &lt;tr&gt;
                    &lt;td&gt;
                        &lt;span class="custom-checkbox"&gt;
                            &lt;input type="checkbox" id="checkbox4" name="options[]" value="1"&gt;
                            &lt;label for="checkbox4"&gt;&lt;/label&gt;
                        &lt;/span&gt;
                    &lt;/td&gt;
                    &lt;td&gt;Govind Singh&lt;/td&gt;
                    &lt;td&gt;<EMAIL>&lt;/td&gt;
                    &lt;td&gt;11 Noida Rd, UP, INDIA&lt;/td&gt;
                    &lt;td&gt;(91) xxx-xxxx&lt;/td&gt;
                    &lt;td&gt;
                        &lt;span class="d-flex"&gt;
                            &lt;a href="#editEmployeeModal" class="edit" data-toggle="modal"&gt;&lt;i class="far fa-edit" data-toggle="tooltip" title="Edit"&gt;&lt;/i&gt;&lt;/a&gt;
                        &lt;a href="#deleteEmployeeModal" class="delete" data-toggle="modal"&gt;&lt;i class="far fa-trash-alt" data-toggle="tooltip" title="Delete"&gt;&#xE872;&lt;/i&gt;&lt;/a&gt;
                        &lt;/span&gt;
                    &lt;/td&gt;
                &lt;/tr&gt;					
                &lt;tr&gt;
                    &lt;td&gt;
                        &lt;span class="custom-checkbox"&gt;
                            &lt;input type="checkbox" id="checkbox5" name="options[]" value="1"&gt;
                            &lt;label for="checkbox5"&gt;&lt;/label&gt;
                        &lt;/span&gt;
                    &lt;/td&gt;
                    &lt;td&gt;Arvind Sharma&lt;/td&gt;
                    &lt;td&gt;<EMAIL>&lt;/td&gt;
                    &lt;td&gt;111 Noida Rd, UP, INDIA&lt;/td&gt;
                    &lt;td&gt;(91) xxx-xxxx&lt;/td&gt;
                    &lt;td&gt;
                        &lt;span class="d-flex"&gt;
                            &lt;a href="#editEmployeeModal" class="edit" data-toggle="modal"&gt;&lt;i class="far fa-edit" data-toggle="tooltip" title="Edit"&gt;&lt;/i&gt;&lt;/a&gt;
                        &lt;a href="#deleteEmployeeModal" class="delete" data-toggle="modal"&gt;&lt;i class="far fa-trash-alt" data-toggle="tooltip" title="Delete"&gt;&#xE872;&lt;/i&gt;&lt;/a&gt;
                        &lt;/span&gt;
                    &lt;/td&gt;
                &lt;/tr&gt; 
            &lt;/tbody&gt;
        &lt;/table&gt;
        &lt;div class="clearfix"&gt;
            &lt;div class="hint-text"&gt;Showing &lt;b&gt;5&lt;/b&gt; out of &lt;b&gt;25&lt;/b&gt; entries&lt;/div&gt;
            &lt;ul class="pagination"&gt;
                &lt;li class="page-item disabled"&gt;&lt;a href="#"&gt;Previous&lt;/a&gt;&lt;/li&gt;
                &lt;li class="page-item"&gt;&lt;a href="#" class="page-link"&gt;1&lt;/a&gt;&lt;/li&gt;
                &lt;li class="page-item"&gt;&lt;a href="#" class="page-link"&gt;2&lt;/a&gt;&lt;/li&gt;
                &lt;li class="page-item active"&gt;&lt;a href="#" class="page-link"&gt;3&lt;/a&gt;&lt;/li&gt;
                &lt;li class="page-item"&gt;&lt;a href="#" class="page-link"&gt;4&lt;/a&gt;&lt;/li&gt;
                &lt;li class="page-item"&gt;&lt;a href="#" class="page-link"&gt;5&lt;/a&gt;&lt;/li&gt;
                &lt;li class="page-item"&gt;&lt;a href="#" class="page-link"&gt;Next&lt;/a&gt;&lt;/li&gt;
            &lt;/ul&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;        
&lt;/div&gt;
&lt;!-- Edit Modal HTML --&gt;
&lt;div id="addEmployeeModal" class="modal fade"&gt;
&lt;div class="modal-dialog"&gt;
    &lt;div class="modal-content"&gt;
        &lt;form&gt;
            &lt;div class="modal-header"&gt;						
                &lt;h4 class="modal-title"&gt;Add Employee&lt;/h4&gt;
                &lt;button type="button" class="close" data-dismiss="modal" aria-hidden="true"&gt;&times;&lt;/button&gt;
            &lt;/div&gt;
            &lt;div class="modal-body"&gt;					
                &lt;div class="form-group"&gt;
                    &lt;label&gt;Name&lt;/label&gt;
                    &lt;input type="text" class="form-control" required&gt;
                &lt;/div&gt;
                &lt;div class="form-group"&gt;
                    &lt;label&gt;Email&lt;/label&gt;
                    &lt;input type="email" class="form-control" required&gt;
                &lt;/div&gt;
                &lt;div class="form-group"&gt;
                    &lt;label&gt;Address&lt;/label&gt;
                    &lt;textarea class="form-control" required&gt;&lt;/textarea&gt;
                &lt;/div&gt;
                &lt;div class="form-group"&gt;
                    &lt;label&gt;Phone&lt;/label&gt;
                    &lt;input type="text" class="form-control" required&gt;
                &lt;/div&gt;					
            &lt;/div&gt;
            &lt;div class="modal-footer"&gt;
                &lt;input type="button" class="btn btn-default" data-dismiss="modal" value="Cancel"&gt;
                &lt;input type="submit" class="btn btn-success" value="Add"&gt;
            &lt;/div&gt;
        &lt;/form&gt;
    &lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;!-- Edit Modal HTML --&gt;
&lt;div id="editEmployeeModal" class="modal fade"&gt;
&lt;div class="modal-dialog"&gt;
    &lt;div class="modal-content"&gt;
        &lt;form&gt;
            &lt;div class="modal-header"&gt;						
                &lt;h4 class="modal-title"&gt;Edit Employee&lt;/h4&gt;
                &lt;button type="button" class="close" data-dismiss="modal" aria-hidden="true"&gt;&times;&lt;/button&gt;
            &lt;/div&gt;
            &lt;div class="modal-body"&gt;					
                &lt;div class="form-group"&gt;
                    &lt;label&gt;Name&lt;/label&gt;
                    &lt;input type="text" class="form-control" required&gt;
                &lt;/div&gt;
                &lt;div class="form-group"&gt;
                    &lt;label&gt;Email&lt;/label&gt;
                    &lt;input type="email" class="form-control" required&gt;
                &lt;/div&gt;
                &lt;div class="form-group"&gt;
                    &lt;label&gt;Address&lt;/label&gt;
                    &lt;textarea class="form-control" required&gt;&lt;/textarea&gt;
                &lt;/div&gt;
                &lt;div class="form-group"&gt;
                    &lt;label&gt;Phone&lt;/label&gt;
                    &lt;input type="text" class="form-control" required&gt;
                &lt;/div&gt;					
            &lt;/div&gt;
            &lt;div class="modal-footer"&gt;
                &lt;input type="button" class="btn btn-default" data-dismiss="modal" value="Cancel"&gt;
                &lt;input type="submit" class="btn btn-info" value="Save"&gt;
            &lt;/div&gt;
        &lt;/form&gt;
    &lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
&lt;!-- Delete Modal HTML --&gt;
&lt;div id="deleteEmployeeModal" class="modal fade"&gt;
&lt;div class="modal-dialog"&gt;
    &lt;div class="modal-content"&gt;
        &lt;form&gt;
            &lt;div class="modal-header"&gt;						
                &lt;h4 class="modal-title"&gt;Delete Employee&lt;/h4&gt;
                &lt;button type="button" class="close" data-dismiss="modal" aria-hidden="true"&gt;&times;&lt;/button&gt;
            &lt;/div&gt;
            &lt;div class="modal-body"&gt;					
                &lt;p&gt;Are you sure you want to delete these Records?&lt;/p&gt;
                &lt;p class="text-warning"&gt;&lt;small&gt;This action cannot be undone.&lt;/small&gt;&lt;/p&gt;
            &lt;/div&gt;
            &lt;div class="modal-footer"&gt;
                &lt;input type="button" class="btn btn-default" data-dismiss="modal" value="Cancel"&gt;
                &lt;input type="submit" class="btn btn-danger" value="Delete"&gt;
            &lt;/div&gt;
        &lt;/form&gt;
    &lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
</code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
body {
    color: #566787;
    background: #f5f5f5;
    
    font-size: 13px;
}
.table-responsive {
    margin: 30px 0;
}
.table-wrapper {
    background: #fff;
    padding: 20px 25px;
    border-radius: 0;
    min-width: 1000px;
    box-shadow: 0 1px 1px rgba(0,0,0,.05);
}
.table-title {        
    padding-bottom: 15px;
    background: #112636;
    color: #fff;
    padding: 16px 30px;
    min-width: 100%;
    margin: -20px -25px 10px;
    border-radius: 0;
}
.table-title h2 {
    margin: 5px 0 0;
    font-size: 24px;
}
.table-title .btn-group {
    float: right;
}
.table-title .btn {
    color: #fff;
    float: right;
    font-size: 13px;
    border: none;
    min-width: 50px;
    border-radius: 0px;
    border: none;
    outline: none !important;
    margin-left: 10px;
    display: flex;
    align-items: center;
}
.table-title .btn i {
    float: left;
    margin-right: 5px;
}
.table-title .btn span {
    float: left;
    margin-top: 2px;
}
table.table tr th, table.table tr td {
    border-color: #e9e9e9;
    padding: 12px 15px;
    vertical-align: middle;
}
table.table tr th:first-child {
    width: 60px;
}
table.table tr th:last-child {
    width: 100px;
}
table.table-striped tbody tr:nth-of-type(odd) {
    background-color: #fcfcfc;
}
table.table-striped.table-hover tbody tr:hover {
    background: #f5f5f5;
}
table.table th i {
    font-size: 13px;
    margin: 0 5px;
    cursor: pointer;
}	
table.table td:last-child i {
    opacity: 0.9;
    margin: 0 5px;
}
table.table td a {
    font-weight: bold;
    color: #566787;
    display: inline-block;
    text-decoration: none;
    outline: none !important;
}
table.table td a:hover {
    color: #2196F3;
}
table.table td a.edit {
    color: #FFC107;
}
table.table td a.delete {
    color: #F44336;
}
table.table td i {
    font-size: 19px;
}
table.table .avatar {
    border-radius: 50%;
    vertical-align: middle;
    margin-right: 10px;
}
.pagination {
    float: right;
    margin: 0 0 5px;
}
.pagination li a {
    border: none;
    font-size: 13px;
    min-width: 30px;
    min-height: 30px;
    color: #999;
    margin: 0 2px;
    line-height: 30px;
    border-radius: 0px !important;
    text-align: center;
    padding: 0 6px;
}

.pagination li i {
    font-size: 16px;
    padding-top: 6px
}
.hint-text {
    float: left;
    margin-top: 10px;
    font-size: 13px;
}    
/* Custom checkbox */
.custom-checkbox {
    position: relative;
}
.custom-checkbox input[type="checkbox"] {    
    opacity: 0;
    position: absolute;
    margin: 5px 0 0 3px;
    z-index: 9;
}
.custom-checkbox label:before{
    width: 18px;
    height: 18px;
}
.custom-checkbox label:before {
    content: '';
    margin-right: 10px;
    display: inline-block;
    vertical-align: text-top;
    background: white;
    border: 1px solid #bbb;
    border-radius: 0px;
    box-sizing: border-box;
    z-index: 2;
}
.custom-checkbox input[type="checkbox"]:checked + label:after {
    content: '';
    position: absolute;
    left: 6px;
    top: 3px;
    width: 6px;
    height: 11px;
    border: solid #000;
    border-width: 0 3px 3px 0;
    transform: inherit;
    z-index: 3;
    transform: rotateZ(45deg);
}
.custom-checkbox input[type="checkbox"]:checked + label:before {
    border-color: #007265;
    background: #007265;
}
.custom-checkbox input[type="checkbox"]:checked + label:after {
    border-color: #fff;
}
.custom-checkbox input[type="checkbox"]:disabled + label:before {
    color: #b8b8b8;
    cursor: auto;
    box-shadow: none;
    background: #ddd;
}
/* Modal styles */
.modal .modal-dialog {
    max-width: 400px;
}

.modal .modal-content {
    border-radius: 0px;
    font-size: 14px;
}
.modal .modal-footer {
    background: #ecf0f1;
    border-radius: 0;
}
.modal .modal-title {
    display: inline-block;
}
.modal .form-control {
    border-radius: 0px;
    box-shadow: none;
    border-color: #dddddd;
}
.modal textarea.form-control {
    resize: vertical;
}
.modal .btn {
    border-radius: 0px;
    min-width: 100px;
}	
.modal form label {
    font-weight: normal;
}	
        </code></pre>
        </div>
        <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">
$(document).ready(function(){
    // Activate tooltip
    $('[data-toggle="tooltip"]').tooltip();
    
    // Select/Deselect checkboxes
    var checkbox = $('table tbody input[type="checkbox"]');
    $("#selectAll").click(function(){
        if(this.checked){
            checkbox.each(function(){
                this.checked = true;                        
            });
        } else{
            checkbox.each(function(){
                this.checked = false;                        
            });
        } 
    });
    checkbox.click(function(){
        if(!this.checked){
            $("#selectAll").prop("checked", false);
        }
    });
});
            </code></pre>
        </div>
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
