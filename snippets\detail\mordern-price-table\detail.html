<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}


    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
          <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
          <i class="fas fa-bars"></i>
        </span>
      </div>
<main role="main">



  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
        <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
                <div>
                    <h4 class="size20 mb-2 pt-2">Pricing Table</h4>                   
                </div>
              </div>   
        </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/mordern-price-table/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
    &lt;div class="container"&gt;
    &lt;div class="text-center"&gt;
        &lt;div class="nav price-tabs" role="tablist"&gt;
            &lt;a class="nav-link active" href="#yearly" role="tab" data-toggle="tab"&gt;Yearly&lt;/a&gt;
            &lt;a class="nav-link" href="#monthly" role="tab" data-toggle="tab"&gt;Monthly&lt;/a&gt;
        &lt;/div&gt;
    &lt;/div&gt;
    &lt;div class="tab-content wow fadeIn" style="visibility: visible; animation-name: fadeIn;"&gt;
        &lt;div role="tabpanel" class="tab-pane fade show active" id="yearly"&gt;
            &lt;div class="row justify-content-center"&gt;
                &lt;div class="col-md-6 col-lg-4 mb-30"&gt;
                    &lt;div class="price-item text-center"&gt;
                        &lt;div class="price-top"&gt;
                            &lt;h4&gt;Personal&lt;/h4&gt;
                            &lt;h2 class="mb-0"&gt;&lt;sup&gt;$&lt;/sup&gt;99&lt;/h2&gt;
                            &lt;span class="text-capitalize"&gt;per year&lt;/span&gt;
                        &lt;/div&gt;
                        &lt;div class="price-content"&gt;
                            &lt;ul class="border-bottom mb-30 mt-md-4 pb-3 text-left"&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Eget erovtiu faucid&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Cras justo odio&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Morbi leo risus&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt;Porta consectetur ac&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt; Vestibulum at eros&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt;Adipisci atque beatae&lt;/span&gt;
                                &lt;/li&gt;
                            &lt;/ul&gt;
                            &lt;a href="#" class="btn btn-custom"&gt;Buy now&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="col-md-6 col-lg-4 mb-30"&gt;
                    &lt;div class="price-item text-center popular"&gt;
                        &lt;div class="price-top"&gt;
                            &lt;h4&gt;Business&lt;/h4&gt;
                            &lt;h2 class="mb-0"&gt;&lt;sup&gt;$&lt;/sup&gt;299&lt;/h2&gt;
                            &lt;span class="text-capitalize"&gt;per year&lt;/span&gt;
                        &lt;/div&gt;
                        &lt;div class="price-content"&gt;
                            &lt;ul class="border-bottom mb-30 mt-md-4 pb-3 text-left"&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Eget erovtiu faucid&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Cras justo odio&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Morbi leo risus&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt;Porta consectetur ac&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt; Vestibulum at eros&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt;Adipisci atque beatae&lt;/span&gt;
                                &lt;/li&gt;
                            &lt;/ul&gt;
                            &lt;a href="#" class="btn btn-custom btn-light"&gt;Buy now&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="col-md-6 col-lg-4 mb-30"&gt;
                    &lt;div class="price-item text-center"&gt;
                        &lt;div class="price-top"&gt;
                            &lt;h4&gt;Enterprise&lt;/h4&gt;
                            &lt;h2 class="mb-0"&gt;&lt;sup&gt;$&lt;/sup&gt;399&lt;/h2&gt;
                            &lt;span class="text-capitalize"&gt;per year&lt;/span&gt;
                        &lt;/div&gt;
                        &lt;div class="price-content"&gt;
                            &lt;ul class="border-bottom mb-30 mt-md-4 pb-3 text-left"&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Eget erovtiu faucid&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Cras justo odio&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Morbi leo risus&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt;Porta consectetur ac&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt; Vestibulum at eros&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt;Adipisci atque beatae&lt;/span&gt;
                                &lt;/li&gt;
                            &lt;/ul&gt;
                            &lt;a href="#" class="btn btn-custom"&gt;Buy now&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;div role="tabpanel" class="tab-pane fade" id="monthly"&gt;
            &lt;div class="row justify-content-center"&gt;
                &lt;div class="col-md-6 col-lg-4 mb-30"&gt;
                    &lt;div class="price-item text-center"&gt;
                        &lt;div class="price-top"&gt;
                            &lt;h4&gt;Personal&lt;/h4&gt;
                            &lt;h2 class="mb-0"&gt;&lt;sup&gt;$&lt;/sup&gt;29&lt;/h2&gt;
                            &lt;span class="text-capitalize"&gt;per month&lt;/span&gt;
                        &lt;/div&gt;
                        &lt;div class="price-content"&gt;
                            &lt;ul class="border-bottom mb-30 mt-md-4 pb-3 text-left"&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Eget erovtiu faucid&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Cras justo odio&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Morbi leo risus&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt;Porta consectetur ac&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt; Vestibulum at eros&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt;Adipisci atque beatae&lt;/span&gt;
                                &lt;/li&gt;
                            &lt;/ul&gt;
                            &lt;a href="#" class="btn btn-custom"&gt;Buy now&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="col-md-6 col-lg-4 mb-30"&gt;
                    &lt;div class="price-item text-center popular"&gt;
                        &lt;div class="price-top"&gt;
                            &lt;h4&gt;Business&lt;/h4&gt;
                            &lt;h2 class="mb-0"&gt;&lt;sup&gt;$&lt;/sup&gt;59&lt;/h2&gt;
                            &lt;span class="text-capitalize"&gt;per month&lt;/span&gt;
                        &lt;/div&gt;
                        &lt;div class="price-content"&gt;
                            &lt;ul class="border-bottom mb-30 mt-md-4 pb-3 text-left"&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Eget erovtiu faucid&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Cras justo odio&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Morbi leo risus&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt;Porta consectetur ac&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt; Vestibulum at eros&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt;Adipisci atque beatae&lt;/span&gt;
                                &lt;/li&gt;
                            &lt;/ul&gt;
                            &lt;a href="#" class="btn btn-custom btn-light"&gt;Buy now&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="col-md-6 col-lg-4 mb-30"&gt;
                    &lt;div class="price-item text-center"&gt;
                        &lt;div class="price-top"&gt;
                            &lt;h4&gt;Enterprise&lt;/h4&gt;
                            &lt;h2 class="mb-0"&gt;&lt;sup&gt;$&lt;/sup&gt;99&lt;/h2&gt;
                            &lt;span class="text-capitalize"&gt;per month&lt;/span&gt;
                        &lt;/div&gt;
                        &lt;div class="price-content"&gt;
                            &lt;ul class="border-bottom mb-30 mt-md-4 pb-3 text-left"&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Eget erovtiu faucid&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Cras justo odio&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-check mr-2"&gt;&lt;/i&gt;
                                    &lt;span class="c-black"&gt;Morbi leo risus&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt;Porta consectetur ac&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt; Vestibulum at eros&lt;/span&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;i class="zmdi zmdi-close mr-2"&gt;&lt;/i&gt;
                                    &lt;span&gt;Adipisci atque beatae&lt;/span&gt;
                                &lt;/li&gt;
                            &lt;/ul&gt;
                            &lt;a href="#" class="btn btn-custom"&gt;Buy now&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
</code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
body{
    background:#eee;
    font-family: 'Fira Sans';
    margin-top:20px;}

.price-tabs {
    background-color: #fff;
    -webkit-box-shadow: 0 5px 20px 0 rgba(39, 39, 39, 0.1);
            box-shadow: 0 5px 20px 0 rgba(39, 39, 39, 0.1);
    display: inline-block;
    padding: 7px;
    border-radius: 40px;
    border: 1px solid #007265;
    margin-bottom: 45px;
}

@media (min-width: 768px) {
    .price-tabs {
    margin-bottom: 60px;
    }
}

.price-tabs .nav-link {
    color: #007265;
    font-weight: 500;
    font-family: "Fira Sans", sans-serif;
    font-size: 16px;
    padding: 12px 35px;
    display: inline-block;
    text-transform: capitalize;
    border-radius: 40px;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

@media (min-width: 768px) {
    .price-tabs .nav-link {
    padding: 12px 40px;
    }
}

.price-tabs .nav-link.active {
    background-color: #007265;
    color: #fff;
}

.price-item {
    background-color: #fff;
    -webkit-box-shadow: 0 5px 30px 0 rgba(39, 39, 39, 0.15);
            box-shadow: 0 5px 30px 0 rgba(39, 39, 39, 0.15);
    border-radius: 10px;
}

@media (min-width: 768px) {
    .price-item {
    margin: 0 20px;
    padding-top: 20px;
    }
}

.price-item .price-top {
    -webkit-box-shadow: 0 5px 30px 0 rgba(39, 39, 39, 0.15);
            box-shadow: 0 5px 30px 0 rgba(39, 39, 39, 0.15);
    padding: 50px 0 25px;
    background-color: #007265;
    border-radius: 10px;
    position: relative;
    z-index: 0;
    margin-bottom: 33px;
}

@media (min-width: 768px) {
    .price-item .price-top {
    margin: 0 -20px;
    border-radius: 20px;
    }
}

.price-item .price-top:after {
    height: 50px;
    width: 100%;
    border-radius: 0 0 10px 10px;
    background-color: #007265;
    position: absolute;
    content: '';
    left: 0;
    bottom: -17px;
    z-index: -1;
    -webkit-transform: skewY(5deg);
            transform: skewY(5deg);
    -webkit-box-shadow: 0 5px 10px 0 rgba(113, 113, 113, 0.15);
            box-shadow: 0 5px 10px 0 rgba(113, 113, 113, 0.15);
}

@media (min-width: 768px) {
    .price-item .price-top:after {
    border-radius: 0 0 20px 20px;
    }
}

.price-item .price-top * {
    color: #fff;
}

.price-item .price-top h2 {
    font-weight: 700;
}

.price-item .price-top h2 sup {
    top: 13px;
    left: -5px;
    font-size: 0.35em;
    font-weight: 500;
    vertical-align: top;
}

.price-item .price-content {
    padding: 30px;
    padding-bottom: 40px;
}

.price-item .price-content li {
    position: relative;
    margin-bottom: 15px;
    margin-left: 10px;
    margin-right: 10px;
    text-align: center;
}

@media (min-width: 992px) {
    .price-item .price-content li {
    padding-left: 28px;
    text-align: left;
    }
}

@media (min-width: 992px) {
    .price-item .price-content li i {
    position: absolute;
    left: 0;
    top: 3px;
    }
}

.price-item .price-content .zmdi-check {
    color: #28a745;
}

.price-item .price-content .zmdi-close {
    color: #f00;
}

.popular {
    background-color: #007265;
}

.popular .price-top {
    background-color: #fff;
}

.popular .price-top:after {
    background-color: #fff;
}

.popular .price-top h4 {
    color: #101f41;
}

.popular .price-top h2, .popular .price-top span, .popular .price-top sup {
    color: #007265;
}

.popular .price-content ul *,
.popular .price-content ul .zmdi-close, .popular .price-content ul .zmdi-check {
    color: #fff !important;
}        
</code></pre>
        </div>
        <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">

            </code></pre>
        </div>
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
