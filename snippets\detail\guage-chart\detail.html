<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Component Library</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">
      <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Guage Chart</h4>
              <div class="mb-2">
                <a
                  href="./downloads/guage-chart.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/guage-chart/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
              <pre class="line-numbers">
<code class="language-markup">&lt;div id="chartdiv"&gt;&lt;/div&gt;</code>
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">#chartdiv {
  width: 100%;
  height: 500px;
}

</code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">var root = am5.Root.new("chartdiv");
                root.setThemes([
                    am5themes_Animated.new(root)
                ]);
                var chart = root.container.children.push(
                    am5radar.RadarChart.new(root, {
                        startAngle: -180,
                        endAngle: 0
                    })
                );
                var axisRenderer = am5radar.AxisRendererCircular.new(root, {
                    innerRadius: -30
                });
                axisRenderer.grid.template.setAll({
                    forceHidden: true
                });
                axisRenderer.ticks.template.setAll({
                    visible: true,
                    strokeOpacity: 1
                });
                var xAxis = chart.xAxes.push(
                    am5xy.ValueAxis.new(root, {
                        min: 0,
                        max: 100,
                        textType: "radial",
                        strictMinMax: true,
                        renderer: axisRenderer
                    })
                );
                // Responsive font size
                axisRenderer.labels.template.onPrivate("radius", function (size) {
                    var radius = axisRenderer.getPrivate("radius");
                    var fontSize = Math.min(20, radius / 10);
                    axisRenderer.labels.template.set("fontSize", fontSize);
                });
                var axisDataItem = xAxis.makeDataItem({});
        
                var clockHand = am5radar.ClockHand.new(root, {
                    pinRadius: am5.percent(7),
                    radius: am5.percent(80),
                    bottomWidth: 20
                });
                var bullet = axisDataItem.set("bullet", am5xy.AxisBullet.new(root, {
                    sprite: clockHand
                }));
                xAxis.createAxisRange(axisDataItem);
                var label = chart.radarContainer.children.push(
                    am5.Label.new(root, {
                        fill: am5.color(0x000000),
                        centerX: am5.percent(50),
                        textAlign: "center",
                        centerY: am5.percent(50),
                        fontSize: "2em"
                    })
                );
                axisDataItem.set("value", 50);
                bullet.get("sprite").on("rotation", function () {
                    var value = axisDataItem.get("value");
                    var text = Math.round(axisDataItem.get("value")).toString();
                    var fill = am5.color(0x000000);
                    xAxis.axisRanges.each(function (axisRange) {
                        if (value > axisRange.get("value") && value <= axisRange.get("endValue")) {
                            fill = axisRange.get("axisFill").get("fill");
                        }
                    });
                    clockHand.pin.animate({
                        key: "fill",
                        to: fill,
                        duration: 500,
                        easing: am5.ease.out(am5.ease.cubic)
                    });
                    clockHand.hand.animate({
                        key: "fill",
                        to: fill,
                        duration: 500,
                        easing: am5.ease.out(am5.ease.cubic)
                    });
                });
               
        
                chart.bulletsContainer.set("mask", undefined);
        
                var bandsData = [{
                    title: "Safe",
                    color: am5.color(0x29BB3C),
                    from: 0,
                    to: 10
                }, {
                    title: "Low risk",
                    color: am5.color(0xC7C01A),
                    from: 10,
                    to: 20
                }, {
                    title: "Medium risk",
                    color: am5.color(0xFBC20C),
                    from: 20,
                    to: 40
                }, {
                    title: "High risk",
                    color: am5.color(0xCC5B0B),
                    from: 40,
                    to: 70
                }, {
                    title: "Danger",
                    color: am5.color(0xBB390A),
                    from: 70,
                    to: 100
                }];
        
                // Create tooltip for bands
                var tooltip = am5.Tooltip.new(root, {
                    getFillFromSprite: false,
                    autoTextColor: false
                });
        
                tooltip.get("background").set("fill", am5.color(0x000000));
                tooltip.label.set("fill", am5.color(0xffffff));
        
                am5.array.each(bandsData, function (data) {
                    var axisRange = xAxis.createAxisRange(xAxis.makeDataItem({}));
                    var tick = axisRange.get("tick");
                    if (tick) {
                        tick.set("visible", false);
                    }
        
                    axisRange.setAll({
                        value: data.from,
                        endValue: data.to
                    });
        
                    axisRange.get("axisFill").setAll({
                        visible: true,
                        fill: data.color,
                        tooltipText: data.title,
                        fillOpacity: 30,
                        tooltip: tooltip
                    });
        
                    axisRange.get("label").setAll({
                        text: data.title,
                        inside: true,
                        tooltipPosition: "pointer",
                        forceInactive: true,
                        fill: root.interfaceColors.get("background") && am5.color(0x000000),
                        // fontWeight: "500",
                        maxWidth: 20,
                        oversizedBehavior: "truncate"
                    });
        
                    var label = axisRange.get("label");
        
                    label.onPrivate("radius", function () {
                        label.set("maxWidth", label.get("maxWidth"));
                    });
        
                    // Set maxWidth adapter on circular labels
                    label.adapters.add("maxWidth", function (maxWidth, target) {
                        var dataItem = target.dataItem;
                        if (dataItem) {
                            var value = dataItem.get("value", 0);
                            var endValue = dataItem.get("endValue", 0);
        
                            var position = xAxis.valueToPosition(value);
                            var endPosition = xAxis.valueToPosition(endValue);
        
                            var angle = axisRenderer.positionToAngle(position);
                            var endAngle = axisRenderer.positionToAngle(endPosition);
        
                            var radius = target.getPrivate("radius", 0);
        
                            var arcLength = 2 * Math.PI * radius * Math.abs(endAngle - angle) / 360 - 10;
        
                            if (arcLength < 40) {
                                target.set("forceHidden", true);
                            } else {
                                target.set("forceHidden", false);
                            }
        
                            return arcLength;
                        }
                    });
        
                });
        
                chart.appear(1000, 100);

            </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
