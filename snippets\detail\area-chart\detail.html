<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Area Chart</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Area chart</h4>
              <div class="mb-2">
                <a
                  href="./downloads/area-chart.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/area-chart/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
              <pre class="line-numbers">
<code class="language-markup">&lt;div id="chartdiv"&gt;&lt;/div&gt;</code>
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">#chartdiv {
                width: 100%;
                height: 500px;
            }</code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">
                var root = am5.Root.new("chartdiv");
                root.setThemes([
                    am5themes_Animated.new(root)
                ]);
                root.dateFormatter.setAll({
                    dateFormat: "yyyy",
                    dateFields: ["valueX"]
                });
                var data = [{
                    "date": "2012-07-27",
                    "value": 13
                }, {
                    "date": "2012-07-28",
                    "value": 11
                }, {
                    "date": "2012-07-29",
                    "value": 15
                }, {
                    "date": "2012-07-30",
                    "value": 16
                }, {
                    "date": "2012-07-31",
                    "value": 18
                }, {
                    "date": "2012-08-01",
                    "value": 13
                }, {
                    "date": "2012-08-02",
                    "value": 22
                }, {
                    "date": "2012-08-03",
                    "value": 23
                }, {
                    "date": "2012-08-04",
                    "value": 20
                }, {
                    "date": "2012-08-05",
                    "value": 17
                }, {
                    "date": "2012-08-06",
                    "value": 16
                }, {
                    "date": "2012-08-07",
                    "value": 18
                }, {
                    "date": "2012-08-08",
                    "value": 21
                }, {
                    "date": "2012-08-09",
                    "value": 26
                }, {
                    "date": "2012-08-10",
                    "value": 24
                }, {
                    "date": "2012-08-11",
                    "value": 29
                }, {
                    "date": "2012-08-12",
                    "value": 32
                }, {
                    "date": "2012-08-13",
                    "value": 18
                }, {
                    "date": "2012-08-14",
                    "value": 24
                }, {
                    "date": "2012-08-15",
                    "value": 22
                }, {
                    "date": "2012-08-16",
                    "value": 18
                }, {
                    "date": "2012-08-17",
                    "value": 19
                }, {
                    "date": "2012-08-18",
                    "value": 14
                }, {
                    "date": "2012-08-19",
                    "value": 15
                }, {
                    "date": "2012-08-20",
                    "value": 12
                }, {
                    "date": "2012-08-21",
                    "value": 8
                }, {
                    "date": "2012-08-22",
                    "value": 9
                }, {
                    "date": "2012-08-23",
                    "value": 8
                }, {
                    "date": "2012-08-24",
                    "value": 7
                }, {
                    "date": "2012-08-25",
                    "value": 5
                }, {
                    "date": "2012-08-26",
                    "value": 11
                }, {
                    "date": "2012-08-27",
                    "value": 13
                }, {
                    "date": "2012-08-28",
                    "value": 18
                }, {
                    "date": "2012-08-29",
                    "value": 20
                }, {
                    "date": "2012-08-30",
                    "value": 29
                }, {
                    "date": "2012-08-31",
                    "value": 33
                }, {
                    "date": "2012-09-01",
                    "value": 42
                }, {
                    "date": "2012-09-02",
                    "value": 35
                }, {
                    "date": "2012-09-03",
                    "value": 31
                }, {
                    "date": "2012-09-04",
                    "value": 47
                }, {
                    "date": "2012-09-05",
                    "value": 52
                }, {
                    "date": "2012-09-06",
                    "value": 46
                }, {
                    "date": "2012-09-07",
                    "value": 41
                }, {
                    "date": "2012-09-08",
                    "value": 43
                }, {
                    "date": "2012-09-09",
                    "value": 40
                }, {
                    "date": "2012-09-10",
                    "value": 39
                }, {
                    "date": "2012-09-11",
                    "value": 34
                }, {
                    "date": "2012-09-12",
                    "value": 29
                }, {
                    "date": "2012-09-13",
                    "value": 34
                }, {
                    "date": "2012-09-14",
                    "value": 37
                }, {
                    "date": "2012-09-15",
                    "value": 42
                }, {
                    "date": "2012-09-16",
                    "value": 49
                }, {
                    "date": "2012-09-17",
                    "value": 46
                }, {
                    "date": "2012-09-18",
                    "value": 47
                }, {
                    "date": "2012-09-19",
                    "value": 55
                }, {
                    "date": "2012-09-20",
                    "value": 59
                }, {
                    "date": "2012-09-21",
                    "value": 58
                }, {
                    "date": "2012-09-22",
                    "value": 57
                }, {
                    "date": "2012-09-23",
                    "value": 61
                }, {
                    "date": "2012-09-24",
                    "value": 59
                }, {
                    "date": "2012-09-25",
                    "value": 67
                }, {
                    "date": "2012-09-26",
                    "value": 65
                }, {
                    "date": "2012-09-27",
                    "value": 61
                }, {
                    "date": "2012-09-28",
                    "value": 66
                }, {
                    "date": "2012-09-29",
                    "value": 69
                }, {
                    "date": "2012-09-30",
                    "value": 71
                }, {
                    "date": "2012-10-01",
                    "value": 67
                }, {
                    "date": "2012-10-02",
                    "value": 63
                }, {
                    "date": "2012-10-03",
                    "value": 46
                }, {
                    "date": "2012-10-04",
                    "value": 32
                }, {
                    "date": "2012-10-05",
                    "value": 21
                }, {
                    "date": "2012-10-06",
                    "value": 18
                }, {
                    "date": "2012-10-07",
                    "value": 21
                }, {
                    "date": "2012-10-08",
                    "value": 28
                }, {
                    "date": "2012-10-09",
                    "value": 27
                }, {
                    "date": "2012-10-10",
                    "value": 36
                }, {
                    "date": "2012-10-11",
                    "value": 33
                }, {
                    "date": "2012-10-12",
                    "value": 31
                }, {
                    "date": "2012-10-13",
                    "value": 30
                }, {
                    "date": "2012-10-14",
                    "value": 34
                }, {
                    "date": "2012-10-15",
                    "value": 38
                }, {
                    "date": "2012-10-16",
                    "value": 37
                }, {
                    "date": "2012-10-17",
                    "value": 44
                }, {
                    "date": "2012-10-18",
                    "value": 49
                }, {
                    "date": "2012-10-19",
                    "value": 53
                }, {
                    "date": "2012-10-20",
                    "value": 57
                }, {
                    "date": "2012-10-21",
                    "value": 60
                }, {
                    "date": "2012-10-22",
                    "value": 61
                }, {
                    "date": "2012-10-23",
                    "value": 69
                }, {
                    "date": "2012-10-24",
                    "value": 67
                }, {
                    "date": "2012-10-25",
                    "value": 72
                }, {
                    "date": "2012-10-26",
                    "value": 77
                }, {
                    "date": "2012-10-27",
                    "value": 75
                }, {
                    "date": "2012-10-28",
                    "value": 70
                }, {
                    "date": "2012-10-29",
                    "value": 72
                }, {
                    "date": "2012-10-30",
                    "value": 70
                }, {
                    "date": "2012-10-31",
                    "value": 72
                }, {
                    "date": "2012-11-01",
                    "value": 73
                }, {
                    "date": "2012-11-02",
                    "value": 67
                }, {
                    "date": "2012-11-03",
                    "value": 68
                }, {
                    "date": "2012-11-04",
                    "value": 65
                }, {
                    "date": "2012-11-05",
                    "value": 71
                }, {
                    "date": "2012-11-06",
                    "value": 75
                }, {
                    "date": "2012-11-07",
                    "value": 74
                }, {
                    "date": "2012-11-08",
                    "value": 71
                }, {
                    "date": "2012-11-09",
                    "value": 76
                }, {
                    "date": "2012-11-10",
                    "value": 77
                }, {
                    "date": "2012-11-11",
                    "value": 81
                }, {
                    "date": "2012-11-12",
                    "value": 83
                }, {
                    "date": "2012-11-13",
                    "value": 80
                }, {
                    "date": "2012-11-14",
                    "value": 81
                }, {
                    "date": "2012-11-15",
                    "value": 87
                }, {
                    "date": "2012-11-16",
                    "value": 82
                }, {
                    "date": "2012-11-17",
                    "value": 86
                }, {
                    "date": "2012-11-18",
                    "value": 80
                }, {
                    "date": "2012-11-19",
                    "value": 87
                }, {
                    "date": "2012-11-20",
                    "value": 83
                }, {
                    "date": "2012-11-21",
                    "value": 85
                }, {
                    "date": "2012-11-22",
                    "value": 84
                }, {
                    "date": "2012-11-23",
                    "value": 82
                }, {
                    "date": "2012-11-24",
                    "value": 73
                }, {
                    "date": "2012-11-25",
                    "value": 71
                }, {
                    "date": "2012-11-26",
                    "value": 75
                }, {
                    "date": "2012-11-27",
                    "value": 79
                }, {
                    "date": "2012-11-28",
                    "value": 70
                }, {
                    "date": "2012-11-29",
                    "value": 73
                }, {
                    "date": "2012-11-30",
                    "value": 61
                }, {
                    "date": "2012-12-01",
                    "value": 62
                }, {
                    "date": "2012-12-02",
                    "value": 66
                }, {
                    "date": "2012-12-03",
                    "value": 65
                }, {
                    "date": "2012-12-04",
                    "value": 73
                }, {
                    "date": "2012-12-05",
                    "value": 79
                }, {
                    "date": "2012-12-06",
                    "value": 78
                }, {
                    "date": "2012-12-07",
                    "value": 78
                }, {
                    "date": "2012-12-08",
                    "value": 78
                }, {
                    "date": "2012-12-09",
                    "value": 74
                }, {
                    "date": "2012-12-10",
                    "value": 73
                }, {
                    "date": "2012-12-11",
                    "value": 75
                }, {
                    "date": "2012-12-12",
                    "value": 70
                }, {
                    "date": "2012-12-13",
                    "value": 77
                }, {
                    "date": "2012-12-14",
                    "value": 67
                }, {
                    "date": "2012-12-15",
                    "value": 62
                }, {
                    "date": "2012-12-16",
                    "value": 64
                }, {
                    "date": "2012-12-17",
                    "value": 61
                }, {
                    "date": "2012-12-18",
                    "value": 59
                }, {
                    "date": "2012-12-19",
                    "value": 53
                }, {
                    "date": "2012-12-20",
                    "value": 54
                }, {
                    "date": "2012-12-21",
                    "value": 56
                }, {
                    "date": "2012-12-22",
                    "value": 59
                }, {
                    "date": "2012-12-23",
                    "value": 58
                }, {
                    "date": "2012-12-24",
                    "value": 55
                }, {
                    "date": "2012-12-25",
                    "value": 52
                }, {
                    "date": "2012-12-26",
                    "value": 54
                }, {
                    "date": "2012-12-27",
                    "value": 50
                }, {
                    "date": "2012-12-28",
                    "value": 50
                }, {
                    "date": "2012-12-29",
                    "value": 51
                }, {
                    "date": "2012-12-30",
                    "value": 52
                }, {
                    "date": "2012-12-31",
                    "value": 58
                }, {
                    "date": "2013-01-01",
                    "value": 60
                }, {
                    "date": "2013-01-02",
                    "value": 67
                }, {
                    "date": "2013-01-03",
                    "value": 64
                }, {
                    "date": "2013-01-04",
                    "value": 66
                }, {
                    "date": "2013-01-05",
                    "value": 60
                }, {
                    "date": "2013-01-06",
                    "value": 63
                }, {
                    "date": "2013-01-07",
                    "value": 61
                }, {
                    "date": "2013-01-08",
                    "value": 60
                }, {
                    "date": "2013-01-09",
                    "value": 65
                }, {
                    "date": "2013-01-10",
                    "value": 75
                }, {
                    "date": "2013-01-11",
                    "value": 77
                }, {
                    "date": "2013-01-12",
                    "value": 78
                }, {
                    "date": "2013-01-13",
                    "value": 70
                }, {
                    "date": "2013-01-14",
                    "value": 70
                }, {
                    "date": "2013-01-15",
                    "value": 73
                }, {
                    "date": "2013-01-16",
                    "value": 71
                }, {
                    "date": "2013-01-17",
                    "value": 74
                }, {
                    "date": "2013-01-18",
                    "value": 78
                }, {
                    "date": "2013-01-19",
                    "value": 85
                }, {
                    "date": "2013-01-20",
                    "value": 82
                }, {
                    "date": "2013-01-21",
                    "value": 83
                }, {
                    "date": "2013-01-22",
                    "value": 88
                }, {
                    "date": "2013-01-23",
                    "value": 85
                }, {
                    "date": "2013-01-24",
                    "value": 85
                }, {
                    "date": "2013-01-25",
                    "value": 80
                }, {
                    "date": "2013-01-26",
                    "value": 87
                }, {
                    "date": "2013-01-27",
                    "value": 84
                }, {
                    "date": "2013-01-28",
                    "value": 83
                }, {
                    "date": "2013-01-29",
                    "value": 84
                }, {
                    "date": "2013-01-30",
                    "value": 81
                }];
        
                var chart = root.container.children.push(am5xy.XYChart.new(root, {
                    focusable: true,
                    panX: true,
                    panY: true,
                    wheelX: "panX",
                    wheelY: "zoomX",
                    pinchZoomX: true,
                    layout: root.verticalLayout,
                }));
                chart.children.unshift(
                    am5.Label.new(root, {
                        text: "Area chart title",
                        fontSize: 12,
                        fontWeight: "bold",
                        textAlign: "center",
                        x: am5.percent(50),
                        centerX: am5.percent(50),
                    })
                );
        
                var easing = am5.ease.linear;
                var xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
                    maxDeviation: 0.1,
                    groupData: false,
                    baseInterval: {
                        timeUnit: "day",
                        count: 1
                    },
                    renderer: am5xy.AxisRendererX.new(root, {
                        strokeOpacity: 1,
                        strokeWidth: 1,
                    }),
                    tooltip: am5.Tooltip.new(root, {})
                }));
        
                var xRenderer = xAxis.get("renderer");
                xRenderer.labels.template.setAll({
                    rotation: -60,
                    centerY: am5.p50,
                });
                xRenderer.grid.template.setAll({
                    stroke: am5.color(0xfff),
                    strokeWidth: 0,
                    strokeOpacity: 0,
                });
                xRenderer.labels.template.setAll({
                    fontSize: "12px",
                });
        
        
                var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                    maxDeviation: 0.2,
                    renderer: am5xy.AxisRendererY.new(root, {
                        strokeOpacity: 1,
                        strokeWidth: 1,
                    })
                }));
                var yRenderer = yAxis.get("renderer");
                yRenderer.grid.template.setAll({
                    stroke: am5.color(0xfff),
                    strokeWidth: 0,
                    strokeOpacity: 0,
                });
                yRenderer.labels.template.setAll({
                    fontSize: "12px",
                });
                yAxis.children.unshift(
                    am5.Label.new(root, {
                        text: "Value axis title",
                        textAlign: "center",
                        y: am5.p50,
                        rotation: -90,
                        fontWeight: "bold",
                        fontSize: 12,
                    })
                );
        
                var series = chart.series.push(am5xy.LineSeries.new(root, {
                    minBulletDistance: 10,
                    connect: false,
                    xAxis: xAxis,
                    yAxis: yAxis,
                    valueYField: "value",
                    valueXField: "date",
                    fill: am5.color(0x00b19c),
                    tooltip: am5.Tooltip.new(root, {
                        pointerOrientation: "horizontal",
                        labelText: "{valueY}"
                    })
                }));
        
                series.fills.template.setAll({
        
                    fillOpacity: 0.6,
                    visible: true
                });
        
                series.strokes.template.setAll({
                    strokeWidth: 2
                });
                series.data.processor = am5.DataProcessor.new(root, {
                    dateFormat: "yyyy-MM-dd",
                    dateFields: ["date"]
                });
        
                series.data.setAll(data);
        
        
                series.bullets.push(function () {
                    var circle = am5.Circle.new(root, {
                        radius: 4,
                        fill: root.interfaceColors.get("background"),
                        stroke: series.get("fill"),
                        strokeWidth: 2
                    })
        
                    return am5.Bullet.new(root, {
                        sprite: circle
                    })
                });
                var cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
                    xAxis: xAxis,
                    behavior: "none"
                }));
                cursor.lineY.set("visible", false);
                chart.appear(1000, 100);
            </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
