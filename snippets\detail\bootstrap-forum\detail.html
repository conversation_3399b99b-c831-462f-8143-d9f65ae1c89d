
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}
    


    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
          <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
          <i class="fas fa-bars"></i>
        </span>
      </div>

<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
        <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
                <div>
                    <h4 class="size20 mb-2 pt-2">Forum Template</h4>                   
                </div>
              </div>   
        </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/bootstrap-forum/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
  &lt;link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0-2/css/all.min.css" integrity="sha256-46r060N2LrChLLb5zowXQ72/iKKNiw/lAmygmHExk/o=" crossorigin="anonymous" /&gt;
  &lt;div class="container"&gt;
  &lt;div class="main-body p-0"&gt;
      &lt;div class="inner-wrapper"&gt;
          &lt;!-- Inner sidebar --&gt;
          &lt;div class="inner-sidebar"&gt;
              &lt;!-- Inner sidebar header --&gt;
              &lt;div class="inner-sidebar-header justify-content-center"&gt;
                  &lt;button class="btn btn-primary has-icon btn-block" type="button" data-toggle="modal" data-target="#threadModal"&gt;
                      &lt;svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus mr-2"&gt;
                          &lt;line x1="12" y1="5" x2="12" y2="19"&gt;&lt;/line&gt;
                          &lt;line x1="5" y1="12" x2="19" y2="12"&gt;&lt;/line&gt;
                      &lt;/svg&gt;
                      NEW DISCUSSION
                  &lt;/button&gt;
              &lt;/div&gt;
              &lt;!-- /Inner sidebar header --&gt;
  
              &lt;!-- Inner sidebar body --&gt;
              &lt;div class="inner-sidebar-body p-0"&gt;
                  &lt;div class="p-3 h-100" data-simplebar="init"&gt;
                      &lt;div class="simplebar-wrapper" style="margin: -16px;"&gt;
                          &lt;div class="simplebar-height-auto-observer-wrapper"&gt;&lt;div class="simplebar-height-auto-observer"&gt;&lt;/div&gt;&lt;/div&gt;
                          &lt;div class="simplebar-mask"&gt;
                              &lt;div class="simplebar-offset" style="right: 0px; bottom: 0px;"&gt;
                                  &lt;div class="simplebar-content-wrapper" style="height: 100%; overflow: hidden scroll;"&gt;
                                      &lt;div class="simplebar-content" style="padding: 16px;"&gt;
                                          &lt;nav class="nav nav-pills nav-gap-y-1 flex-column"&gt;
                                              &lt;a href="javascript:void(0)" class="nav-link nav-link-faded has-icon active"&gt;All Threads&lt;/a&gt;
                                              &lt;a href="javascript:void(0)" class="nav-link nav-link-faded has-icon"&gt;Popular this week&lt;/a&gt;
                                              &lt;a href="javascript:void(0)" class="nav-link nav-link-faded has-icon"&gt;Popular all time&lt;/a&gt;
                                              &lt;a href="javascript:void(0)" class="nav-link nav-link-faded has-icon"&gt;Solved&lt;/a&gt;
                                              &lt;a href="javascript:void(0)" class="nav-link nav-link-faded has-icon"&gt;Unsolved&lt;/a&gt;
                                              &lt;a href="javascript:void(0)" class="nav-link nav-link-faded has-icon"&gt;No replies yet&lt;/a&gt;
                                          &lt;/nav&gt;
                                      &lt;/div&gt;
                                  &lt;/div&gt;
                              &lt;/div&gt;
                          &lt;/div&gt;
                          &lt;div class="simplebar-placeholder" style="width: 234px; height: 292px;"&gt;&lt;/div&gt;
                      &lt;/div&gt;
                      &lt;div class="simplebar-track simplebar-horizontal" style="visibility: hidden;"&gt;&lt;div class="simplebar-scrollbar" style="width: 0px; display: none;"&gt;&lt;/div&gt;&lt;/div&gt;
                      &lt;div class="simplebar-track simplebar-vertical" style="visibility: visible;"&gt;&lt;div class="simplebar-scrollbar" style="height: 151px; display: block; transform: translate3d(0px, 0px, 0px);"&gt;&lt;/div&gt;&lt;/div&gt;
                  &lt;/div&gt;
              &lt;/div&gt;
              &lt;!-- /Inner sidebar body --&gt;
          &lt;/div&gt;
          &lt;!-- /Inner sidebar --&gt;
  
          &lt;!-- Inner main --&gt;
          &lt;div class="inner-main"&gt;
              &lt;!-- Inner main header --&gt;
              &lt;div class="inner-main-header"&gt;
                  &lt;a class="nav-link nav-icon rounded-circle nav-link-faded mr-3 d-md-none" href="#" data-toggle="inner-sidebar"&gt;&lt;i class="material-icons"&gt;arrow_forward_ios&lt;/i&gt;&lt;/a&gt;
                  &lt;select class="custom-select custom-select-sm w-auto mr-1"&gt;
                      &lt;option selected=""&gt;Latest&lt;/option&gt;
                      &lt;option value="1"&gt;Popular&lt;/option&gt;
                      &lt;option value="3"&gt;Solved&lt;/option&gt;
                      &lt;option value="3"&gt;Unsolved&lt;/option&gt;
                      &lt;option value="3"&gt;No Replies Yet&lt;/option&gt;
                  &lt;/select&gt;
                  &lt;span class="input-icon input-icon-sm ml-auto w-auto"&gt;
                      &lt;input type="text" class="form-control form-control-sm bg-gray-200 border-gray-200 shadow-none mb-4 mt-4" placeholder="Search forum" /&gt;
                  &lt;/span&gt;
              &lt;/div&gt;
              &lt;!-- /Inner main header --&gt;
  
              &lt;!-- Inner main body --&gt;
  
              &lt;!-- Forum List --&gt;
              &lt;div class="inner-main-body p-2 p-sm-3 collapse forum-content show"&gt;
                  &lt;div class="card mb-2"&gt;
                      &lt;div class="card-body p-2 p-sm-3"&gt;
                          &lt;div class="media forum-item"&gt;
                              &lt;a href="#" data-toggle="collapse" data-target=".forum-content"&gt;&lt;img src="https://picsum.photos/seed/picsum/315/315" class="mr-3 rounded-circle" width="50" alt="User" /&gt;&lt;/a&gt;
                              &lt;div class="media-body"&gt;
                                  &lt;h6&gt;&lt;a href="#" data-toggle="collapse" data-target=".forum-content" class="text-body"&gt;Realtime fetching data&lt;/a&gt;&lt;/h6&gt;
                                  &lt;p&gt;
                                      lorem ipsum dolor sit amet lorem ipsum dolor sit amet lorem ipsum dolor sit amet
                                  &lt;/p&gt;
                                  &lt;p class="text-muted"&gt;&lt;a href="javascript:void(0)"&gt;Sneha Issar&lt;/a&gt; replied &lt;span class="text-secondary font-weight-bold"&gt;13 minutes ago&lt;/span&gt;&lt;/p&gt;
                              &lt;/div&gt;
                              &lt;div class="text-muted small text-center align-self-center"&gt;
                                  &lt;span class="d-none d-sm-inline-block"&gt;&lt;i class="far fa-eye"&gt;&lt;/i&gt; 19&lt;/span&gt;
                                  &lt;span&gt;&lt;i class="far fa-comment ml-2"&gt;&lt;/i&gt; 3&lt;/span&gt;
                              &lt;/div&gt;
                          &lt;/div&gt;
                      &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div class="card mb-2"&gt;
                      &lt;div class="card-body p-2 p-sm-3"&gt;
                          &lt;div class="media forum-item"&gt;
                              &lt;a href="#" data-toggle="collapse" data-target=".forum-content"&gt;&lt;img src="https://picsum.photos/seed/picsum/315/315" class="mr-3 rounded-circle" width="50" alt="User" /&gt;&lt;/a&gt;
                              &lt;div class="media-body"&gt;
                                  &lt;h6&gt;&lt;a href="#" data-toggle="collapse" data-target=".forum-content" class="text-body"&gt;Laravel 7 database backup&lt;/a&gt;&lt;/h6&gt;
                                  &lt;p&gt;
                                      lorem ipsum dolor sit amet lorem ipsum dolor sit amet lorem ipsum dolor sit amet
                                  &lt;/p&gt;
                                  &lt;p class="text-muted"&gt;&lt;a href="javascript:void(0)"&gt;Vimal Thapliyal&lt;/a&gt; replied &lt;span class="text-secondary font-weight-bold"&gt;3 hours ago&lt;/span&gt;&lt;/p&gt;
                              &lt;/div&gt;
                              &lt;div class="text-muted small text-center align-self-center"&gt;
                                  &lt;span class="d-none d-sm-inline-block"&gt;&lt;i class="far fa-eye"&gt;&lt;/i&gt; 18&lt;/span&gt;
                                  &lt;span&gt;&lt;i class="far fa-comment ml-2"&gt;&lt;/i&gt; 1&lt;/span&gt;
                              &lt;/div&gt;
                          &lt;/div&gt;
                      &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div class="card mb-2"&gt;
                      &lt;div class="card-body p-2 p-sm-3"&gt;
                          &lt;div class="media forum-item"&gt;
                              &lt;a href="#" data-toggle="collapse" data-target=".forum-content"&gt;&lt;img src="https://picsum.photos/seed/picsum/315/315" class="mr-3 rounded-circle" width="50" alt="User" /&gt;&lt;/a&gt;
                              &lt;div class="media-body"&gt;
                                  &lt;h6&gt;&lt;a href="#" data-toggle="collapse" data-target=".forum-content" class="text-body"&gt;Http client post raw content&lt;/a&gt;&lt;/h6&gt;
                                  &lt;p&gt;
                                      lorem ipsum dolor sit amet lorem ipsum dolor sit amet lorem ipsum dolor sit amet
                                  &lt;/p&gt;
                                  &lt;p class="text-muted"&gt;&lt;a href="javascript:void(0)"&gt;Pankaj&lt;/a&gt; replied &lt;span class="text-secondary font-weight-bold"&gt;7 hours ago&lt;/span&gt;&lt;/p&gt;
                              &lt;/div&gt;
                              &lt;div class="text-muted small text-center align-self-center"&gt;
                                  &lt;span class="d-none d-sm-inline-block"&gt;&lt;i class="far fa-eye"&gt;&lt;/i&gt; 32&lt;/span&gt;
                                  &lt;span&gt;&lt;i class="far fa-comment ml-2"&gt;&lt;/i&gt; 2&lt;/span&gt;
                              &lt;/div&gt;
                          &lt;/div&gt;
                      &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div class="card mb-2"&gt;
                      &lt;div class="card-body p-2 p-sm-3"&gt;
                          &lt;div class="media forum-item"&gt;
                              &lt;a href="#" data-toggle="collapse" data-target=".forum-content"&gt;&lt;img src="https://picsum.photos/seed/picsum/315/315" class="mr-3 rounded-circle" width="50" alt="User" /&gt;&lt;/a&gt;
                              &lt;div class="media-body"&gt;
                                  &lt;h6&gt;&lt;a href="#" data-toggle="collapse" data-target=".forum-content" class="text-body"&gt;Top rated filter not working&lt;/a&gt;&lt;/h6&gt;
                                  &lt;p&gt;
                                      lorem ipsum dolor sit amet lorem ipsum dolor sit amet lorem ipsum dolor sit amet
                                  &lt;/p&gt;
                                  &lt;p class="text-muted"&gt;&lt;a href="javascript:void(0)"&gt;bugsysha&lt;/a&gt; replied &lt;span class="text-secondary font-weight-bold"&gt;11 hours ago&lt;/span&gt;&lt;/p&gt;
                              &lt;/div&gt;
                              &lt;div class="text-muted small text-center align-self-center"&gt;
                                  &lt;span class="d-none d-sm-inline-block"&gt;&lt;i class="far fa-eye"&gt;&lt;/i&gt; 49&lt;/span&gt;
                                  &lt;span&gt;&lt;i class="far fa-comment ml-2"&gt;&lt;/i&gt; 9&lt;/span&gt;
                              &lt;/div&gt;
                          &lt;/div&gt;
                      &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div class="card mb-2"&gt;
                      &lt;div class="card-body p-2 p-sm-3"&gt;
                          &lt;div class="media forum-item"&gt;
                              &lt;a href="#" data-toggle="collapse" data-target=".forum-content"&gt;&lt;img src="https://picsum.photos/seed/picsum/315/315" class="mr-3 rounded-circle" width="50" alt="User" /&gt;&lt;/a&gt;
                              &lt;div class="media-body"&gt;
                                  &lt;h6&gt;&lt;a href="#" data-toggle="collapse" data-target=".forum-content" class="text-body"&gt;Create a delimiter field&lt;/a&gt;&lt;/h6&gt;
                                  &lt;p&gt;
                                      lorem ipsum dolor sit amet lorem ipsum dolor sit amet lorem ipsum dolor sit amet
                                  &lt;/p&gt;
                                  &lt;p class="text-muted"&gt;&lt;a href="javascript:void(0)"&gt;jackalds&lt;/a&gt; replied &lt;span class="text-secondary font-weight-bold"&gt;12 hours ago&lt;/span&gt;&lt;/p&gt;
                              &lt;/div&gt;
                              &lt;div class="text-muted small text-center align-self-center"&gt;
                                  &lt;span class="d-none d-sm-inline-block"&gt;&lt;i class="far fa-eye"&gt;&lt;/i&gt; 65&lt;/span&gt;
                                  &lt;span&gt;&lt;i class="far fa-comment ml-2"&gt;&lt;/i&gt; 10&lt;/span&gt;
                              &lt;/div&gt;
                          &lt;/div&gt;
                      &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div class="card mb-2"&gt;
                      &lt;div class="card-body p-2 p-sm-3"&gt;
                          &lt;div class="media forum-item"&gt;
                              &lt;a href="#" data-toggle="collapse" data-target=".forum-content"&gt;&lt;img src="https://picsum.photos/seed/picsum/315/315" class="mr-3 rounded-circle" width="50" alt="User" /&gt;&lt;/a&gt;
                              &lt;div class="media-body"&gt;
                                  &lt;h6&gt;&lt;a href="#" data-toggle="collapse" data-target=".forum-content" class="text-body"&gt;One model 4 tables&lt;/a&gt;&lt;/h6&gt;
                                  &lt;p&gt;
                                      lorem ipsum dolor sit amet lorem ipsum dolor sit amet lorem ipsum dolor sit amet
                                  &lt;/p&gt;
                                  &lt;p class="text-muted"&gt;&lt;a href="javascript:void(0)"&gt;bugsysha&lt;/a&gt; replied &lt;span class="text-secondary font-weight-bold"&gt;14 hours ago&lt;/span&gt;&lt;/p&gt;
                              &lt;/div&gt;
                              &lt;div class="text-muted small text-center align-self-center"&gt;
                                  &lt;span class="d-none d-sm-inline-block"&gt;&lt;i class="far fa-eye"&gt;&lt;/i&gt; 45&lt;/span&gt;
                                  &lt;span&gt;&lt;i class="far fa-comment ml-2"&gt;&lt;/i&gt; 4&lt;/span&gt;
                              &lt;/div&gt;
                          &lt;/div&gt;
                      &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div class="card mb-2"&gt;
                      &lt;div class="card-body p-2 p-sm-3"&gt;
                          &lt;div class="media forum-item"&gt;
                              &lt;a href="#" data-toggle="collapse" data-target=".forum-content"&gt;&lt;img src="https://picsum.photos/seed/picsum/315/315" class="mr-3 rounded-circle" width="50" alt="User" /&gt;&lt;/a&gt;
                              &lt;div class="media-body"&gt;
                                  &lt;h6&gt;&lt;a href="#" data-toggle="collapse" data-target=".forum-content" class="text-body"&gt;Auth attempt returns false&lt;/a&gt;&lt;/h6&gt;
                                  &lt;p&gt;
                                      lorem ipsum dolor sit amet lorem ipsum dolor sit amet lorem ipsum dolor sit amet
                                  &lt;/p&gt;
                                  &lt;p class="text-muted"&gt;&lt;a href="javascript:void(0)"&gt;michaeloravec&lt;/a&gt; replied &lt;span class="text-secondary font-weight-bold"&gt;18 hours ago&lt;/span&gt;&lt;/p&gt;
                              &lt;/div&gt;
                              &lt;div class="text-muted small text-center align-self-center"&gt;
                                  &lt;span class="d-none d-sm-inline-block"&gt;&lt;i class="far fa-eye"&gt;&lt;/i&gt; 70&lt;/span&gt;
                                  &lt;span&gt;&lt;i class="far fa-comment ml-2"&gt;&lt;/i&gt; 3&lt;/span&gt;
                              &lt;/div&gt;
                          &lt;/div&gt;
                      &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;ul class="pagination pagination-sm pagination-circle justify-content-center mb-0"&gt;
                      &lt;li class="page-item disabled"&gt;
                          &lt;span class="page-link has-icon"&gt;&lt;i class="material-icons"&gt;chevron_left&lt;/i&gt;&lt;/span&gt;
                      &lt;/li&gt;
                      &lt;li class="page-item"&gt;&lt;a class="page-link" href="javascript:void(0)"&gt;1&lt;/a&gt;&lt;/li&gt;
                      &lt;li class="page-item active"&gt;&lt;span class="page-link"&gt;2&lt;/span&gt;&lt;/li&gt;
                      &lt;li class="page-item"&gt;&lt;a class="page-link" href="javascript:void(0)"&gt;3&lt;/a&gt;&lt;/li&gt;
                      &lt;li class="page-item"&gt;
                          &lt;a class="page-link has-icon" href="javascript:void(0)"&gt;&lt;i class="material-icons"&gt;chevron_right&lt;/i&gt;&lt;/a&gt;
                      &lt;/li&gt;
                  &lt;/ul&gt;
              &lt;/div&gt;
              &lt;!-- /Forum List --&gt;
  
              &lt;!-- Forum Detail --&gt;
              &lt;div class="inner-main-body p-2 p-sm-3 collapse forum-content"&gt;
                  &lt;a href="#" class="btn btn-light btn-sm mb-3 has-icon" data-toggle="collapse" data-target=".forum-content"&gt;&lt;i class="fa fa-arrow-left mr-2"&gt;&lt;/i&gt;Back&lt;/a&gt;
                  &lt;div class="card mb-2"&gt;
                      &lt;div class="card-body"&gt;
                          &lt;div class="media forum-item"&gt;
                              &lt;a href="javascript:void(0)" class="card-link"&gt;
                                  &lt;img src="https://picsum.photos/seed/picsum/315/315" class="rounded-circle" width="50" alt="User" /&gt;
                                  &lt;small class="d-block text-center text-muted"&gt;Newbie&lt;/small&gt;
                              &lt;/a&gt;
                              &lt;div class="media-body ml-3"&gt;
                                  &lt;a href="javascript:void(0)" class="text-secondary"&gt;Mokrani&lt;/a&gt;
                                  &lt;small class="text-muted ml-2"&gt;1 hour ago&lt;/small&gt;
                                  &lt;h5 class="mt-1"&gt;Realtime fetching data&lt;/h5&gt;
                                  &lt;div class="mt-3 font-size-sm"&gt;
                                      &lt;p&gt;Hellooo :)&lt;/p&gt;
                                      &lt;p&gt;
                                          I'm newbie with laravel and i want to fetch data from database in realtime for my dashboard anaytics and i found a solution with ajax but it dosen't work if any one have a simple solution it will be
                                          helpful
                                      &lt;/p&gt;
                                      &lt;p&gt;Thank&lt;/p&gt;
                                  &lt;/div&gt;
                              &lt;/div&gt;
                              &lt;div class="text-muted small text-center"&gt;
                                  &lt;span class="d-none d-sm-inline-block"&gt;&lt;i class="far fa-eye"&gt;&lt;/i&gt; 19&lt;/span&gt;
                                  &lt;span&gt;&lt;i class="far fa-comment ml-2"&gt;&lt;/i&gt; 3&lt;/span&gt;
                              &lt;/div&gt;
                          &lt;/div&gt;
                      &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div class="card mb-2"&gt;
                      &lt;div class="card-body"&gt;
                          &lt;div class="media forum-item"&gt;
                              &lt;a href="javascript:void(0)" class="card-link"&gt;
                                  &lt;img src="https://picsum.photos/seed/picsum/315/315" class="rounded-circle" width="50" alt="User" /&gt;
                                  &lt;small class="d-block text-center text-muted"&gt;Pro&lt;/small&gt;
                              &lt;/a&gt;
                              &lt;div class="media-body ml-3"&gt;
                                  &lt;a href="javascript:void(0)" class="text-secondary"&gt;Sneha Issar&lt;/a&gt;
                                  &lt;small class="text-muted ml-2"&gt;1 hour ago&lt;/small&gt;
                                  &lt;div class="mt-3 font-size-sm"&gt;
                                      &lt;p&gt;What exactly doesn't work with your ajax calls?&lt;/p&gt;
                                      &lt;p&gt;Also, WebSockets are a great solution for realtime data on a dashboard. Laravel offers this out of the box using broadcasting&lt;/p&gt;
                                  &lt;/div&gt;
                                  &lt;button class="btn btn-xs text-muted has-icon"&gt;&lt;i class="fa fa-heart" aria-hidden="true"&gt;&lt;/i&gt;1&lt;/button&gt;
                                  &lt;a href="javascript:void(0)" class="text-muted small"&gt;Reply&lt;/a&gt;
                              &lt;/div&gt;
                          &lt;/div&gt;
                      &lt;/div&gt;
                  &lt;/div&gt;
              &lt;/div&gt;
              &lt;!-- /Forum Detail --&gt;
  
              &lt;!-- /Inner main body --&gt;
          &lt;/div&gt;
          &lt;!-- /Inner main --&gt;
      &lt;/div&gt;
  
      &lt;!-- New Thread Modal --&gt;
      &lt;div class="modal fade" id="threadModal" tabindex="-1" role="dialog" aria-labelledby="threadModalLabel" aria-hidden="true"&gt;
          &lt;div class="modal-dialog modal-lg" role="document"&gt;
              &lt;div class="modal-content"&gt;
                  &lt;form&gt;
                      &lt;div class="modal-header d-flex align-items-center bg-primary text-white"&gt;
                          &lt;h6 class="modal-title mb-0" id="threadModalLabel"&gt;New Discussion&lt;/h6&gt;
                          &lt;button type="button" class="close" data-dismiss="modal" aria-label="Close"&gt;
                              &lt;span aria-hidden="true"&gt;×&lt;/span&gt;
                          &lt;/button&gt;
                      &lt;/div&gt;
                      &lt;div class="modal-body"&gt;
                          &lt;div class="form-group"&gt;
                              &lt;label for="threadTitle"&gt;Title&lt;/label&gt;
                              &lt;input type="text" class="form-control" id="threadTitle" placeholder="Enter title" autofocus="" /&gt;
                          &lt;/div&gt;
                          &lt;textarea class="form-control summernote" style="display: none;"&gt;&lt;/textarea&gt;
  
                          &lt;div class="custom-file form-control-sm mt-3" style="max-width: 300px;"&gt;
                              &lt;input type="file" class="custom-file-input" id="customFile" multiple="" /&gt;
                              &lt;label class="custom-file-label" for="customFile"&gt;Attachment&lt;/label&gt;
                          &lt;/div&gt;
                      &lt;/div&gt;
                      &lt;div class="modal-footer"&gt;
                          &lt;button type="button" class="btn btn-light" data-dismiss="modal"&gt;Cancel&lt;/button&gt;
                          &lt;button type="button" class="btn btn-primary"&gt;Post&lt;/button&gt;
                      &lt;/div&gt;
                  &lt;/form&gt;
              &lt;/div&gt;
          &lt;/div&gt;
      &lt;/div&gt;
  &lt;/div&gt;
  &lt;/div&gt;
</code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
body{
    margin-top:20px;
    color: #1a202c;
    text-align: left;
    background-color: #e2e8f0;    
}
.inner-wrapper {
    position: relative;
    height: calc(100vh - 3.5rem);
    transition: transform 0.3s;
}
@media (min-width: 992px) {
    .sticky-navbar .inner-wrapper {
        height: calc(100vh - 3.5rem - 48px);
    }
}

.inner-main,
.inner-sidebar {
    position: absolute;
    top: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
}
.inner-sidebar {
    left: 0;
    width: 235px;
    border-right: 1px solid #cbd5e0;
    background-color: #fff;
    z-index: 1;
}
.inner-main {
    right: 0;
    left: 235px;
}
.inner-main-footer,
.inner-main-header,
.inner-sidebar-footer,
.inner-sidebar-header {
    height: 3.5rem;
    border-bottom: 1px solid #cbd5e0;
    display: flex;
    align-items: center;
    padding: 0 1rem;
    flex-shrink: 0;
}
.inner-main-body,
.inner-sidebar-body {
    padding: 1rem;
    overflow-y: auto;
    position: relative;
    flex: 1 1 auto;
}
.inner-main-body .sticky-top,
.inner-sidebar-body .sticky-top {
    z-index: 999;
}
.inner-main-footer,
.inner-main-header {
    background-color: #fff;
}
.inner-main-footer,
.inner-sidebar-footer {
    border-top: 1px solid #cbd5e0;
    border-bottom: 0;
    height: auto;
    min-height: 3.5rem;
}
@media (max-width: 767.98px) {
    .inner-sidebar {
        left: -235px;
    }
    .inner-main {
        left: 0;
    }
    .inner-expand .main-body {
        overflow: hidden;
    }
    .inner-expand .inner-wrapper {
        transform: translate3d(235px, 0, 0);
    }
}

.nav .show>.nav-link.nav-link-faded, .nav-link.nav-link-faded.active, .nav-link.nav-link-faded:active, .nav-pills .nav-link.nav-link-faded.active, .navbar-nav .show>.nav-link.nav-link-faded {
    color: #3367b5;
    background-color: #c9d8f0;
}

.nav-pills .nav-link.active, .nav-pills .show>.nav-link {
    color: #fff;
    background-color: #467bcb;
}
.nav-link.has-icon {
    display: flex;
    align-items: center;
}
.nav-link.active {
    color: #467bcb;
}
.nav-pills .nav-link {
    border-radius: .25rem;
}
.nav-link {
    color: #4a5568;
}
.card {
    box-shadow: 0 1px 3px 0 rgba(0,0,0,.1), 0 1px 2px 0 rgba(0,0,0,.06);
}

.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 0 solid rgba(0,0,0,.125);
    border-radius: .25rem;
}

.card-body {
    flex: 1 1 auto;
    min-height: 1px;
    padding: 1rem;
}
          </code></pre>
        </div>
        <!-- <div id="tab04" class="tab-contents">
          <h2>Tab 4</h2>
          <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Eius quos aliquam consequuntur, esse provident impedit minima porro! Laudantium laboriosam culpa quis fugiat ea, architecto velit ab, deserunt rem quibusdam voluptatum.</p>
        </div> -->
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
