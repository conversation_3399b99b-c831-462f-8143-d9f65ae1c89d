<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Horizontal Bar Chart</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Horizontal bar chart</h4>
              <div class="mb-2">
                <a
                  href="./downloads/bar-chart.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/bar-chart/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
              <pre class="line-numbers">
<code class="language-markup">&lt;div class="container-fluid py-3 my-3"&gt;
    &lt;div class="row"&gt;
        &lt;div class="col-sm-6 border-right"&gt;
            &lt;div id="chartdiv" style="width: 100%; height: 380px;"&gt;&lt;/div&gt;

        &lt;/div&gt;
        &lt;div class="col-sm-6"&gt;
            &lt;div id="chartdiv1" style="width: 100%; height: 380px;"&gt;&lt;/div&gt;

        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;</code>
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css"></code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">


                var data = [
                    {
                        "cName": "Pork - United States",
                        "positive": 43.4
                    },
                    {
                        "cName": "Eggs - United States",
                        "positive": 29.3
                    },
                    {
                        "cName": "Oat - Global",
                        "positive": 14.2
                    },
                    {
                        "cName": "Cocoa - Global",
                        "positive": 9.4
                    },
                    {
                        "cName": "Molasses - Asia Pacific",
                        "positive": 9.8
                    },
                    {
                        "cName": "Wheat - United States",
                        "positive": 6.7
                    },
                    {
                        "cName": "Cheese - Asia Pacific",
                        "positive": 6.1
                    },
                    {
                        "cName": "Tallow - Latin America",
                        "positive": 4.7
                    },
                    {
                        "cName": "Salmon - Europe",
                        "positive": 4.2
                    },
                    {
                        "cName": "Jet Fuel - United States",
                        "positive": 3.8
                    }
                ];
        
                var data1 = [
                    {
                        "cName": "Ethylene - Asia Pacific",
                        "negative": -16.11
                    },
                    {
                        "cName": "HDPE - United States",
                        "negative": -13.41
                    },
                    {
                        "cName": "Coal - Asia Pacific",
                        "negative": -13.12
                    },
                    {
                        "cName": "Methanol - Europe",
                        "negative": -12.6
                    },
                    {
                        "cName": "Benzene - United States",
                        "negative": -10.92
                    },
                    {
                        "cName": "HDPE - Europe",
                        "negative": -10.69
                    },
                    {
                        "cName": "Methanol - Asia",
                        "negative": -10.62
                    },
                    {
                        "cName": "Benzene - Asia Pacific",
                        "negative": -9.23
                    },
                    {
                        "cName": "Cheese - United States",
                        "negative": -8.96
                    },
                    {
                        "cName": "Palladium - Global",
                        "negative": -8.87
                    }
                ]
        
        
                function drawChartNegative(node, data, categoryName, valueName, color) {
                    var root = am5.Root.new(node);
        
        
                    root.setThemes([
                        am5themes_Animated.new(root)
                    ]);
        
        
                    var chart = root.container.children.push(
                        am5xy.XYChart.new(root, {
                            focusable: true,
        
                            layout: root.verticalLayout,
                        })
                    );
        
        
        
        
        
                    var yRenderer = am5xy.AxisRendererY.new(root, {
                        strokeOpacity: 0.2,
                        strokeWidth: 1,
                        minGridDistance: 10,
        
                    });
        
                    yRenderer.labels.template.setAll({
                        multiLocation: 0.5,
                        location: 0.5,
                        paddingRight: 15
                    });
                    yRenderer.grid.template.setAll({
                        stroke: am5.color(0xfff),
                        strokeWidth: 0,
                        strokeOpacity: 0,
                    });
                    yRenderer.labels.template.setAll({
                        fontSize: "12px",
                    });
        
                    yRenderer.grid.template.set("location", 0.5);
        
                    var yAxis = chart.yAxes.push(
                        am5xy.CategoryAxis.new(root, {
                            categoryField: categoryName,
                            tooltip: am5.Tooltip.new(root, {}),
                            numberFormat: "#'%'",
                            renderer: yRenderer
                        })
                    );
        
                    yAxis.data.setAll(data);
        
        
                    function createSeries(field) {
        
        
                        var xRenderer = am5xy.AxisRendererX.new(root, {
                            minGridDistance: 40,
                            strokeOpacity: 0.2,
                            strokeWidth: 1,
        
                        });
        
                        xRenderer.labels.template.setAll({
        
                            centerY: am5.p50,
                        });
                        xRenderer.grid.template.setAll({
                            stroke: am5.color(0xfff),
                            strokeWidth: 0,
                            strokeOpacity: 0,
                        });
                        xRenderer.labels.template.setAll({
                            fontSize: "12px",
                        });
                        var minValue = Math.min(...data.map(d => d.negative));
        
                        var xAxis = chart.xAxes.push(
                            am5xy.ValueAxis.new(root, {
                                renderer: xRenderer,
                                min: minValue,
                                max: 0,
                                numberFormat: "#'%'",
                                tooltip: am5.Tooltip.new(root, {
                                    animationDuration: 0
                                }),
        
                            })
                        );
        
        
        
        
        
        
                        var series = chart.series.push(
                            am5xy.ColumnSeries.new(root, {
                                xAxis: xAxis,
                                yAxis: yAxis,
                                valueXField: valueName,
                                categoryYField: categoryName,
        
                            })
                        );
        
                        var tooltip1 = am5.Tooltip.new(root, {
                            labelText: "{valueX}",
                            pointerOrientation: "horizontal",
                        });
                        tooltip1.get("background").setAll({
                            cornerRadius: 0,
                        });
                        series.set("tooltip", tooltip1);
        
        
        
                        series.columns.template.setAll({
                            fill: am5.color(color),
                            strokeOpacity: 0
                        });
                        series.bullets.push(function () {
                            return am5.Bullet.new(root, {
                                sprite: am5.Label.new(root, {
                                    text: "{valueX}%",
                                    fill: am5.color("#ffffff"),
                                    centerY: am5.p50,
                                    centerX: am5.p50,
                                    populateText: true,
                                    fontSize: "12px",
                                })
                            })
                        });
                        series.data.setAll(data);
                        series.appear();
        
                        return series;
                    }
        
                    createSeries(valueName);
        
                    var cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
                        behavior: "none",
                        yAxis: yAxis
                    }));
        
                    chart.appear(1000, 100);
                }
        
                function drawChartPositive(node, data, categoryName, valueName, color) {
                    var root = am5.Root.new(node);
        
        
                    root.setThemes([
                        am5themes_Animated.new(root)
                    ]);
        
        
                    var chart = root.container.children.push(
                        am5xy.XYChart.new(root, {
                            focusable: true,
        
                            layout: root.verticalLayout,
                        })
                    );
        
        
        
        
        
                    var yRenderer = am5xy.AxisRendererY.new(root, {
                        strokeOpacity: 0.2,
                        strokeWidth: 1,
                        minGridDistance: 10,
        
                    });
        
                    yRenderer.labels.template.setAll({
                        multiLocation: 0.5,
                        location: 0.5,
                        paddingRight: 15
                    });
                    yRenderer.grid.template.setAll({
                        stroke: am5.color(0xfff),
                        strokeWidth: 0,
                        strokeOpacity: 0,
                    });
                    yRenderer.labels.template.setAll({
                        fontSize: "12px",
                    });
        
                    yRenderer.grid.template.set("location", 0.5);
        
                    var yAxis = chart.yAxes.push(
                        am5xy.CategoryAxis.new(root, {
                            categoryField: categoryName,
                            tooltip: am5.Tooltip.new(root, {}),
                            numberFormat: "#'%'",
                            renderer: yRenderer
                        })
                    );
        
                    yAxis.data.setAll(data);
        
        
                    function createSeries(field) {
        
        
                        var xRenderer = am5xy.AxisRendererX.new(root, {
                            minGridDistance: 40,
                            strokeOpacity: 0.2,
                            strokeWidth: 1,
        
                        });
        
                        xRenderer.labels.template.setAll({
        
                            centerY: am5.p50,
                        });
                        xRenderer.grid.template.setAll({
                            stroke: am5.color(0xfff),
                            strokeWidth: 0,
                            strokeOpacity: 0,
                        });
                        xRenderer.labels.template.setAll({
                            fontSize: "12px",
                        });
        
                        var xAxis = chart.xAxes.push(
                            am5xy.ValueAxis.new(root, {
                                renderer: xRenderer,
                                min: 0,
                                numberFormat: "#'%'",
                                tooltip: am5.Tooltip.new(root, {
                                    animationDuration: 0
                                }),
        
                            })
                        );
        
        
        
        
        
        
                        var series = chart.series.push(
                            am5xy.ColumnSeries.new(root, {
                                xAxis: xAxis,
                                yAxis: yAxis,
                                valueXField: valueName,
                                categoryYField: categoryName,
        
                            })
                        );
        
                        var tooltip1 = am5.Tooltip.new(root, {
                            labelText: "{valueX}",
                            pointerOrientation: "horizontal",
                        });
                        tooltip1.get("background").setAll({
                            cornerRadius: 0,
                        });
                        series.set("tooltip", tooltip1);
        
        
        
                        series.columns.template.setAll({
                            fill: am5.color(color),
                            strokeOpacity: 0
                        });
                        series.bullets.push(function () {
                            return am5.Bullet.new(root, {
                                sprite: am5.Label.new(root, {
                                    text: "{valueX}%",
                                    fill: am5.color("#ffffff"),
                                    centerY: am5.p50,
                                    centerX: am5.p50,
                                    populateText: true,
                                    fontSize: "12px",
                                })
                            })
                        });
                        series.data.setAll(data);
                        series.appear();
        
                        return series;
                    }
        
                    createSeries(valueName);
        
                    var cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
                        behavior: "none",
                        yAxis: yAxis
                    }));
        
                    chart.appear(1000, 100);
                }
        
        
                drawChartPositive("chartdiv", data, "cName", "positive", "#bb3506");
                drawChartNegative("chartdiv1", data1, "cName", "negative", "#25ba3b");
        
            </code></pre>
        </div>
          </div>
        </div>
        </div>
      </div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
