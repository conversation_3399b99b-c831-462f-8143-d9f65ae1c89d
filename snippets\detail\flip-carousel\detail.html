<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Component Library</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">
      <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Flip carousel</h4>
              <div class="mb-2">
                <a
                  href="./downloads/flip-carousel.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/flip-carousel/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
              <pre class="line-numbers">
<code class="language-markup">&lt;div class="container"&gt;
    &lt;h1&gt;Flip carousel&lt;/h1&gt;
    &lt;div class="gallery"&gt;
      &lt;div class="gallery-container"&gt;
        &lt;img
          class="gallery-item"
          src="http://fakeimg.pl/300/00af9b/ffffff?text=Slide1"
        /&gt;
        &lt;img
          class="gallery-item"
          src="http://fakeimg.pl/300/00af9b/ffffff?text=Slide2"
        /&gt;
        &lt;img
          class="gallery-item"
          src="http://fakeimg.pl/300/00af9b/ffffff?text=Slide3"
        /&gt;
        &lt;img
          class="gallery-item"
          src="http://fakeimg.pl/300/00af9b/ffffff?text=Slide4"
        /&gt;
        &lt;img
          class="gallery-item"
          src="http://fakeimg.pl/300/00af9b/ffffff?text=Slide5"
        /&gt;
      &lt;/div&gt;
      &lt;div class="gallery-controls"&gt;&lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;</code>
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">.gallery {
                width: 100%;
              }
              .gallery-container {
                align-items: center;
                display: flex;
                height: 400px;
                margin: 0 auto;
                max-width: 1000px;
                position: relative;
              }
              .gallery-item {
                height: 150px;
                opacity: 0.4;
                position: absolute;
                transition: all 0.3s ease-in-out;
                width: 150px;
                z-index: 0;
              }
              .gallery-item.gallery-item-selected {
                border: 1px solid #ddd;
                height: 300px;
                opacity: 1;
                left: 50%;
                transform: translateX(-50%);
                width: 300px;
                z-index: 2;
              }
              .gallery-item.gallery-item-previous,
              .gallery-item.gallery-item-next {
                height: 200px;
                opacity: 1;
                width: 200px;
                z-index: 1;
              }
              .gallery-item.gallery-item-previous {
                left: 30%;
                transform: translateX(-50%);
              }
              .gallery-item.gallery-item-next {
                left: 70%;
                transform: translateX(-50%);
              }
              .gallery-item.gallery-item-first {
                left: 15%;
                transform: translateX(-50%);
              }
              .gallery-item.gallery-item-last {
                left: 85%;
                transform: translateX(-50%);
              }
              .gallery-controls {
                display: flex;
                justify-content: center;
                margin: 30px 0;
              }
              .gallery-controls button {
                border: 0;
                cursor: pointer;
                font-size: 16px;
                margin: 0 20px;
                padding: 0 12px;
                text-transform: capitalize;
              }
              .gallery-controls button:focus {
                outline: none;
              }
              .gallery-controls-previous {
                position: relative;
              }
              .gallery-controls-previous::before {
                border: solid #000;
                border-width: 0 2px 2px 0;
                content: "";
                display: inline-block;
                height: 4px;
                left: -10px;
                padding: 2px;
                position: absolute;
                top: 0;
                transform: rotate(135deg) translateY(-50%);
                transition: left 0.15s ease-in-out;
                width: 4px;
              }
              .gallery-controls-previous:hover::before {
                left: -18px;
              }
              .gallery-controls-next {
                position: relative;
              }
              .gallery-controls-next::before {
                border: solid #000;
                border-width: 0 2px 2px 0;
                content: "";
                display: inline-block;
                height: 4px;
                padding: 2px;
                position: absolute;
                right: -10px;
                top: 50%;
                transform: rotate(-45deg) translateY(-50%);
                transition: right 0.15s ease-in-out;
                width: 4px;
              }
              .gallery-controls-next:hover::before {
                right: -18px;
              }
              .gallery-nav {
                bottom: -15px;
                display: flex;
                justify-content: center;
                list-style: none;
                padding: 0;
                position: absolute;
                width: 100%;
              }
              .gallery-nav li {
                background: #ccc;
                border-radius: 50%;
                height: 10px;
                margin: 0 16px;
                width: 10px;
              }
              .gallery-nav li.gallery-item-selected {
                background: #555;
              }
              body {
                background: #f4f4f4;
              }
              .container {
                margin: auto;
                max-width: 1000px;
              }
              h1 {
                margin-bottom: 0rem;
                text-align: center;
              }</code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">
                const galleryContainer = document.querySelector(".gallery-container");
                const galleryControlsContainer =
                  document.querySelector(".gallery-controls");
                const galleryControls = ["previous", "next"];
                const galleryItems = document.querySelectorAll(".gallery-item");
                class Carousel {
                  constructor(container, items, controls) {
                    this.carouselContainer = container;
                    this.carouselControls = controls;
                    this.carouselArray = [...items];
                  }
                  setInitialState() {
                    this.carouselArray[0].classList.add("gallery-item-first");
                    this.carouselArray[1].classList.add("gallery-item-previous");
                    this.carouselArray[2].classList.add("gallery-item-selected");
                    this.carouselArray[3].classList.add("gallery-item-next");
                    this.carouselArray[4].classList.add("gallery-item-last");
                    document.querySelector(".gallery-nav").childNodes[0].className =
                      "gallery-nav-item gallery-item-first";
                    document.querySelector(".gallery-nav").childNodes[1].className =
                      "gallery-nav-item gallery-item-previous";
                    document.querySelector(".gallery-nav").childNodes[2].className =
                      "gallery-nav-item gallery-item-selected";
                    document.querySelector(".gallery-nav").childNodes[3].className =
                      "gallery-nav-item gallery-item-next";
                    document.querySelector(".gallery-nav").childNodes[4].className =
                      "gallery-nav-item gallery-item-last";
                  }
                  setCurrentState(target, selected, previous, next, first, last) {
                    selected.forEach((el) => {
                      el.classList.remove("gallery-item-selected");
                      if (target.className == "gallery-controls-previous") {
                        el.classList.add("gallery-item-next");
                      } else {
                        el.classList.add("gallery-item-previous");
                      }
                    });
                    previous.forEach((el) => {
                      el.classList.remove("gallery-item-previous");
                      if (target.className == "gallery-controls-previous") {
                        el.classList.add("gallery-item-selected");
                      } else {
                        el.classList.add("gallery-item-first");
                      }
                    });
                    next.forEach((el) => {
                      el.classList.remove("gallery-item-next");
                      if (target.className == "gallery-controls-previous") {
                        el.classList.add("gallery-item-last");
                      } else {
                        el.classList.add("gallery-item-selected");
                      }
                    });
                    first.forEach((el) => {
                      el.classList.remove("gallery-item-first");
                      if (target.className == "gallery-controls-previous") {
                        el.classList.add("gallery-item-previous");
                      } else {
                        el.classList.add("gallery-item-last");
                      }
                    });
                    last.forEach((el) => {
                      el.classList.remove("gallery-item-last");
                      if (target.className == "gallery-controls-previous") {
                        el.classList.add("gallery-item-first");
                      } else {
                        el.classList.add("gallery-item-next");
                      }
                    });
                  }
                  setNav() {
                    galleryContainer.appendChild(document.createElement("ul")).className =
                      "gallery-nav";
                    this.carouselArray.forEach((item) => {
                      const nav = galleryContainer.lastElementChild;
                      nav.appendChild(document.createElement("li"));
                    });
                  }
                  setControls() {
                    this.carouselControls.forEach((control) => {
                      galleryControlsContainer.appendChild(
                        document.createElement("button")
                      ).className = `gallery-controls-${control}`;
                    });
                    !!galleryControlsContainer.childNodes[0]
                      ? (galleryControlsContainer.childNodes[0].innerHTML =
                          this.carouselControls[0])
                      : null;
                    !!galleryControlsContainer.childNodes[1]
                      ? (galleryControlsContainer.childNodes[1].innerHTML =
                          this.carouselControls[1])
                      : null;
                  }
                  useControls() {
                    const triggers = [...galleryControlsContainer.childNodes];
                    triggers.forEach((control) => {
                      control.addEventListener("click", () => {
                        const target = control;
                        const selectedItem = document.querySelectorAll(
                          ".gallery-item-selected"
                        );
                        const previousSelectedItem = document.querySelectorAll(
                          ".gallery-item-previous"
                        );
                        const nextSelectedItem =
                          document.querySelectorAll(".gallery-item-next");
                        const firstCarouselItem = document.querySelectorAll(
                          ".gallery-item-first"
                        );
                        const lastCarouselItem =
                          document.querySelectorAll(".gallery-item-last");
                        this.setCurrentState(
                          target,
                          selectedItem,
                          previousSelectedItem,
                          nextSelectedItem,
                          firstCarouselItem,
                          lastCarouselItem
                        );
                      });
                    });
                  }
                }
                const exampleCarousel = new Carousel(
                  galleryContainer,
                  galleryItems,
                  galleryControls
                );
                exampleCarousel.setControls();
                exampleCarousel.setNav();
                exampleCarousel.setInitialState();
                exampleCarousel.useControls();
              </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
