@media (max-width: 767.98px) and (min-width: 576px){
    /* .slide-box div {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 17%;
} */
}

@media (max-width: 991.98px) and (min-width: 768px){
    /* .slide-box div {
    -ms-flex: 0 0 33.3333%;
    flex: 0 0 33.3333%;
    max-width: 17.3333%;
} */
}


@media (min-width: 992px)
{
  /* .slide-box div {
    -ms-flex: 0 0 15%;
    flex: 0 0 25%;
    max-width: 17%;
  } */
  .desktop{
	  display:flex;
	  
  }
  .mobile{display:none}
}

@media  (min-width: 990px) and (max-width: 1160px){
    .themeBorderBottom{
        max-height: 90px;
    }
    .navbar-brand {
        display: flex;
        flex-direction: column;
    }
    nav.desktop a.navbar-brand span.tierName {
        margin-left: 0px;
        color: #00ae9b;
        font-size: 16px;
        position: relative;
        bottom: 0px;
        left: 0;
    }
}


@media screen and (max-width: 1088px){
    
    #tsc_nav_1 .nav-link {
        display: block;
        padding: .4rem 0.9rem;
    }
    .catimageSection {
        overflow: hidden;
        height: auto;
    }
    .catimageSection img {
        max-width: 100%;
        height: auto;
        width: 100%;
        margin-left: 0;
    }
    .newsDesc{
        display:block;
    }
    .newsGroup {
        margin-bottom: 60px;
        float: none;
        clear: both;
}
.desktop.navbar .input-group{
    width: 750px;
}
}

@media (min-width: 320px) and (max-width: 991px) {
	.catLand .col-sm-3 {
    padding-right: 10px;
}
	.lock .marginTop86:after {
	   position: absolute;
    content: '';
    width: 100%;
    height: 100%;
    background: #f9f9f9;
    overflow: hidden;
    top:0;
    left: 0;z-index: 9;
   
}
	/* .mysol.dropdown:hover > .dropdown-menu {
  display: block;
} */
	.navbar .input-group {
   
	background: #fff;
}
	.lock{overflow:hidden!important;position:relative;}
	.lock .navbar-toggler-icon {
    background-image: url(../images/close.png);
}
	.lock .marginTop86{/* opacity:0.1 */}
	.lock .themeBorderBottom{background: #f9f9f9; border-color:#f9f9f9}
	.marginTop86 {
    margin-top: 61px;
}
#tsc_nav_1 .odi .nav-link {
    padding: 3px 8px;
}
    .mob{flex-direction: row;}
      #tsc_nav_1 .nav-item {
        text-align: left !important;
        margin-left: 2px;
      }
    #loadTopCategory .col-sm-4{max-width:100%;flex: auto;padding-right:0}
      #tsc_nav_1 .nav-item a {
        text-align: left !important;
        margin-left: 0px;
        padding-left: 2px;white-space:normal;
      }
      .navbar-brand img{width:70px}
    
     
    #tsc_nav_1 .navbar-nav {
        align-items: normal;    padding: 10px 3px;
    }
    .out100{
        display: none;
    }    
	 .mobile{
	  display:flex;
	  
  }
  .desktop{display:none}
     .themeBorderBottom{max-height: 61px; }
     .blogdesc {        top: 50%;
        min-height: 131px;
        transform: translate(-50%,-50%);}
     
	 .blogHeading a {
    color: #fff;
	 font-size: 17px}
	 .blogHeading {
   
	 align-items: flex-start;}.blogContainer {margin-bottom: 10px;}
}	

@media (max-width: 320px){
	.navbar-light .navbar-brand {margin-right:0!important}

	.catwrap 	{margin: 0 auto;
    display: inherit;}
	.navbar .input-group {
    height: auto;}
	.input-group input,.input-group span{margin-top:5px;}
	.navbar .input-group {
    background: transparent;
}
.input-group input {
    height: 28px;
    border-left-width: 0;
}
}
@media all and (min-width:1366px) {
    .container-fluid{width:1310px}/* .footer{width:97.7%} */
    .marginTop86 {
   
   min-height: 796px; 
}


.modal-lg {
    max-width: 1160px;
}
  }

@media screen and (min-width: 320px) and (max-width:699px) {
    .carousel-control-prev {
    margin-left: -24px;
}
.carousel-control-next {
    margin-right: -24px;
}
.arrowicon {
    font-size: 40px;
    color: #98bac3;
}
.resizing_select {
    width: 70px !important;
}
.navbar-brand{
    position: relative;
}
.navbar-brand span{
    position: absolute;
    bottom: -2px;
    margin-left: 5px;
}
div.slider-image {
    margin-left: 0px;
}
.slick-slider:before {
    box-shadow: 13px 12px 29px 64px rgb(255, 255, 255);
}
.arrowicon {
    font-size: 30px;
    color: #98bac3;
}
.slideControls i {
    font-size: 30px;
}
section#loadTopCategoryMobile {
    overflow-y: auto;
    height: 370px;
}
.secondaryOption .col-sm-5.pl-1 {
    padding-left: 15px !important;
}
.catimageSection {
    height: 131px !important;
}
.Rheading .icons {

    background: #fff;

}
div#grid-viewinsightsReports {
    margin-top: 10px;
}
.socialMediaShare .col-sm-4 {
    width: 25% !important;
    /* display: flex; */
    max-width: 30% !important;
}
input#myInput {
    width: 96%;
}
#cpyLink {
    
    padding-left: 10px;
    padding-top: 10px;
    text-align: center;
    display: block !important;
    margin: 0 auto !important;
}
.blogHeading a {
    color: #fff;
    font-size: 13px;
    line-height: 18px;
}
#remove-row #LoadMoreButton {
    padding-left: 30px;
}
.catimageSection::after {
  box-shadow: -15px -7px 20px 16px rgba(255,255, 255,1);

}
.d-flex.iconDiv::after {
    content: ' ';
    width: 100%;
    position: absolute;
    height: 1px;
    background: #ddd;
    bottom: 10px;
}
#loadSourcingConutriesData .col-sm-12 {
    padding: 15px !important;
}
div#loadTariffData .col-sm-12 {
    padding: 15px !important;
}
.row{
    overflow-x:hidden;
}
#tsc_nav_1 .nav-item a {
    font-size: 12px;
}
}

@media screen and (min-width: 700px) and (max-width:1260px) {
    .arrowicon {
    font-size: 40px;
    color: #98bac3;
}
.carousel-control-prev {
    margin-left: -42px;
}
.carousel-control-next {
    margin-right: -42px;
}
.back .descriptionSection{
    font-size: 12px;
}
}
/* @media screen and (max-width:1024px) {
    .col-sm-3{
        -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    }
    .catLand .col-sm-3{
        -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
    }
} */
@media screen and (min-width: 577px){
    
.dropdown:hover > .dropdown-menu {
    display: block;
  }
#tsc_nav_1 .dropdown:hover > .dropdown-menu {
    display: block;
    border-radius: 0px;
  }
}

@media screen and (min-width: 991px) and (max-width: 1024px){
    .descriptionSection a {
        font-weight: normal;
        font-size: 12px !important;
    }
    
}

@media screen and (min-width: 577px) and (max-width:1023px) {
    /* .slide-box div {
    -ms-flex: 0 0 15%;
    flex: 0 0 25%;
    max-width: 25%;
} */
.carouselSection .carousel-inner{
    overflow: hidden !important;
}
#carousel0 .card-style:hover, .card-style:hover {
    z-index: 2;
    -webkit-transition: none;
    -webkit-transform: none;
    -ms-transition: none;
    -ms-transform: none;
    -moz-transition: none;
    -moz-transform: none;
    transition: none;
    transform: none;
    background-color: #fff !important;
}


.slideTextSection p {
    font-size: 12px;
}
.carouselSection .box{
    margin-left:0px;
}
.lslide {
    margin-right: -1px !important;
}

[class^="col-sm-"],[class^=" col-sm-"],[class^="col-4"],[class^=" col-4-"]{
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
}
.catwrap {
    height: auto;
    width: 100%;
    display: block;
    margin-bottom: 20px;
}
.catContainer{
    float: none;
}
.catwrap:hover .catContainer {
    z-index: 2;
    -webkit-transition: none;
    -webkit-transform: none;
    -ms-transition: none;
    -ms-transform: none;
    -moz-transition: none;
    -moz-transform: none;
    transition: none;
    transform: none;
    background-color: #fff !important;
    position: relative;
    height: auto;
}
.catContainer:hover .catdesc {
    transform: none;
    padding-bottom: inherit;
    padding-top: inherit;
    padding: inherit;
}
#displayReportData .col-sm-3 {
    width: 50% !important;
    max-width: 50% !important;
}
.seemore{
    display: none;
}
.catimageSection img{
    height: auto !important;
}
.back .descriptionSection{
    margin-top:0px;
}

}


@media screen and (min-width: 320px) and (max-width:576px) {
    /* .slide-box div {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
} */
.mobile.navbar ul.navbar-nav .show .dropdown-menu{
    display:block;
}
.catwrap {
    height: auto;
    width: 100%;
    display: block;
    margin-bottom: 20px;
}
.catContainer{
    float: none;
}
.catwrap:hover .catContainer {
    z-index: 2;
    -webkit-transition: none;
    -webkit-transform: none;
    -ms-transition: none;
    -ms-transform: none;
    -moz-transition: none;
    -moz-transform: none;
    transition: none;
    transform: none;
    background-color: #fff !important;
    position: relative;
    height: auto;
}
.catContainer:hover .catdesc {
    transform: none;
    padding-bottom: inherit;
    padding-top: inherit;
    padding: inherit;
}
.seemore{
    display: none;
}
.catimageSection img{
    height: auto !important;
}
}
@media screen and (max-width: 568px) and (orientation: landscape) {
   .carouselSection .boxContainer{
       height: 280px !important;
   }
   .carouselSection .box {
    height: 285px;
}
.descriptionSection a{
    font-size: 12px !important;
}
/* h1 + select + span.select2-container{
    width: auto
} */

}

@media (min-width: 992px){
   .header.widget-header{
        flex-direction: column;
    }
}
@media(max-width: 768px){
    [class^="col-sm-"],[class^=" col-sm-"],[class^="col-4"],[class^=" col-4-"]{
      width: 100% !important;
      max-width: 100% !important;
      flex: 0 0 100% !important;
  }
  .newsDesc{
      display: block !important;
  }
}

@media (min-width: 375px) and (max-width: 991px){
    
    .newsDesc {
                display: block;
        
    }
  		
body {
    font-size: 14px !important; 
    

}
    .themeBorderBottom {
        position: absolute;
}
.fixedcontactUs {
    bottom: 10px;    
}
.fixedContactForm {
    bottom: 80px;
}
.marginTop86 {
    margin-top: 5px;
    padding-top: 50px;
}
.resizing_select {
    width: 70px !important;
}
.blogContainer {
    width: 100%;
    height: auto;
}
.blogdesc {
    top: inherit;
    min-height: auto;
}
.blogdesc {
    padding-left:0px;
    transform: translate(-50%,-50%);
}
.blogHeading {
    display: block;
    justify-content: center;
    align-items: center;
    height: auto;
    max-width: auto;
}
.blogdesc {
    
    top: 50% !important;
    
}
.slimScrollDiv,.scrollNews{
    height: auto !important;
}
.footer{
    text-align: center;
}
.footer .col-sm-6{
    text-align: center !important;
}
.blogHeading a {
    color: #fff;
    font-size: 12px;
}
.serbox {
    
    min-height: auto;
}










.back .descriptionSection a {
    font-weight: normal;
    font-size: 14px;
    padding-bottom: 5px;
}
.carouselSection .box {
    height: 250px;

}
#carousel0 .box {
   
    height: 250px;
   
}
.autoHideClick li.nav-item {
    display: block !important;
    width: 100% !important;
}
/* #lightSlider .slide-box {
    display: flex;
    justify-content: flex-end !important;
} */
.catContainer .icons {
    padding-top: 20px;
}
.catdesc p{
    font-size: 14px;
}
#collapseFilter .mb-4,#collapseFilter .mb-3{
    margin-bottom:20px !important;
}
#collapseFilter .assetFilter {
    margin-bottom:20px
} 
#collapseFilter .col-sm-3.mb-3.pl-0{
    padding-left: 15px !important;
}
.borderBottomDark .col-sm-12.mb-4.pr-0{
    padding-right: 15px !important;
} 
.chartArea .noteSection .btn.float-right{
    display:block !important;
    width: 100%;
    margin-top:10px;
}
.Rheading .icons{
    margin-left:0px !important;
    display: block !important;
    background: #eee;
    margin-top: 5px;
    margin-bottom: 5px;
}
.Rheading .icons i{
    position: relative !important;
    left: 0 !important;
}
.mobileNum{
    display: block;
background: #eee;
padding: 10px;
line-height: 19px;
width: 100%;
}
.mobileNum.float-right{
    float: none !important;
}
.mobileNum.float-right .float-right{
    float: none !important;
}
.bkbtn {
    
    top: 0px;
    
    background: #eee;
    width: 100%;
    padding-top: 10px;
    padding-bottom: 10px;
    font-size: 14px;
}
.la-search {
    -ms-transform: rotate(269deg);
    -webkit-transform: rotate(269deg);
    transform: rotate(269deg);
    font-size: 16px !important;
}
.serbox p{font-weight:normal;font-size: 14px;}
.favtabCATSection  .carouselSection .box {
    
    margin-left: 0px !important;
}
.repoAction.inline-block{
    display:block !important;
}
.Rheading .icons{
    text-align:left !important;
}
.favtabCATSection .repoAction   .icons{
    margin-left:0px !important;
    display: block !important;
    background: #eee;
    margin-top: 5px;
    margin-bottom: 5px;
    width: 100%;
    text-align: left !important;
}
.Rheading .icons {
    margin-left: 0px !important;
    display: block !important;
    background: #eee;
    margin-top: 5px;
    margin-bottom: 5px;
    padding: 5px 0px;
}
.mom {
    padding: 20px;
}
#remove-row #LoadMoreButton{
    /* padding-left: 10px; */
}
.serbox{
    min-height: auto;
}
.serbox p {
    min-height: auto !important;
}
#videoIframeId{
    height: auto !important;
}
}
@media screen and (min-width: 320px) and (max-width:569px) {
    body {
        font-size: 14px !important; 
        
    
    } 
    .back .descriptionSection a {
        font-weight: normal;
        font-size: 14px;
        padding-bottom: 5px;
    }
    .carouselSection .box {
        height: 280px;
        background: #fff;
        width: 280px;
        margin-left: -1px;
    }
    .lSSlideWrapper {
        padding-left: 0px;
    }
    .lSSlideWrapper {
        padding-left: 0px;
        /* margin-left: -8px; */
    }
    #carousel0 .box {
        background: #fff !important;
        height: 280px;
        border: 1px solid #d2e2e6 !important;
    }
    #carousel0 .box {
        width: 280px !important;
    }
    #carousel0 .lslide {
        margin-right: -1.2px !important;
    }
    #carousel0 .lSSlideWrapper{
        margin-left: 0px !important;
    }
    /* #lightSlider .slide-box {
        display: flex;
        justify-content: flex-end !important;
    } */
    .catContainer .icons {
        padding-top: 20px;
    }
    .catdesc p{
        font-size: 14px;
    }
    #collapseFilter .mb-4,#collapseFilter .mb-3{
        margin-bottom:20px !important;
    }
    #collapseFilter .assetFilter {
        margin-bottom:20px
    } 
    #collapseFilter .col-sm-3.mb-3.pl-0{
        padding-left: 15px !important;
    }
    .borderBottomDark .col-sm-12.mb-4.pr-0{
        padding-right: 15px !important;
    } 
    .chartArea .noteSection .btn.float-right{
        display:block !important;
        width: 100%;
        margin-top:10px;
    }
    .Rheading .icons{
        margin-left:0px !important;
        display: block !important;
        background: #eee;
        margin-top: 5px;
        margin-bottom: 5px;
    }
    .Rheading .icons i{
        position: relative !important;
        left: 0 !important;
    }
    .mobileNum{
        display: block;
    background: #eee;
    padding: 10px;
    line-height: 19px;
    width: 100%;
    }
    .mobileNum.float-right{
        float: none !important;
    }
    .mobileNum.float-right .float-right{
        float: none !important;
    }
    .bkbtn {
        
        top: 0px;
        
        background: #eee;
        width: 100%;
        padding-top: 10px;
        padding-bottom: 10px;
        font-size: 14px;
    }
    .la-search {
        -ms-transform: rotate(269deg);
        -webkit-transform: rotate(269deg);
        transform: rotate(269deg);
        font-size: 16px !important;
    }
    .serbox p{font-weight:normal;font-size: 14px;}
    .favtabCATSection  .carouselSection .box {
        
        margin-left: 0px !important;
    }
    .repoAction.inline-block{
        display:block !important;
    }
    .Rheading .icons{
        text-align:left !important;
    }
    .favtabCATSection .repoAction   .icons{
        margin-left:0px !important;
        display: block !important;
        background: #eee;
        margin-top: 5px;
        margin-bottom: 5px;
        width: 100%;
        text-align: left !important;
    }
    .Rheading .icons {
        margin-left: 0px !important;
        display: block !important;
        background: #eee;
        margin-top: 5px;
        margin-bottom: 5px;
        padding: 5px 0px;
    }
    .mom {
        padding: 20px;
    }
    #remove-row #LoadMoreButton{
        padding-left: 10px;
    }
    .serbox{
        min-height: auto;
    }
    .serbox p {
        min-height: auto !important;
    }
    #videoIframeId{
        height: auto !important;
    }
    
.footer{text-align:center!important}
.footer .text-right{text-align:center!important}
.blogdesc {
    top: 140px !important;
    min-height: 100px;
}
.blogHeading{
    height: auto;
}

.carouselSection .carousel-inner{
    overflow: hidden !important;
}
.card-style:hover {
    z-index: 2;
    -webkit-transition: none;
    -webkit-transform: none;
    -ms-transition: none;
    -ms-transform: none;
    -moz-transition: none;
    -moz-transform: none;
    transition: none;
    transform: none;
    background-color: #fff !important;
}
.slideTextSection p {
    font-size: 12px;
}
.slideTextSection h2{
    font-size: 22px;
}
/* div.slider-image{
    min-height: 150px;
}
.slick-slide img{
    min-height: 150px;
} */
.slick-current:after {
    content: '';
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none; 
    width: 100%;
    height: 98%;
    top: 0;
    position: absolute;
}
.shapeSection{
    top: 65px !important;
}
#rectangle {
    width: 78px;
    height: 30px;
    background: #7ccc4e;
    display: inline-block;
    margin-left: -4px;
}
.shapeText {
    position: absolute;
    top: 6px;
    color: #fff;
    font-size: 12px;
    left: 11px;
    width: 100%;
    display: block;
}
.carouselSection .box{
    margin-left:0px !important;
}
.fixedContactForm{
    width: 310px !important;
}
/* h1 + select + span.select2-container {
    width: auto !important;
    padding-right: 20px;
    width: 100% !important;
} */
.covidPageM .tab-content{
    padding-left: 0px !important;
    padding-right: 0px !important;
}
.covidPageM .blogContainer,.blogContainer{
    padding-right: 15px !important;
}
.bannerSection,.footerBanner{
    font-size: 1.4em !important;
}
.covidPageM .blogContainer,.blogContainer {
    width: 100%;
    height: auto;
}
.blogdesc {
    top: 50%!important;
    padding: 10px;    
}
.blogHeading {
    height: auto;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}
.blogHeading a {
    color: #fff;
    font-size: 12px;
    line-height: 18px;
}
.catLand #news.col-sm-3 {
    padding-right: 15px;
    padding-left: 15px;
}
#loadReports1 .loadMoreData{
    margin-left: -15px;
}
h1{
    font-size:16px !important;
}
.catheading  a{
    font-size: 16px;
}
.catLand .col-sm-3 {
    padding-right: 10px;
    padding-left: 0px;
    padding-right: 0px;
}
.viewReport{
    height: auto !important;
    width:100% !important;
    min-height: 320px;
}
.viewReport iframe{
    height: auto;
    width: 100% !important;
    min-height: 320px;
}
#remove-row #LoadMoreButton{
    padding-left:0px;
}
.serbox .descriptionSection a {
    min-height: 40px;
    display: block;
    font-size: 16px;
}
tspan{
    font-size: 12px !important;
}

div.slider-image {
    margin-left: 0px;
}
.slick-slider:before {
    box-shadow: 13px 12px 29px 64px rgb(255, 255, 255);
}
.arrowicon {
    font-size: 30px;
    color: #98bac3;
}
.slideControls i {
    font-size: 30px;
}
section#loadTopCategoryMobile {
    overflow-y: auto;
    height: 370px;
}
.secondaryOption .col-sm-5.pl-1 {
    padding-left: 15px !important;
}
.catimageSection {
    height: 131px !important;
}
.Rheading .icons {

    background: #fff;

}
div#grid-viewinsightsReports {
    margin-top: 10px;
}
.socialMediaShare .col-sm-4 {
    width: 25% !important;
    /* display: flex; */
    max-width: 30% !important;
}
input#myInput {
    width: 96%;
}
#cpyLink {
    
    padding-left: 10px;
    padding-top: 10px;
    text-align: center;
    display: block !important;
    margin: 0 auto !important;
}
.blogHeading a {
    color: #fff;
    font-size: 13px;
    line-height: 18px;
}
#remove-row #LoadMoreButton {
    padding-left: 30px;
}
.catimageSection::after {
  box-shadow: -15px -7px 20px 16px rgba(255,255, 255,1);

}
.d-flex.iconDiv::after {
    content: ' ';
    width: 100%;
    position: absolute;
    height: 1px;
    background: #ddd;
    bottom: 10px;
}
#loadSourcingConutriesData .col-sm-12 {
    padding: 15px !important;
}
div#loadTariffData .col-sm-12 {
    padding: 15px !important;
}
#tsc_nav_1 .nav-item a {
    font-size: 12px;
}
}
