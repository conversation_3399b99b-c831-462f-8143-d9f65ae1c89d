<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Blog listing mailer</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Blog listing mailer</h4>
              <div class="mb-2">
                <a
                  href="./downloads/blog-listing-mailer.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <!-- <li><a href="#tab03">CSS</a></li> -->
                <!-- <li><a href="#tab04">Javascript</a></li> -->
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <!-- <li><a href="#tab03">CSS</a></li> -->
                <!-- <li><a href="#tab04">Javascript</a></li> -->
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/blog-listing-mailer/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
<pre class="line-numbers"><code class="language-markup">&lt;table width="620" border="0" cellpadding="0" cellspacing="0" align="center"&gt;
&lt;!-- Header--&gt;
&lt;tbody&gt;
&lt;tr style="background-color:#fff;"&gt;
        &lt;td colspan="3"&gt;
            &lt;img src="./header.jpg" width="620" height="200" alt=""&gt;&lt;/td&gt;
    &lt;/td&gt;
&lt;/tr&gt;
&lt;tr&gt;
&lt;td style="text-align: left;vertical-align: top;padding-bottom: 10px;" colspan="2" width="70%"&gt;&lt;br&gt; 
    &lt;font style="font-family: Corbel, Arial, Corbel, Helvetica, sans-serif; font-size: 24px;color:#9dbdc6;"&gt;
        &lt;b&gt;Latest update&lt;/b&gt;
        &lt;br&gt;
    &lt;/font&gt; 
&lt;/td&gt;
&lt;td style="text-align: right;vertical-align: top;padding-bottom: 0px;" width="30%"&gt;&lt;br&gt; 
    &lt;font style="font-family: Corbel, Arial, Corbel, Helvetica, sans-serif;font-size: 14px;padding-right: 5px;"&gt;&nbsp;&lt;/font&gt; 
&lt;/td&gt;
&lt;/tr&gt;
&lt;tr&gt;
&lt;td style="text-align: left;vertical-align: top;" colspan="3"&gt;
    &lt;font style="font-family: Corbel, Arial, Corbel, Helvetica, sans-serif;font-size: 21px;padding-bottom: 5px;color:#8d959e;"&gt;Lorem ipsum dolor sit amet consectetur adipisicing elit&lt;/font&gt;&lt;br&gt;&lt;br&gt;
    &lt;font style="font-family: Corbel, Arial, Corbel, Helvetica, sans-serif;font-size: 14px;padding-right: 5px;"&gt;         
    &lt;a href="#" style="color:#00af9b; text-decoration: underline;"&gt;Lorem ipsum dolor sit amet consectetur adipisicing elit. &lt;/a&gt;
    &lt;br&gt;
    &lt;font style="font-family: Corbel, Arial, Corbel, Helvetica, sans-serif;font-size: 12px;padding-right: 5px; color:#8d959e;padding-bottom: 10px;"&gt;Posted: 12 October 2022&lt;/font&gt;
    &lt;br&gt;
    Lorem ipsum, dolor sit amet consectetur adipisicing elit. Eius nemo rerum necessitatibus quo quam! Sint est dolores facere non quisquam eveniet tempora quam. Sunt fugit ducimus nisi pariatur ab voluptatibus sint tenetur, recusandae ipsum alias eos ipsa sequi dignissimos veritatis impedit maxime quisquam debitis atque obcaecati omnis quod similique voluptate!
    &lt;br&gt;&lt;br&gt;
    &lt;/font&gt;
    &lt;font style="font-family: Corbel, Arial, Corbel, Helvetica, sans-serif;font-size: 14px;padding-right: 5px;"&gt;         
    &lt;a href="#" style="color:#00af9b; text-decoration: underline;"&gt;Lorem ipsum dolor sit amet consectetur adipisicing elit. &lt;/a&gt;
    &lt;br&gt;
    &lt;font style="font-family: Corbel, Arial, Corbel, Helvetica, sans-serif;font-size: 12px;padding-right: 5px; color:#8d959e;padding-bottom: 10px;"&gt;Posted: 12 October 2022&lt;/font&gt;
    &lt;br&gt;
    Lorem ipsum, dolor sit amet consectetur adipisicing elit. Eius nemo rerum necessitatibus quo quam! Sint est dolores facere non quisquam eveniet tempora quam. Sunt fugit ducimus nisi pariatur ab voluptatibus sint tenetur, recusandae ipsum alias eos ipsa sequi dignissimos veritatis impedit maxime quisquam debitis atque obcaecati omnis quod similique voluptate!
    &lt;br&gt;&lt;br&gt;
    &lt;/font&gt;
    

    &lt;br&gt;



    &lt;font style="font-family: Corbel, Arial, Corbel, Helvetica, sans-serif;font-size: 21px;padding-bottom: 5px;color:#8d959e;"&gt;Corporate services&lt;/font&gt;&lt;br&gt;&lt;br&gt;
    &lt;font style="font-family: Corbel, Arial, Corbel, Helvetica, sans-serif;font-size: 14px;padding-right: 5px;"&gt;         
    &lt;a href="#" style="color:#00af9b; text-decoration: underline;"&gt;Lorem ipsum dolor sit amet consectetur adipisicing elit. &lt;/a&gt;
    &lt;br&gt;
    &lt;font style="font-family: Corbel, Arial, Corbel, Helvetica, sans-serif;font-size: 12px;padding-right: 5px; color:#8d959e;padding-bottom: 10px;"&gt;Posted: 12 October 2022&lt;/font&gt;
    &lt;br&gt;
    Lorem ipsum, dolor sit amet consectetur adipisicing elit. Eius nemo rerum necessitatibus quo quam! Sint est dolores facere non quisquam eveniet tempora quam. Sunt fugit ducimus nisi pariatur ab voluptatibus sint tenetur, recusandae ipsum alias eos ipsa sequi dignissimos veritatis impedit maxime quisquam debitis atque obcaecati omnis quod similique voluptate!
    &lt;br&gt;&lt;br&gt;
    &lt;/font&gt;
    &lt;font style="font-family: Corbel, Arial, Corbel, Helvetica, sans-serif;font-size: 14px;padding-right: 5px;"&gt;         
    &lt;a href="#" style="color:#00af9b; text-decoration: underline;"&gt;Lorem ipsum dolor sit amet consectetur adipisicing elit. &lt;/a&gt;
    &lt;br&gt;
    &lt;font style="font-family: Corbel, Arial, Corbel, Helvetica, sans-serif;font-size: 12px;padding-right: 5px; color:#8d959e;padding-bottom: 10px;"&gt;Posted: 12 October 2022&lt;/font&gt;
    &lt;br&gt;
    Lorem ipsum, dolor sit amet consectetur adipisicing elit. Eius nemo rerum necessitatibus quo quam! Sint est dolores facere non quisquam eveniet tempora quam. Sunt fugit ducimus nisi pariatur ab voluptatibus sint tenetur, recusandae ipsum alias eos ipsa sequi dignissimos veritatis impedit maxime quisquam debitis atque obcaecati omnis quod similique voluptate!
    &lt;br&gt;&lt;br&gt;
    &lt;/font&gt;
    &lt;font style="font-family: Corbel, Arial, Corbel, Helvetica, sans-serif;font-size: 14px;padding-right: 5px;"&gt;
    &lt;br&gt;
    &lt;br&gt; 
    Thanks,
    &lt;br&gt;
    Amplifi PRO team
    &lt;br&gt;
    The Smart Cube
&lt;/font&gt;&lt;/td&gt;
&lt;/tr&gt;
&lt;tr&gt;
&lt;td colspan="3"&gt;&nbsp;&lt;/td&gt;
&lt;/tr&gt;

&lt;tr&gt;
&lt;td align="left" colspan="3"&gt;
&lt;img src="./bar.png" style="text-align:center;padding-top: 10px;padding-bottom: 20px;height: 12px; width: 100%;"&gt;
&lt;/td&gt;
&lt;/tr&gt;
&lt;tr&gt;
&lt;td align="left" colspan="3"&gt;
    &lt;a href="https://thesmartcube.com" style="font-weight: bold; text-decoration: none;color: #00af9b;
    font-family: Corbel, Arial, Corbel, Helvetica,sans-serif;font-size: 14px; padding-bottom: 10px;"&gt;
        thesmartcube.com
    &lt;/a&gt;&lt;br&gt;&lt;br&gt;
    
        &lt;a href="https://www.facebook.com/The.Smart.Cube" style="margin-right: 10px;padding-bottom: 10px;"&gt;
        &lt;img src="./fb.png"&gt;&lt;/a&gt;
        &nbsp;&nbsp;
        &lt;a href="https://twitter.com/TSCinsights" style="margin-right: 10px;"&gt;&lt;img src="./tw.png"&gt;&lt;/a&gt;
        &nbsp;&nbsp;
        &lt;a href="https://www.linkedin.com/company/the-smart-cube"&gt;&lt;img src="./in.png"&gt;&lt;/a&gt;&lt;br&gt;&lt;br&gt;
    
&lt;/td&gt;
&lt;/tr&gt;
&lt;tr&gt;
&lt;td style="margin: 0; padding: 0; font-family: Corbel, Arial, Corbel, Helvetica, sans-serif; color: #666; font-size: 10px;padding-right: 10px;" colspan="3"&gt;
    &lt;p style="text-decoration: none;font-weight: bold;color: #00af9b;font-size: 12px;padding-top: 10px;padding-bottom: 5px;
font-family: Corbel, Arial, Corbel, sans-serif; "&gt;Disclaimer&lt;/p&gt;
    This is a system-generated email; please do not reply.
    &lt;br&gt;
    This e-mail may contain confidential and/or legally privileged
    information. If you are not the intended recipient (or have received
    this message in error) please notify the sender immediately and
    delete this e-mail. Any unauthorised copying, disclosure,
    distribution, or any action taken or omitted to be taken in reliance
    on the material in this email is strictly forbidden and may be
    unlawful.
&lt;/td&gt;
&lt;/tr&gt;
&lt;tr&gt;&lt;td colspan="3"&gt;&nbsp;&lt;/td&gt;&lt;/tr&gt;
&lt;tr&gt;&lt;td colspan="3"&gt;&nbsp;&lt;/td&gt;&lt;/tr&gt;
&lt;/tbody&gt;
&lt;/table&gt;
</code>
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">

              </code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">

            </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
