
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Anniversary mailer Template</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Anniversary mailer template</h4>
              <div class="mb-2">
                <a
                  href="./downloads/anniversary-mailer.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <!-- <li><a href="#tab03">CSS</a></li> -->
                <!-- <li><a href="#tab04">Javascript</a></li> -->
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <!-- <li><a href="#tab03">CSS</a></li> -->
                <!-- <li><a href="#tab04">Javascript</a></li> -->
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/anniversary-mailer/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
<pre class="line-numbers">
<code class="language-markup">   
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
&lt;title&gt;The Smart Cube&lt;/title&gt;
&lt;meta charset="utf-8"&gt;
&lt;meta name="viewport" content="width=device-width, initial-scale=1"&gt;
&lt;meta http-equiv="X-UA-Compatible" content="IE=edge" /&gt;
&lt;style type="text/css"&gt;
    /* CLIENT-SPECIFIC STYLES */
    body, table, td, a{-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;} /* Prevent WebKit and Windows mobile changing default text sizes */
    table, td{mso-table-lspace: 0pt; mso-table-rspace: 0pt;} /* Remove spacing between tables in Outlook 2007 and up */
    img{-ms-interpolation-mode: bicubic;} /* Allow smoother rendering of resized image in Internet Explorer */

    /* RESET STYLES */
    img{border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none;}
    table{border-collapse: collapse !important;}
    body{height: 100% !important; margin: 0 !important; padding: 0 !important; width: 100% !important; background: #fff}

    /* iOS BLUE LINKS */
    a[x-apple-data-detectors] {
        color: inherit !important;
        text-decoration: none !important;
        font-size: inherit !important;
        font-family: inherit !important;
        font-weight: inherit !important;
        line-height: inherit !important;
    }

    /* MOBILE STYLES */
    @media screen and (max-width: 525px) {

        /* ALLOWS FOR FLUID TABLES */
        .wrapper {
            width: 100% !important;
            max-width: 100% !important;
        }

        /* ADJUSTS LAYOUT OF LOGO IMAGE */
        .logo img {
            margin: 0 auto !important;
        }

        /* USE THESE CLASSES TO HIDE CONTENT ON MOBILE */
        .mobile-hide {
            display: none !important;
        }

        .img-max {
            max-width: 100% !important;
            width: 100% !important;
            height: auto !important;
        }

        /* FULL-WIDTH TABLES */
        .responsive-table {
            width: 100% !important;
        }

        /* UTILITY CLASSES FOR ADJUSTING PADDING ON MOBILE */
        .padding {
            padding: 10px 5% 15px 5% !important;
        }

        .padding-meta {
            padding: 5px 5% 0px 5% !important;
        }

        .no-padding {
            padding: 0 !important;
        }

        .section-padding {
            padding: 5px 35px 30px 35px !important;
        }

    }

    /* ANDROID CENTER FIX */
    div[style*="margin: 16px 0;"] { margin: 0 !important; }
&lt;/style&gt;
&lt;/head&gt;
&lt;body style="margin: 0 !important; padding: 0 !important;"&gt;

&lt;!-- HEADER --&gt;
&lt;table border="0" cellpadding="0" cellspacing="0" width="661" bgcolor="#d1dce0" align="center"&gt;
    &lt;tr&gt;
        &lt;td align="center"&gt;
            &lt;!--[if (gte mso 9)|(IE)]&gt;
            &lt;table align="center" border="0" cellspacing="0" cellpadding="0" width="661"&gt;
            &lt;tr&gt;
            &lt;td align="center" valign="top" width="661"&gt;
            &lt;![endif]--&gt;
            &lt;table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 661px;" class="wrapper"&gt;
                &lt;tr&gt;
                    &lt;td align="left" valign="top"&gt;
                        &lt;img src="images/Header.jpg" width="661" height="213" border="0" alt="Best wishes for the day!"  style="display: block; font-family: 'Corbel', Arial, sans-serif; color: #ffffff; font-size: 16pt; text-decoration: none;"/&gt;
                    &lt;/td&gt;
                    
                    
                &lt;/tr&gt;
            &lt;/table&gt;
            &lt;!--[if (gte mso 9)|(IE)]&gt;
            &lt;/td&gt;
            &lt;/tr&gt;
            &lt;/table&gt;
            &lt;![endif]--&gt;
        &lt;/td&gt;
    &lt;/tr&gt;
    
    
    &lt;tr&gt;
        &lt;td align="center"&gt;
            &lt;!--[if (gte mso 9)|(IE)]&gt;
            &lt;table align="center" border="0" cellspacing="0" cellpadding="0" width="661"&gt;
            &lt;tr&gt;
            &lt;td align="center" valign="top" width="601"&gt;
            &lt;![endif]--&gt;
            &lt;table border="0" cellpadding="0" cellspacing="0" width="100%"  bgcolor="#ffffff" style="padding-bottom: 20px; max-width: 601px;" class="responsive-table"&gt;
                &lt;!-- TITLE --&gt;
                &lt;tr&gt;
                    &lt;td align="center" height="100%" valign="top" width="100%" style="padding: 5px 35px 30px 35px;" class="section-padding"&gt;
                        &lt;table align="left" border="0" cellpadding="0" cellspacing="0" width="100%"&gt;
                                            &lt;tr&gt;

                                                &lt;td style="padding: 20px 0 0 0;" class="no-padding"&gt;
                                                    &lt;!-- ARTICLE --&gt;
                                                    &lt;table border="0" cellspacing="0" cellpadding="0" width="100%"&gt;
                                                        
                                                        &lt;tr&gt;
                                                            &lt;td align="left" style="padding: 0 0 5px 0; font-size: 18pt; font-family:'Corbel', Arial, sans-serif; font-weight: normal; color: #338e85;" class="padding" height="30"&gt;&lt;strong&gt;Dear Gaurav,&lt;/strong&gt;&lt;/td&gt;
                                                        &lt;/tr&gt;
                                                        &lt;tr&gt;
                                                            &lt;td align="left" style="padding: 0 0 5px 0;" class="padding-meta"&gt;
                                                            &lt;img src="images/Congratulations1.jpg" width="546" height="287" alt="Congratulations"/&gt;
                                                            
                                                            &lt;/td&gt;
                                                        &lt;/tr&gt;
                                                        &lt;tr&gt;
                                                                &lt;td align="left" style="padding: 5px 0 15px 0; font-size: 14pt; line-height: 18pt; font-family: 'Corbel', Arial, sans-serif; color: #1e2a39;" class="padding"&gt;
                                                            On behalf of all in TSC, on completing 3 years with the firm.&lt;br&gt;
We wish you all the very best for the coming years…&lt;/td&gt;
                                                        &lt;/tr&gt;
                                                        &lt;tr&gt;
                                                            &lt;td style="padding:0 0 20px 0; font-size: 14pt; line-height: 18pt; font-family: 'Corbel', Arial, sans-serif; color: #1e2a39;" align="left" class="padding"&gt;
                                                                Warm Wishes&lt;br&gt;
                                                                &lt;strong style="font-size: 14pt; line-height: 22px; font-family: 'Corbel', Arial, sans-serif; color: #33beaf"&gt;TSC HR Team&lt;/strong&gt;
                                                            &lt;/td&gt;
                                                        &lt;/tr&gt;

                                                    &lt;/table&gt;
                                                &lt;/td&gt;
                                            &lt;/tr&gt;
                                        &lt;/table&gt;
                    &lt;/td&gt;
                &lt;/tr&gt;
                
            &lt;/table&gt;
            &lt;!--[if (gte mso 9)|(IE)]&gt;
            &lt;/td&gt;
            &lt;/tr&gt;
            &lt;/table&gt;
            &lt;![endif]--&gt;
        &lt;/td&gt;
    &lt;/tr&gt;
    &lt;tr&gt;
        &lt;td align="center" style="padding: 20px;"&gt;
            &lt;!--[if (gte mso 9)|(IE)]&gt;
            &lt;table align="center" border="0" cellspacing="0" cellpadding="0" width="661"&gt;
            &lt;tr&gt;
            &lt;td align="center" valign="top" width="661"&gt;
            &lt;![endif]--&gt;
            &lt;!-- UNSUBSCRIBE COPY --&gt;
            &lt;table width="100%" border="0" cellspacing="0" cellpadding="0" align="center" style="max-width: 661px;" class="responsive-table"&gt;
                &lt;tr&gt;
                    &lt;td align="center" style="font-size: 12pt; line-height: 14pt; font-family: 'Corbel', Arial, sans-serif; color:#666666;"&gt;
                        &lt;a href="http://www.thesmartcube.com" target="_blank" style="font-family: Arial, sans-serif; font-size: 14px; color: #338e85; font-weight: 600; text-decoration: none"&gt;thesmartcube.com&lt;/a&gt;&lt;br&gt;&lt;br style="height: 10px;"&gt;
                        
                    &lt;a href="https://www.facebook.com/The.Smart.Cube" target="_blank" style="font-family: Arial, sans-serif; font-size: 14px; color: #338e85;"&gt;&lt;img src="images/facebook.jpg" width="28" height="28" border="0" alt="Facebook"/&gt;&lt;/a&gt;
                    &nbsp; &nbsp;
                        &lt;a href="https://twitter.com/TSCinsights" target="_blank" style="font-family: Arial, sans-serif; font-size: 14px; color: #338e85;"&gt;&lt;img src="images/twitter.jpg" width="28" height="28" border="0" alt="Facebook"/&gt;&lt;/a&gt;
                        &nbsp; &nbsp;
                        &lt;a href="https://www.linkedin.com/company/the-smart-cube" target="_blank" style="font-family: Arial, sans-serif; font-size: 14px; color: #338e85;"&gt;&lt;img src="images/linkedin.jpg" width="28" height="28" border="0" alt="Facebook"/&gt;&lt;/a&gt;
                    &lt;/td&gt;
                &lt;/tr&gt;
            &lt;/table&gt;
            &lt;!--[if (gte mso 9)|(IE)]&gt;
            &lt;/td&gt;
            &lt;/tr&gt;
            &lt;/table&gt;
            &lt;![endif]--&gt;
        &lt;/td&gt;
    &lt;/tr&gt;
&lt;/table&gt;
&lt;/body&gt;
&lt;/html&gt;  
</code>  
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">

              </code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">

            </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
