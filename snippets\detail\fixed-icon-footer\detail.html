
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
      <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
      <div class="col-sm-12 p-0">
        <div class="d-flex justify-content-between">
                <h4 class="size20 mb-2 pt-2">Fixed icon footer layout</h4> 
                <div class="mb-2">
                  <a
                    href="./downloads/fixed-icon-footer.zip"
                    class="btn btn-sm btn-outline-primary btn-dark mr-2"
                    ><i class="fas fa-download"></i> Download zip</a
                  >
                  <button class="btn btn-sm btn-outline-info" id="fullScreen">
                    <i class="fas fa-expand"></i> View on Fullscreen
                  </button>
                </div>             
          </div>   
    </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
          
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
            <!-- <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/multiselect/index.html"></iframe> -->
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/fixed-icon-footer/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
 
  &lt;div class="page-wrap"&gt;
  &lt;main class="page-main"&gt;
    &lt;article&gt;
      &lt;div class="section"&gt;
        &lt;div class="article-header"&gt;
          &lt;h1&gt;Bigfoot&lt;/h1&gt;
          &lt;h2&gt;Fact or fiction? &lt;/h2&gt;
          &lt;p&gt;Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dignissimos laborum cumque incidunt, enim ipsa dicta? Porro illo doloribus, consectetur eum exercitationem sit ipsam, est nesciunt maxime, eius animi dolor? Harum.&lt;/p&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      &lt;div class="section"&gt;
        &lt;div class="article-content"&gt;
          &lt;p class="caption"&gt;Photo by John Moeses Bauan for Unsplash&lt;/p&gt;
          &lt;p&gt;Illo numquam, sapiente neque repellendus facere amet doloribus asperiores quia eum? Sunt vero amet neque vel? Tempora, nulla voluptatum amet autem culpa magnam debitis! Dolores esse quam amet nobis ut.&lt;/p&gt;
          &lt;p&gt;Sunt excepturi in nostrum, fugiat veritatis ab sit sequi nemo aperiam deserunt temporibus, dolorem ex adipisci autem. Quasi, iure fugiat! Nulla amet doloribus velit nam tempora, soluta consequatur doloremque omnis?&lt;/p&gt;
          &lt;p&gt; Aenean iaculis odio et eleifend elementum. Pellentesque porta lacus sit amet ligula tristique ornare. Ut euismod suscipit enim. Morbi eu molestie odio. Nullam pellentesque bibendum magna non aliquet. Praesent commodo metus ut dapibus sollicitudin. Etiam tristique arcu et risus venenatis egestas. Mauris quis feugiat leo. Mauris scelerisque eleifend risus a suscipit. Praesent commodo pharetra vehicula. Praesent imperdiet luctus ex eget consectetur. Sed facilisis lobortis dolor quis scelerisque. Pellentesque porta orci nec viverra semper.&lt;/p&gt;
          &lt;p&gt;In nec ornare mauris. Ut bibendum libero sit amet leo finibus ornare. Donec vel ultrices velit, vel congue elit. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos. Etiam blandit sem nisi, eget fringilla risus tristique sit amet. Ut quis dictum massa, nec tincidunt urna. Aliquam dignissim mi at eros egestas elementum. Phasellus tempor lectus id consectetur finibus. Etiam felis augue, ullamcorper sed pretium dictum, interdum sed nulla. Morbi eu ante ac tortor suscipit posuere. Praesent feugiat, dolor sed condimentum tincidunt, massa tortor congue lorem, eget sollicitudin nulla nibh sit amet justo. Pellentesque erat quam, iaculis pellentesque eros non, accumsan laoreet quam.&lt;/p&gt;
          &lt;p&gt;Praesent nec lacus urna. Pellentesque hendrerit turpis sed dictum vehicula. Ut in sapien odio. Sed vitae finibus nisl. Pellentesque dui nisl, condimentum facilisis feugiat nec, dictum quis nisi. Donec pellentesque ornare mauris non congue. Integer consequat nulla at odio congue luctus.&lt;/p&gt;
          &lt;p&gt;Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Quisque lobortis mattis turpis eu commodo. Donec diam massa, convallis at ex non, finibus lacinia ipsum. Vestibulum rutrum nunc mollis condimentum mollis. Nunc at nunc mauris. Proin magna nisl, imperdiet imperdiet malesuada a, gravida ac dolor. Vivamus ex ipsum, convallis vitae egestas in, dignissim eu augue. Nullam euismod orci lectus, ut tempor mauris malesuada sit amet.&lt;/p&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/article&gt;
  &lt;/main&gt;
  &lt;footer class="page-footer"&gt;
    &lt;div class="content"&gt;
      &lt;nav&gt;
        &lt;div class="link-list"&gt;
          &lt;a href="#"&gt;West Coast&lt;/a&gt;
          &lt;a href="#"&gt;Great Lakes Region&lt;/a&gt;
          &lt;a href="#"&gt;Southeastern US&lt;/a&gt;
          &lt;a href="#"&gt;East Coast&lt;/a&gt;
        &lt;/div&gt;
        &lt;div class="icon-container"&gt;
          &lt;span class="material-icons"&gt;search&lt;/span&gt;
          &lt;h4&gt;Sightings&lt;/h4&gt;
        &lt;/div&gt;
      &lt;/nav&gt;
      &lt;nav&gt;
        &lt;div class="link-list"&gt;
          &lt;a href="#"&gt;Studies in the 1970s&lt;/a&gt;
          &lt;a href="#"&gt;Studies in the 1990s - 2000s&lt;/a&gt;
          &lt;a href="#"&gt;Contemporary Studies&lt;/a&gt;
        &lt;/div&gt;
        &lt;div class="icon-container"&gt;
          &lt;span class="material-icons-outlined"&gt;article&lt;/span&gt;
          &lt;h4&gt;Formal Studies&lt;/h4&gt;
        &lt;/div&gt;
      &lt;/nav&gt;
      &lt;nav&gt;
        &lt;div class="link-list"&gt;
          &lt;a href="#"&gt;Bigfoot in Movies & TV&lt;/a&gt;
          &lt;a href="#"&gt;Laws about Bigfoot&lt;/a&gt;
          &lt;a href="#"&gt;Share Your Story&lt;/a&gt;
        &lt;/div&gt;
        &lt;div class="icon-container"&gt;
          &lt;span class="material-icons-outlined"&gt;live_tv&lt;/span&gt;
          &lt;h4&gt;Popular Culture&lt;/h4&gt;
        &lt;/div&gt;
      &lt;/nav&gt;
    &lt;/div&gt;
  &lt;/footer&gt;
&lt;/div&gt;
</code>
</pre>
           
        </div>  
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
           
            * {
              box-sizing: border-box;
              padding: 0;
              margin: 0;
              font-family: "Roboto", sans-serif;
              color: #f8f9fa;
            }
            h1, h2, h3, h4 {
              font-family: "Raleway", sans-serif;
            }
            .page-wrap {
              display: flex;
              flex-direction: column;
              align-items: center;
              background-image: url(https://images.unsplash.com/photo-1537255263864-b779ce1854ff?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80);
              background-repeat: no-repeat;
              background-attachment: fixed;
              background-position: center center;
              background-size: cover;
            }
            .page-wrap .page-main {
              width: 100%;
              margin-bottom: 81px;
            }
            .page-wrap .page-main article .section {
              background-color: #212529;
              width: 100%;
              display: flex;
              flex-direction: column;
              align-items: center;
            }
            .page-wrap .page-main article .section:first-child {
              margin-bottom: 500px;
            }
            .page-wrap .page-main article .article-header {
              margin-bottom: 30px;
              max-width: 700px;
              padding: 30px 20px 0;
            }
            .page-wrap .page-main article .article-header h1 {
              font-size: 50px;
            }
            .page-wrap .page-main article .article-header h2 {
              font-size: 35px;
              margin-bottom: 40px;
            }
            .page-wrap .page-main article .article-header p {
              font-size: 20px;
              line-height: 1.4;
            }
            .page-wrap .page-main article .article-content {
              background-color: #212529;
              margin-top: 5px;
              max-width: 700px;
              padding: 0 20px;
              margin-bottom: 20px;
            }
            .page-wrap .page-main article .article-content p {
              font-size: 20px;
              line-height: 1.4;
              margin-bottom: 15px;
            }
            .page-wrap .page-main article .article-content p.caption {
              font-weight: 300;
              font-size: 15px;
              line-height: 1.2;
              text-align: center;
              margin-bottom: 40px;
            }
            .page-wrap .page-footer {
              background-color: #141414;
              width: 100%;
              position: fixed;
              bottom: 0;
              display: flex;
              justify-content: center;
              border-top: 1px solid #424242;
            }
            .page-wrap .page-footer .content {
              display: flex;
              max-width: 700px;
              min-width: 700px;
              display: flex;
            }
            .page-wrap .page-footer .content nav {
              flex: 1;
              text-align: center;
              display: flex;
              flex-direction: column;
              align-items: center;
            }
            .page-wrap .page-footer .content nav:hover .icon-container h4 {
              height: 21px;
              opacity: 1;
            }
            .page-wrap .page-footer .content nav:hover .link-list {
              transform: translateY(-100%);
            }
            .page-wrap .page-footer .content nav .icon-container {
              padding: 10px;
              height: 81px;
              display: flex;
              flex-direction: column;
              justify-content: center;
              cursor: pointer;
              z-index: 2;
              width: 100%;
              background-color: #141414;
            }
            .page-wrap .page-footer .content nav .icon-container span {
              font-size: 40px;
            }
            .page-wrap .page-footer .content nav .icon-container h4 {
              font-size: 18px;
              height: 0;
              opacity: 0;
              transition: 0.3s;
            }
            .page-wrap .page-footer .content nav .link-list {
              position: absolute;
              display: flex;
              flex-direction: column;
              transform: translateY(0);
              background-color: #141414;
              border: 1px solid #424242;
              border-bottom: 0;
              width: calc(700px / 3);
              z-index: 1;
              transition: 0.3s;
            }
            .page-wrap .page-footer .content nav .link-list a {
              list-style-type: none;
              font-size: 16px;
              padding: 8px;
              text-decoration: none;
            }
            .page-wrap .page-footer .content nav .link-list a:hover {
              background-color: #1e1e1e;
            }
          </code></pre>
        </div> 
        <div id="tab04" class="tab-contents">
          <pre class="line-numbers">
            <code class="language-javascript">
              
            </code>
          </pre>
        </div>       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="../../../js/select2.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <script src="./js/iframe.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
