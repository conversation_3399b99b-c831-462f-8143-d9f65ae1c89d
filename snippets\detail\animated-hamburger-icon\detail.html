<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Component Library</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">
      <div class="container-fluid mt-1">
          <div class="row">
            <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Hamburger Animation</h4>
                  <div class="mb-2">
                      <a href="./downloads/animated-hamburger-icon.zip" class="btn btn-sm btn-outline-primary btn-dark mr-2"><i class="fas fa-download"></i>  Download zip</a> <button class="btn btn-sm btn-outline-info" id="fullScreen"><i class="fas fa-expand"></i>  View on Fullscreen</button>
                  </div>
              </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/animated-hamburger-icon/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
              <pre class="line-numbers">
<code class="language-markup">
    &lt;section class="row-section"&gt;
    &lt;div&gt;
      &lt;h1&gt;Hamburger Icons Animations&lt;/h1&gt;
      &lt;hr style="width: 20%" /&gt;
      &lt;div class="menuOne"&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
      &lt;/div&gt;
      &lt;div class="menuTwo"&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
      &lt;/div&gt;
      &lt;div class="menuThree"&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
      &lt;/div&gt;
      &lt;div class="menuFour"&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
      &lt;/div&gt;
      &lt;div class="menuFive"&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
      &lt;/div&gt;
  
      &lt;div class="menuSix"&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
      &lt;/div&gt;
      &lt;div class="menuSeven"&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
      &lt;/div&gt;
      &lt;div class="menuEight"&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
        &lt;span&gt;&lt;/span&gt;
      &lt;/div&gt;
      &lt;div style="padding-bottom: 1px"&gt;&lt;/div&gt;
    &lt;/div&gt;
  &lt;/section&gt;
     
</code>
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">
body{padding:0;margin:0;background:no-repeat #002035;background-image:linear-gradient(#002035 50%,#002035);text-align:center;background-size:100% 100%;height:100%;}
div{margin:10% 30px!important;}
.menuOne,.menuTwo,.menuThree,.menuFour{margin:10px;cursor:pointer;}
h1{color:#fff!important;text-shadow:1px 1px 1px #000;}
.menuOne{width:30px;height:30px;position:relative;transition:.1s;display:inline-block;}
.menuOne span{width:5px;height:5px;background-color:#fff;display:block;border-radius:50%;box-shadow:1px 1px 1px #000!important;}
.menuOne:hover span{transform:scale(1.3);transition:350ms cubic-bezier(.8,.5,.2,1.4);box-shadow:0 2px 3px rgba(0,0,0,.4);}
.menuOne span:nth-child(1){position:absolute;left:0;top:0;}
.menuOne span:nth-child(2){position:absolute;left:12px;top:0;}
.menuOne span:nth-child(3){position:absolute;right:0;top:0;}
.menuOne span:nth-child(4){position:absolute;left:0;top:12px;}
.menuOne span:nth-child(5){position:absolute;left:12px;top:12px;}
.menuOne span:nth-child(6){position:absolute;right:0;top:12px;}
.menuOne span:nth-child(7){position:absolute;left:0;bottom:0;}
.menuOne span:nth-child(8){position:absolute;left:12px;bottom:0;}
.menuOne span:nth-child(9){position:absolute;right:0;bottom:0;}
.clickMenuOne{transform:rotate(180deg);cursor:pointer;transition:.2s cubic-bezier(.8,.5,.2,1.4);}
.clickMenuOne span{border-radius:50%;background-color:rgba(255,189,189,.767);transition:.5s cubic-bezier(.8,.5,.2,1.4);}
.menuEight span,.menuFive span,.menuFour span,.menuSeven span,.menuSix span,.menuThree span,.menuTwo span{background-color:#FFF;border-radius:2px;box-shadow:1px 1px 1px #000!important;}
.clickMenuOne span:nth-child(2){position:absolute;left:6px;top:6px;}
.clickMenuOne span:nth-child(4){position:absolute;left:6px;top:18px;}
.clickMenuOne span:nth-child(6){position:absolute;right:6px;top:6px;}
.clickMenuOne span:nth-child(8){position:absolute;left:18px;bottom:6px;}
.menuTwo{width:35px;height:30px;position:relative;display:inline-block;}
.menuTwo span{position:absolute;transition:.3s cubic-bezier(.8,.5,.2,1.4);}
.menuTwo span:nth-child(1){width:100%;height:4px;display:block;top:0;left:0;}
.menuTwo span:nth-child(2){width:100%;height:4px;display:block;top:13px;left:0;}
.menuTwo span:nth-child(3){width:100%;height:4px;display:block;bottom:0;left:0;}
.menuTwo:not(.clickMenuTow):hover span:nth-child(1){width:100%;height:4px;display:block;top:-2px;left:0;transition:.3s cubic-bezier(.8,.5,.2,1.4);}
.menuTwo:not(.clickMenuTow):hover span:nth-child(2){width:100%;height:4px;display:block;top:13px;left:0;transition:.4s cubic-bezier(.8,.5,.2,1.4);}
.menuTwo:not(.clickMenuTow):hover span:nth-child(3){width:100%;height:4px;display:block;bottom:-2px;left:0;transition:.3s cubic-bezier(.8,.5,.2,1.4);}
.clickMenuTow span:nth-child(1){left:3px;top:12px;width:30px;transition:.3s cubic-bezier(.8,.5,.2,1.4);transform:rotate(90deg);transition-delay:150ms;}
.clickMenuTow span:nth-child(2){left:2px;top:20px;width:20px;transition:.3s cubic-bezier(.8,.5,.2,1.4);transform:rotate(45deg);transition-delay:50ms;}
.clickMenuTow span:nth-child(3){left:14px;top:20px;width:20px;transition:.3s cubic-bezier(.8,.5,.2,1.4);transform:rotate(-45deg);transition-delay:.1s;}
.menuThree{width:35px;height:30px;position:relative;display:inline-block;}
.menuThree span{position:absolute;transition:.3s cubic-bezier(.8,.5,.2,1.4);}
.menuThree span:nth-child(1){width:100%;height:4px;display:block;top:0;left:0;}
.menuThree span:nth-child(2){width:100%;height:4px;display:block;top:13px;left:0;}
.menuThree span:nth-child(3){width:100%;height:4px;display:block;bottom:0;left:0;}
.menuThree:not(.clickMenuThree):hover span:nth-child(1){width:100%;height:4px;display:block;top:-2px;left:0;transition:.3s cubic-bezier(.8,.5,.2,1.4);}
.menuThree:not(.clickMenuThree):hover span:nth-child(2){width:100%;height:4px;display:block;top:13px;left:0;transition:.4s cubic-bezier(.8,.5,.2,1.4);}
.menuThree:not(.clickMenuThree):hover span:nth-child(3){width:100%;height:4px;display:block;bottom:-2px;left:0;transition:.3s cubic-bezier(.8,.5,.2,1.4);}
.clickMenuThree{transform:rotate(-90deg);}
.clickMenuThree span:nth-child(1){left:3px;top:12px;width:30px;transition:.3s cubic-bezier(.8,.5,.2,1.4);transform:rotate(90deg);transition-delay:150ms;}
.clickMenuThree span:nth-child(2){left:2px;top:20px;width:20px;transition:.3s cubic-bezier(.8,.5,.2,1.4);transform:rotate(45deg);transition-delay:50ms;}
.clickMenuThree span:nth-child(3){left:14px;top:20px;width:20px;transition:.3s cubic-bezier(.8,.5,.2,1.4);transform:rotate(-45deg);transition-delay:.1s;}
.menuFour{width:35px;height:30px;position:relative;display:inline-block;}
.menuFour span{position:absolute;transition:.3s cubic-bezier(.8,.5,.2,1.4);}
.menuFour span:nth-child(1){width:100%;height:4px;display:block;top:0;left:0;}
.menuFour span:nth-child(2){width:100%;height:4px;display:block;top:13px;left:0;}
.menuFour span:nth-child(3){width:100%;height:4px;display:block;bottom:0;left:0;}
.menuFour:not(.clickMenuFour):hover span:nth-child(1){width:100%;height:4px;display:block;top:-2px;left:0;transition:.3s cubic-bezier(.8,.5,.2,1.4);}
.menuFour:not(.clickMenuFour):hover span:nth-child(2){width:100%;height:4px;display:block;top:13px;left:0;transition:.4s cubic-bezier(.8,.5,.2,1.4);}
.menuFour:not(.clickMenuFour):hover span:nth-child(3){width:100%;height:4px;display:block;bottom:-2px;left:0;transition:.3s cubic-bezier(.8,.5,.2,1.4);}
.menuEight,.menuFive,.menuSeven,.menuSix{margin:10px;cursor:pointer;display:inline-block;}
.clickMenuFour{transform:rotate(90deg);}
.clickMenuFour span:nth-child(1){left:3px;top:12px;width:30px;transition:.3s cubic-bezier(.8,.5,.2,1.4);transform:rotate(90deg);transition-delay:150ms;}
.clickMenuFour span:nth-child(2){left:2px;top:20px;width:20px;transition:.3s cubic-bezier(.8,.5,.2,1.4);transform:rotate(45deg);transition-delay:50ms;}
.clickMenuFour span:nth-child(3){left:14px;top:20px;width:20px;transition:.3s cubic-bezier(.8,.5,.2,1.4);transform:rotate(-45deg);transition-delay:.1s;}
.menuFive{width:35px;height:30px;position:relative;}
.menuFive span{position:absolute;transition:.3s cubic-bezier(.8,.5,.2,1.4);width:100%;height:4px;transition-duration:.5s;}
.menuFive span:nth-child(1){top:0;left:0;}
.menuFive span:nth-child(2){top:13px;left:0;}
.menuFive span:nth-child(3){bottom:0;left:0;}
.menuFive:not(.clickMenuFive):hover span:nth-child(1){transform:rotate(-3deg) scaleY(1.1);}
.menuFive:not(.clickMenuFive):hover span:nth-child(2){transform:rotate(3deg) scaleY(1.1);}
.menuFive:not(.clickMenuFive):hover span:nth-child(3){transform:rotate(-4deg) scaleY(1.1);}
.clickMenuFive span:nth-child(1){transform:rotate(45deg);top:13px;}
.clickMenuFive span:nth-child(2){transform:scale(.1);}
.clickMenuFive span:nth-child(3){transform:rotate(-45deg);top:13px;}
.menuSix{width:35px;height:30px;position:relative;}
.menuSix span{position:absolute;transition:.3s cubic-bezier(.8,.5,.2,1.4);width:100%;height:4px;}
.menuSix span:nth-child(1){top:0;left:0;}
.menuSix span:nth-child(2){top:13px;left:0;}
.menuSix span:nth-child(3){bottom:0;left:0;}
.menuSix:not(.clickMenuSix):hover span:nth-child(1){transform:scaleY(1.2);left:-5px;}
.menuSix:not(.clickMenuSix):hover span:nth-child(2){transform:rotate(5deg) scaleY(1.1);}
.menuSix:not(.clickMenuSix):hover span:nth-child(3){transform:scaleY(1.2);left:5px;}
.clickMenuSix span:nth-child(1){transform:rotate(45deg) scaleX(.7);top:13px;left:-8px;}
.clickMenuSix span:nth-child(2){transform:scale(0);transition-duration:50ms;}
.clickMenuSix span:nth-child(3){transform:rotate(-45deg) scaleX(.7);top:13px;left:7px;}
.menuSeven{width:35px;height:30px;position:relative;}
.menuSeven span{position:absolute;transition:.3s cubic-bezier(.8,.5,.2,1.4);width:100%;height:4px;}
.menuSeven span:nth-child(1){top:0;left:0;}
.menuSeven span:nth-child(2){top:13px;left:0;}
.menuSeven span:nth-child(3){bottom:0;left:0;}
.menuSeven:not(.clickMenuSeven):hover span:nth-child(1){transform:scaleX(.8);}
.menuSeven:not(.clickMenuSeven):hover span:nth-child(2){transform:scaleX(.5);}
.menuSeven:not(.clickMenuSeven):hover span:nth-child(3){transform:scaleX(.8);}
.clickMenuSeven span:nth-child(1){transform:rotate(90deg);top:13px;}
.clickMenuSeven span:nth-child(2){transform:scale(0);transition-duration:50ms;}
.clickMenuSeven span:nth-child(3){top:13px;}
.menuEight{width:35px;height:30px;position:relative;}
.menuEight span{position:absolute;transition:.3s cubic-bezier(.8,.5,.2,1.4);width:100%;height:4px;}
.menuEight span:nth-child(1){top:0;left:0;}
.menuEight span:nth-child(2){top:13px;left:0;}
.menuEight span:nth-child(3){bottom:0;left:0;}
.menuEight:not(.clickMenuEight):hover span:nth-child(1){transform:scaleX(.8);}
.menuEight:not(.clickMenuEight):hover span:nth-child(2){transform:scaleX(.5);}
.menuEight:not(.clickMenuEight):hover span:nth-child(3){transform:scaleX(.8);}
.clickMenuEight span:nth-child(1),.clickMenuEight span:nth-child(3){top:13px;}
</code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">
$(document).ready(function(){
    const menuOne = document.querySelector('.menuOne');
    const menuTwo = document.querySelector('.menuTwo');
    const menuThree = document.querySelector('.menuThree');
    const menuFour = document.querySelector('.menuFour');
    const menuFive = document.querySelector('.menuFive');
    const menuSix = document.querySelector('.menuSix');
    const menuSeven = document.querySelector('.menuSeven');
    const menuEight = document.querySelector('.menuEight');
    function addClassFunOne() {
        this.classList.toggle("clickMenuOne");
    }
    function addClassFunTwo() {
        this.classList.toggle("clickMenuTow");
    }
    function addClassFunThree() {
        this.classList.toggle("clickMenuThree");
    }
    function addClassFunFour() {
        this.classList.toggle("clickMenuFour");
    }
    function addClassFunFive() {
        this.classList.toggle("clickMenuFive");
    }
    function addClassFunSix() {
        this.classList.toggle("clickMenuSix");
    }
    function addClassFunSeven(){
        this.classList.toggle("clickMenuSeven");
    }
    function addClassFunEight(){
        this.classList.toggle("clickMenuEight");
    }
    menuOne.addEventListener('click', addClassFunOne);
    menuTwo.addEventListener('click', addClassFunTwo);
    menuThree.addEventListener('click', addClassFunThree);
    menuFour.addEventListener('click', addClassFunFour);
    menuFive.addEventListener('click', addClassFunFive);
    menuSix.addEventListener('click', addClassFunSix);
    menuSeven.addEventListener('click', addClassFunSeven);
    menuEight.addEventListener('click', addClassFunEight);
});
            </code></pre>
        </div>
          </div>
        </div>
      </div> 
      </div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
      <script src="./js/iframe.js"></script>
  </body>
</html>
