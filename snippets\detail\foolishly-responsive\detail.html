
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
      <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
      <div class="col-sm-12 p-0">
        <div class="d-flex justify-content-between">
                <h4 class="size20 mb-2 pt-2">Responsive tabs</h4> 
                <div class="mb-2">
                  <a
                    href="./downloads/foolishly-responsive.zip"
                    class="btn btn-sm btn-outline-primary btn-dark mr-2"
                    ><i class="fas fa-download"></i> Download zip</a
                  >
                  <button class="btn btn-sm btn-outline-info" id="fullScreen">
                    <i class="fas fa-expand"></i> View on Fullscreen
                  </button>
                </div>             
          </div>   
    </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
          
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
            <!-- <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/multiselect/index.html"></iframe> -->
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/foolishly-responsive/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
 
  &lt;section role="region" aria-label="Assorted Ipsums"&gt;
  &lt;h2 class="collapsed"&gt;
    &lt;span&gt;
      &lt;button type="button" id="btnDisc01" aria-expanded="false" onclick="toggleDisclosure(this.id);" aria-controls="Disclosed01"&gt;
        &lt;span&gt;
          &lt;svg xmlns="\http://www.w3.org/2000/svg&quot;" viewBox="0 0 80 80" focusable="false"&gt;
            &lt;path d="M70.3 13.8L40 66.3 9.7 13.8z"&gt;&lt;/path&gt;
          &lt;/svg&gt;
        &lt;/span&gt;
        Lorem Ipsum
      &lt;/button&gt;
    &lt;/span&gt;
  &lt;/h2&gt;
  &lt;div id="Disclosed01" aria-label="Content" role="region" aria-labelledby="btnDisc01 Disclosed01" tabindex="0"&gt;
    
    &lt;p&gt;
      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sit amet molestie tellus. Proin ornare nisi id rhoncus ornare. In mattis vestibulum sollicitudin. Vestibulum quis nisi eget magna viverra dictum non in neque. Pellentesque vehicula ex tempor sapien laoreet, ut auctor nibh scelerisque. Aliquam dictum ultricies arcu, vitae varius sem viverra a. Aenean mattis, tellus id hendrerit sollicitudin, velit enim pulvinar lorem, nec sollicitudin mi urna a felis. Sed pretium erat risus, ullamcorper sagittis dui semper sed. Praesent ut lorem velit. Aenean tempus, sapien sit amet vehicula pretium, enim justo pretium leo, rhoncus mattis nunc nunc ut dui. Suspendisse fringilla urna sem, feugiat bibendum quam facilisis vitae. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Phasellus suscipit congue mauris, sit amet pellentesque nulla sollicitudin eu. Vestibulum lorem diam, bibendum eu mi non, condimentum elementum arcu. Proin iaculis, risus a hendrerit condimentum, velit mi volutpat dui, vel consectetur massa ex in sem.
    &lt;/p&gt;
    &lt;p&gt;
Sed ac mauris quis lectus efficitur ornare. Sed vitae lobortis dolor. Fusce in iaculis lectus. Morbi gravida lacus ut elementum feugiat. Integer vel posuere dui. Nulla eget pharetra purus. Vestibulum ultricies dui sit amet varius rhoncus. Nunc elementum ipsum id mauris volutpat commodo. Aliquam aliquet, tellus et sodales facilisis, neque urna iaculis nunc, eu maximus nibh tortor gravida neque. Aliquam nec dictum elit. Nunc elementum tortor lorem, eget malesuada lacus placerat eu.
    &lt;/p&gt;
    &lt;p&gt;
Proin hendrerit condimentum suscipit. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos. Proin nisi augue, pulvinar at neque ac, vulputate fringilla urna. Nullam sagittis egestas urna. Maecenas venenatis ante quis interdum iaculis. Vivamus egestas tortor non neque pulvinar lobortis. Suspendisse eget ex non orci consectetur blandit.
    &lt;/p&gt;
    &lt;p&gt;
Fusce nec ultrices dui. Donec a massa at augue consequat blandit. Mauris sagittis arcu quis aliquet venenatis. Aliquam egestas mi massa, sed rutrum risus elementum ut. Donec posuere nunc in volutpat posuere. Fusce non nisi sit amet mi feugiat elementum vitae sit amet risus. Interdum et malesuada fames ac ante ipsum primis in faucibus. Donec vehicula porttitor lorem sit amet scelerisque. Nullam fringilla euismod sem sit amet dictum. Mauris molestie feugiat cursus.
    &lt;/p&gt;
    &lt;p&gt;
Curabitur tincidunt lacus ex. Sed elementum imperdiet rutrum. Nunc metus diam, convallis id volutpat at, commodo vitae quam. Integer eget sapien at mauris lacinia eleifend. Nulla suscipit sagittis enim, vel luctus magna hendrerit eu. Nullam eu posuere elit. Suspendisse ultrices ac ipsum ege
    &lt;/p&gt;
  &lt;/div&gt;

  &lt;h2 class="collapsed"&gt;
    &lt;span&gt;
      &lt;button type="button" id="btnDisc02" aria-expanded="false" onclick="toggleDisclosure(this.id);" aria-controls="Disclosed02"&gt;
        &lt;span&gt;
          &lt;svg xmlns="\http://www.w3.org/2000/svg&quot;" viewBox="0 0 80 80" focusable="false"&gt;
            &lt;path d="M70.3 13.8L40 66.3 9.7 13.8z"&gt;&lt;/path&gt;
          &lt;/svg&gt;
        &lt;/span&gt;
        Cupcake Ipsum
      &lt;/button&gt;
    &lt;/span&gt;
  &lt;/h2&gt;
  &lt;div id="Disclosed02" aria-label="Content" role="region" aria-labelledby="btnDisc02 Disclosed02" tabindex="0"&gt;
    
    &lt;p&gt;
      Soufflé dessert toffee caramels pie chocolate bar. Dragée fruitcake cake dragée jelly beans. Carrot cake chocolate bar muffin liquorice brownie biscuit chocolate cake icing. Pastry bonbon gingerbread liquorice chupa chups. Danish croissant dragée muffin. Tiramisu bonbon pastry dessert brownie pie candy canes. Pie tiramisu jujubes lemon drops biscuit cotton candy. Chupa chups sweet roll sweet cookie ice cream gummi bears chocolate cake cotton candy carrot cake. Donut sesame snaps chocolate pastry pastry cupcake macaroon dessert. Donut macaroon sugar plum croissant chupa chups toffee. Cheesecake croissant cookie chocolate topping sweet. Pastry tart tootsie roll brownie marshmallow danish tart. Powder lemon drops cake toffee tiramisu lollipop cake fruitcake. Muffin cake sesame snaps biscuit jelly chocolate cake pie chocolate cake.
    &lt;/p&gt;
    &lt;p&gt;
Tiramisu biscuit candy canes sweet roll jelly danish cake. Jelly-o lemon drops jelly-o. Gummies ice cream gummies candy canes. Marzipan pastry dragée icing brownie candy canes. Croissant bear claw apple pie jelly beans jelly-o jelly beans. Cookie liquorice jelly beans dessert croissant. Caramels pie chupa chups. Muffin danish cupcake oat cake jujubes macaroon tart. Jelly-o wafer brownie toffee. Cookie marzipan gummi bears gummies liquorice chocolate chocolate bar. Lollipop jelly beans bonbon gingerbread. Jelly sesame snaps brownie sweet sweet roll jelly beans jelly carrot cake.
    &lt;/p&gt;
    &lt;p&gt;
Biscuit brownie halvah halvah cheesecake oat cake dragée. Danish bonbon wafer. Liquorice liquorice sugar plum sesame snaps caramels chupa chups tootsie roll cake. Carrot cake biscuit cupcake soufflé brownie carrot cake. Jelly beans fruitcake bonbon sesame snaps icing jelly beans gingerbread candy canes. Cake cake halvah topping gummi bears cookie pudding cupcake. Cotton candy cake chocolate liquorice chocolate cake powder. Chocolate cake candy canes chocolate bar. Dessert chupa chups jelly beans icing candy jelly beans pastry. Tart liquorice bonbon biscuit cheesecake apple pie muffin liquorice dessert. Carrot cake chocolate bar sweet gingerbread. Gummi bears sesame snaps candy toffee halvah. Muffin tiramisu liquorice icing fruitcake cupcake.
    &lt;/p&gt;
    &lt;p&gt;
Jelly-o halvah wafer chocolate bar. Cake bonbon sweet roll tart jujubes. Icing gingerbread biscuit chocolate bar fruitcake cupcake bonbon icing. Lemon drops oat cake caramels croissant dessert. Wafer cookie brownie cookie fruitcake liquorice. Icing cake gummi bears candy canes sweet. Topping biscuit marzipan. Oat cake tiramisu jelly-o topping. Marzipan carrot cake gummi bears jelly-o tart sugar plum lemon drops pudding. Sweet roll pie marshmallow muffin cotton candy croissant cake. Pudding marshmallow sweet. Wafer marshmallow sugar plum cake icing muffin chocolate cake chupa chups. Ice cream liquorice candy canes.
    &lt;/p&gt;
    &lt;p&gt;
Brownie sugar plum cake sesame snaps candy lollipop pudding halvah tiramisu. Pastry soufflé halvah bonbon muffin dessert marzipan. Tart candy canes wafer biscuit brownie jujubes bear claw. Bonbon halvah donut toffee pudding jujubes jelly-o sweet roll fruitcake. Candy jujubes dragée tart donut gummies chocolate bonbon. Pudding chupa chups marshmallow bear claw. Oat cake bonbon soufflé sweet roll gummi bears toffee tiramisu. Carrot cake tootsie roll icing caramels. Bear claw soufflé powder toffee sweet. Lollipop croissant icing biscuit fruitcake muffin. Carrot cake chocolate bar chupa chups sugar plum cheesecake candy cake. Gummies dragée toffee jujubes jelly gummies chocolate cake tootsie roll.
    &lt;/p&gt;
  &lt;/div&gt;

  &lt;h2 class="collapsed"&gt;
    &lt;span&gt;
      &lt;button type="button" id="btnDisc03" aria-expanded="false" onclick="toggleDisclosure(this.id);" aria-controls="Disclosed03"&gt;
        &lt;span&gt;
          &lt;svg xmlns="\http://www.w3.org/2000/svg&quot;" viewBox="0 0 80 80" focusable="false"&gt;
            &lt;path d="M70.3 13.8L40 66.3 9.7 13.8z"&gt;&lt;/path&gt;
          &lt;/svg&gt;
        &lt;/span&gt;
        Hipster Ipsum (Hipsum?)
      &lt;/button&gt;
    &lt;/span&gt;
  &lt;/h2&gt;
  &lt;div id="Disclosed03" aria-label="Content" role="region" aria-labelledby="btnDisc03 Disclosed03" tabindex="0"&gt;
    
    &lt;p&gt;
      I'm baby xOXO biodiesel vaporware kombucha. 3 wolf moon fingerstache deep v vaporware cardigan, distillery yr man braid chia prism meh kale chips lumbersexual kombucha. Man braid flexitarian portland swag salvia vexillologist knausgaard bitters polaroid williamsburg gochujang small batch lyft pinterest scenester. Intelligentsia hammock pabst listicle single-origin coffee portland sriracha enamel pin.
    &lt;/p&gt;
    &lt;p&gt;
Small batch kickstarter actually, raclette lomo meh chicharrones forage. Roof party trust fund jianbing lo-fi, slow-carb quinoa live-edge. Fingerstache man bun post-ironic viral, stumptown distillery live-edge woke. Williamsburg hot chicken disrupt authentic snackwave farm-to-table retro thundercats swag cardigan. Crucifix health goth master cleanse typewriter shoreditch pinterest dreamcatcher portland man bun ennui hot chicken, flexitarian mumblecore paleo swag. Brunch truffaut iPhone swag air plant shoreditch. Pour-over cray aesthetic, venmo ramps irony letterpress +1 schlitz tote bag knausgaard butcher lumbersexual heirloom.
    &lt;/p&gt;
    &lt;p&gt;
Lo-fi fixie farm-to-table 90's glossier waistcoat bushwick hot chicken kombucha cred distillery dreamcatcher yuccie cliche. PBR&B truffaut kombucha, viral celiac listicle cardigan ennui migas green juice master cleanse shaman tumeric. Man bun squid waistcoat af butcher, kombucha pickled freegan semiotics venmo vegan craft beer synth. Shaman intelligentsia vape, pabst mustache art party pickled tilde bushwick heirloom put a bird on it raw denim knausgaard swag whatever.
    &lt;/p&gt;
    &lt;p&gt;
Tacos franzen selvage subway tile, jianbing trust fund disrupt portland offal leggings pork belly. Typewriter vaporware XOXO raw denim. Green juice before they sold out 90's vinyl, mixtape ramps brooklyn sartorial forage gluten-free paleo glossier hot chicken art party. Yr readymade butcher, selvage locavore iPhone XOXO bushwick coloring book vegan tumeric pabst fixie retro. Blog viral everyday carry, knausgaard fam chambray roof party pork belly cloud bread iceland tumeric. Literally shabby chic man braid kogi typewriter +1 pok pok vape affogato food truck. Pour-over vice helvetica irony PBR&B fashion axe farm-to-table fanny pack artisan thundercats gastropub ethical try-hard.
    &lt;/p&gt;
    &lt;p&gt;
Yr shabby chic raclette live-edge tumeric mustache paleo cronut edison bulb typewriter whatever church-key man braid hoodie. Tbh meh artisan, 3 wolf moon pok pok offal blue bottle chia pork belly. Chicharrones thundercats ramps, hexagon cloud bread fixie 8-bit. Skateboard schlitz etsy, succulents slow-carb tofu migas mlkshk sartorial 3 wolf moon. Aesthetic sustainable ramps scenester cronut. Retro portland small batch vexillologist 3 wolf moon chambray occupy offal vinyl. Pok pok ramps meditation, helvetica plaid craft beer chia narwhal master cleanse actually knausgaard neutra skateboard.
    &lt;/p&gt;
  &lt;/div&gt;

  &lt;h2 class="collapsed"&gt;
    &lt;span&gt;
      &lt;button type="button" id="btnDisc04" aria-expanded="false" onclick="toggleDisclosure(this.id);" aria-controls="Disclosed04"&gt;
        &lt;span&gt;
          &lt;svg xmlns="\http://www.w3.org/2000/svg&quot;" viewBox="0 0 80 80" focusable="false"&gt;
            &lt;path d="M70.3 13.8L40 66.3 9.7 13.8z"&gt;&lt;/path&gt;
          &lt;/svg&gt;
        &lt;/span&gt;
        Zombpsum
      &lt;/button&gt;
    &lt;/span&gt;
  &lt;/h2&gt;
  &lt;div id="Disclosed04" aria-label="Content" role="region" aria-labelledby="btnDisc04 Disclosed04" tabindex="0"&gt;
    
    &lt;p&gt;
      Zombie ipsum reversus ab viral inferno, nam rick grimes malum cerebro. De carne lumbering animata corpora quaeritis. Summus brains sit​​, morbo vel maleficia? De apocalypsi gorger omero undead survivor dictum mauris. Hi mindless mortuis soulless creaturas, imo evil stalking monstra adventus resi dentevil vultus comedat cerebella viventium. Qui animated corpse, cricket bat max brucks terribilem incessu zomby. The voodoo sacerdos flesh eater, suscitat mortuos comedere carnem virus. Zonbi tattered for solum oculi eorum defunctis go lum cerebro. Nescio brains an Undead zombies. Sicut malus putrid voodoo horror. Nigh tofth eliv ingdead.
    &lt;/p&gt;
    &lt;p&gt;
Cum horribilem walking dead resurgere de crazed sepulcris creaturis, zombie sicut de grave feeding iride et serpens. Pestilentia, shaun ofthe dead scythe animated corpses ipsa screams. Pestilentia est plague haec decaying ambulabat mortuos. Sicut zeder apathetic malus voodoo. Aenean a dolor plan et terror soulless vulnerum contagium accedunt, mortui iam vivam unlife. Qui tardius moveri, brid eof reanimator sed in magna copia sint terribiles undeath legionis. Alii missing oculis aliorum sicut serpere crabs nostram. Putridi braindead odores kill and infect, aere implent left four dead.
    &lt;/p&gt;
    &lt;p&gt;
Lucio fulci tremor est dark vivos magna. Expansis creepy arm yof darkness ulnis witchcraft missing carnem armis Kirkman Moore and Adlard caeruleum in locis. Romero morbo Congress amarus in auras. Nihil horum sagittis tincidunt, zombie slack-jawed gelida survival portenta. The unleashed virus est, et iam zombie mortui ambulabunt super terram. Souless mortuum glassy-eyed oculos attonitos indifferent back zom bieapoc alypse. An hoc dead snow braaaiiiins sociopathic incipere Clairvius Narcisse, an ante? Is bello mundi z?
    &lt;/p&gt;
  &lt;/div&gt;
&lt;/section&gt;
</code>
</pre>
           
        </div>  
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">            
          body {
font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, Roboto,
  Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
line-height: 1.4;
}

h1 {
margin: 0 0 .5em 0;
}

/* All the CSS needed to make disclosures work */

/* :has() is supported nowhere yet */
/* h2:has(> span button[aria-expanded="false"]) + div {
display: none;
} */

/* until :has() is supported */
h2.collapsed + div {
display: none;
}

/* END All the CSS needed to make disclosures work */

/* h2 {
position: relative;
} */

@media (min-width: 40em) {
h2 {
  width: 2em;
  position: relative;
/*   outline: 1px dotted #0f0; */
  flex: 2.4em 0 0;
  height: calc(100vh - 4.5em);
  overflow: hidden;
}
h2 > span {
  transform: rotate(-90deg) translate(calc(-100vh + 7em), 0);
  transform-origin: 1.2em center;
  display: block;
  position: absolute;
  height: 2.4em;
  width: calc(100vh - 4.5em);
/*   outline: 1px dotted #f00; */
}
section[role=region] {
  display: flex;
}
h2 + div {
  height: calc(100vh - 7.5em);
}
}
@media (max-width: 39.999em) {
h2 + div {
  height: calc(70vh - 10em);
}
}

button[aria-expanded] {
border: .1em solid #ccc;
padding: .5em 1em .5em .5em;
font: inherit;
background-color: #eee;
}

button[aria-expanded] span {
display: inline-block;
font-size: 60%;
color: #000;
background-color: #00f;
padding: 0.3em 0.2em 0 0.2em;
border: 0.2em solid #00f;
border-radius: 50%;
line-height: 1;
text-align: center;
text-indent: 0;
transform: rotate(270deg);
}

button[aria-expanded] svg {
width: 1.25em;
height: 1.25em;
fill: #fff;
transition: transform 0.25s ease-in;
transform-origin: center 45%;
}

button[aria-expanded]:hover,
button[aria-expanded]:focus {
background-color: #666;
color: #fff;
outline: none;
border-color: #666;
}

button[aria-expanded]:hover span,
button[aria-expanded]:focus span {
background-color: #fff;
outline: none;
}

button[aria-expanded]:hover svg,
button[aria-expanded]:focus svg {
fill: #00f;
}

/* Lean on programmatic state for styling */
button[aria-expanded="true"] svg {
transform: rotate(90deg);
}

section[role=region] {
padding: 0;
border: .05em solid #ccc;
}

section[role=region] > div {
margin: 0;
}

section[role=region] h2 {
margin: 0;
}

section[role=region] button {
display: block;
width: 100%;
text-align: left;
background: #efefef;
border: .01em solid #ccc;
padding: .5em .75em;
}

h2 + div {
/*   height: calc(70vh - 7.5em); */
overflow: auto;
padding: .25em 1.25em;
border: .1em solid #ddd;
background-color: #fff;
margin-top: -.1em;
}

          </code></pre>
        </div> 
        <div id="tab04" class="tab-contents">
          <pre class="line-numbers">
            <code class="language-javascript">
              function closeAllDisclosures() {
                var openDs = document.querySelectorAll("button[aria-expanded]");
                for (var i = 0; i < openDs.length; i++) {
                  if (openDs[i].getAttribute("aria-expanded") == "true") {
                    openDs[i].setAttribute("aria-expanded", "false");
                    openDs[i].removeAttribute("aria-readonly");
                    openDs[i].parentNode.parentNode.classList.add("collapsed");
                  }
                }
              }
              
              function toggleDisclosure(btnID) {
                closeAllDisclosures();
                // Get the button that triggered this
                var theButton = document.getElementById(btnID);
                var btnParent = theButton.parentNode.parentNode;
                // If the button is not expanded...
                if (theButton.getAttribute("aria-expanded") == "false") {
                  // Now set the button to expanded
                  theButton.setAttribute("aria-expanded", "true");
                  theButton.setAttribute("aria-readonly", "true");
                  btnParent.classList.remove("collapsed");
                // Otherwise button is not expanded...
                } else {
                  // Now set the button to collapsed
                  theButton.setAttribute("aria-expanded", "false");
                  theButton.removeAttribute("aria-readonly");
                  btnParent.classList.add("collapsed");
                }
              }
              
              toggleDisclosure('btnDisc01');
            </code>
          </pre>
        </div>       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="../../../js/select2.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <script src="./js/iframe.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
