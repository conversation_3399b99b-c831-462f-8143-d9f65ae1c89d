#sudo: required
#dist: trusty

language: node_js
node_js:
# - "node"
 - "7"

script: karma start tests/karma.conf.js --single-run

#before_install:
# - export CHROME_BIN=/usr/bin/google-chrome
# - export DISPLAY=:99.0
# - sh -e /etc/init.d/xvfb start
# - sudo apt-get update
# - sudo apt-get install -y libappindicator1 fonts-liberation
# - wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
# - sudo dpkg -i google-chrome*.deb

before_install:
  - export CHROME_BIN=chromium-browser
  - export DISPLAY=:99.0
  - sh -e /etc/init.d/xvfb start
