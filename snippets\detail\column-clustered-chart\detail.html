<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <meta name="author" content="Vimal Thapliyal">

    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./css/uikit.css">
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>
        #tab-button {
            display: table;
            table-layout: fixed;
            width: 100%;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        #tab-button li {
            display: table-cell;
            width: 20%;
        }

        #tab-button li a {
            display: block;
            padding: .5em;
            background: #eee;
            border: 1px solid #ddd;
            text-align: center;
            color: #000;
            text-decoration: none;
        }

        #tab-button li:not(:first-child) a {
            border-left: none;
        }

        #tab-button li a:hover,
        #tab-button .is-active a {
            border-bottom-color: transparent;
            background: #fff;
        }

        .tab-contents {
            padding: .5em 2em 1em;
            border: 1px solid #ddd;
        }



        .tab-button-outer {
            display: none;
        }

        .tab-contents {
            margin-top: 20px;
        }

        @media screen and (min-width: 768px) {
            .tab-button-outer {
                position: relative;
                z-index: 2;
                display: block;
            }

            .tab-select-outer {
                display: none;
            }

            .tab-contents {
                position: relative;
                top: -1px;
                margin-top: 0;
            }
        }




        code[class*="language-"],
        pre[class*="language-"] {
            color: black;
            text-shadow: 0 1px white;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            direction: ltr;
            text-align: left;
            white-space: pre;
            word-spacing: normal;
            word-break: normal;
            line-height: 1.5;

            -moz-tab-size: 4;
            -o-tab-size: 4;
            tab-size: 4;

            -webkit-hyphens: none;
            -moz-hyphens: none;
            -ms-hyphens: none;
            hyphens: none;
        }

        pre[class*="language-"]::-moz-selection,
        pre[class*="language-"] ::-moz-selection,
        code[class*="language-"]::-moz-selection,
        code[class*="language-"] ::-moz-selection {
            text-shadow: none;
            background: #b3d4fc;
        }

        pre[class*="language-"]::selection,
        pre[class*="language-"] ::selection,
        code[class*="language-"]::selection,
        code[class*="language-"] ::selection {
            text-shadow: none;
            background: #b3d4fc;
        }

        @media print {

            code[class*="language-"],
            pre[class*="language-"] {
                text-shadow: none;
            }
        }

        /* Code blocks */
        pre[class*="language-"] {
            padding: 1em;
            margin: .5em 0;
            overflow: auto;
        }

        :not(pre)>code[class*="language-"],
        pre[class*="language-"] {
            background: #f5f2f0;
        }

        /* Inline code */
        :not(pre)>code[class*="language-"] {
            padding: .1em;
            border-radius: .3em;
        }

        .token.comment,
        .token.prolog,
        .token.doctype,
        .token.cdata {
            color: slategray;
        }

        .token.punctuation {
            color: #999;
        }

        .namespace {
            opacity: .7;
        }

        .token.property,
        .token.tag,
        .token.boolean,
        .token.number,
        .token.constant,
        .token.symbol {
            color: #905;
        }

        .token.selector,
        .token.attr-name,
        .token.string,
        .token.char,
        .token.builtin {
            color: #690;
        }

        .token.operator,
        .token.entity,
        .token.url,
        .language-css .token.string,
        .style .token.string,
        .token.variable {
            color: #a67f59;
            background: hsla(0, 0%, 100%, .5);
        }

        .token.atrule,
        .token.attr-value,
        .token.keyword {
            color: #07a;
        }

        .token.function {
            color: #DD4A68;
        }

        .token.regex,
        .token.important {
            color: #e90;
        }

        .token.important {
            font-weight: bold;
        }

        .token.entity {
            cursor: help;
        }

        pre.line-numbers {
            position: relative;
            padding-left: 3.8em;
            counter-reset: linenumber;
        }

        pre.line-numbers>code {
            position: relative;
        }

        .line-numbers .line-numbers-rows {
            position: absolute;
            pointer-events: none;
            top: 0;
            font-size: 100%;
            left: -3.8em;
            width: 3em;
            /* works for line-numbers below 1000 lines */
            letter-spacing: -1px;
            border-right: 1px solid #999;

            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;

        }

        .line-numbers-rows>span {
            pointer-events: none;
            display: block;
            counter-increment: linenumber;
        }

        .line-numbers-rows>span:before {
            content: counter(linenumber);
            color: #999;
            display: block;
            padding-right: 0.8em;
            text-align: right;
        }

        .close-menu-icon {
            position: relative;
            top: 10px;
        }

        .hamSection {
            top: 67px;
            padding-top: 0
        }

        .close-menu-icon {
            top: 5px
        }
    </style>
</head>

<body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
            <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu" style="display: none;" class="close-menu-icon" onclick="openNav()">
            <i class="fas fa-bars"></i>
        </span>
    </div>
    <main role="main">
        <div class="container-fluid mt-1">
            <div class="row">
                <script src="./menuNav.js"></script>
                <div id="rightSection" class="col-sm-10">
                    <div class="col-sm-12 p-0">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="size20 mb-2 pt-2">Column clustered Chart</h4>
                            </div>
                            <div class="mb-2">
                                <a href="./downloads/column-clustered-chart.zip"
                                    class="btn btn-sm btn-outline-primary btn-dark mr-2"><i class="fas fa-download"></i>
                                    Download zip</a>
                                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                                    <i class="fas fa-expand"></i> View on Fullscreen
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="tabs">
                        <div class="tab-button-outer">
                            <ul id="tab-button">
                                <li><a href="#tab01">Preview</a></li>
                                <li><a href="#tab02">HTML</a></li>
                                <li><a href="#tab03">CSS</a></li>
                                <li><a href="#tab04">Javascript</a></li>
                            </ul>
                        </div>
                        <div class="tab-select-outer">
                            <select id="tab-select">
                                <li><a href="#tab01">Preview</a></li>
                                <li><a href="#tab02">HTML</a></li>
                                <li><a href="#tab03">CSS</a></li>
                                <li><a href="#tab04">Javascript</a></li>
                            </select>
                        </div>

                        <div id="tab01" class="tab-contents">
                            <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%"
                                height="100%" src="./snippets/preview/column-clustered-chart/index.html"></iframe>
                        </div>
                        <div id="tab02" class="tab-contents">

                            <pre class="line-numbers">
<code class="language-markup">&lt;div id="chartdiv" style="width: 100%; height: 550px"&gt;&lt;/div&gt;</code>
</pre>

                        </div>
                        <div id="tab03" class="tab-contents">
                            <pre class="line-numbers"><code class="language-css">

                            </code></pre>
                        </div>
                        <div id="tab04" class="tab-contents">
                            <pre class="line-numbers"><code class="language-javascript">
                                
        var data = [
        {
            TimeFrame: "2018",
            "United Kingdom": "319.59",
            France: "299.62",
            Germany: "260.53",
            Netherlands: "123.06",
            "United States": "724.92",
            "Rest of the world": "2305.98",
            none: 0,
        },
        {
            TimeFrame: "2019",
            "United Kingdom": "341.26",
            France: "313.84",
            Germany: "269.03",
            Netherlands: "163.22",
            "United States": "761.51",
            "Rest of the world": "2473.81",
            none: 0,
        },
        {
            TimeFrame: "2020",
            "United Kingdom": "295.66",
            France: "324.99",
            Germany: "296.37",
            Netherlands: "182.79",
            "United States": "873.95",
            "Rest of the world": "3110.62",
            none: 0,
        },
        {
            TimeFrame: "2021",
            "United Kingdom": "389.21",
            France: "427.65",
            Germany: "342.11",
            Netherlands: "239.68",
            "United States": "1135.43",
            "Rest of the world": "3840.04",
            none: 0,
        },
        {
            TimeFrame: "2022",
            "United Kingdom": "448.04",
            France: "366.39",
            Germany: "327.92",
            Netherlands: "300.94",
            "United States": "1011.14",
            "Rest of the world": "1026.03",
            none: 0,
        },
    ];

    var root = am5.Root.new("chartdiv");

    root.setThemes([am5themes_Animated.new(root)]);

    var chart = root.container.children.push(
        am5xy.XYChart.new(root, {
            focusable: true,
            panX: true,
            panY: true,
            // wheelX: "panX",
            // wheelY: "zoomX",
            pinchZoomX: true,
            maxTooltipDistance: -1,
            layout: root.verticalLayout,
        })
    );
    chart.children.unshift(
        am5.Label.new(root, {
            text: "Global's cell culture media imports - by country",
            fontSize: 12,
            fontWeight: "bold",
            textAlign: "center",

            x: am5.percent(50),
            centerX: am5.percent(50),
        })
    );

    var cursor = chart.set("cursor", am5xy.XYCursor.new(root, {}));
    cursor.lineY.set("visible", false);

    var xRenderer = am5xy.AxisRendererX.new(root, {
        minGridDistance: 30,
        strokeOpacity: 1,
        strokeWidth: 1,
    });

    xRenderer.grid.template.setAll({
        location: 1,
    });
    xRenderer.grid.template.setAll({
        stroke: am5.color(0xfff),
        strokeWidth: 0,
        strokeOpacity: 0,
    });
    xRenderer.labels.template.setAll({
        fontSize: "12px",
    });
    var xAxis = chart.xAxes.push(
        am5xy.CategoryAxis.new(root, {
            maxDeviation: 0.3,
            categoryField: "TimeFrame",
            renderer: xRenderer,
            tooltip: am5.Tooltip.new(root, {}),
        })
    );
    xAxis.data.setAll(data);

    var yAxis = chart.yAxes.push(
        am5xy.ValueAxis.new(root, {
            calculateTotals: true,
            min: 0,
            extraMax: 0.1,

            renderer: am5xy.AxisRendererY.new(root, {
                strokeOpacity: 1,
                strokeWidth: 1,
            }),
        })
    );
    yAxis.children.unshift(
        am5.Label.new(root, {
            text: "USD million",
            textAlign: "center",
            y: am5.p50,
            rotation: -90,
            fontWeight: "bold",
            fontSize: 12,
        })
    );

    var yRenderer1 = yAxis.get("renderer");
    yRenderer1.grid.template.setAll({
        stroke: am5.color(0xfff),
        strokeWidth: 0,
        strokeOpacity: 0,
    });
    yRenderer1.labels.template.setAll({
        fontSize: "12px",
    });

    var legend = chart.children.push(
        am5.Legend.new(root, { x: am5.percent(50), centerX: am5.percent(50) })
    );
    function makeSeries(name, fieldName, color, showTotal) {
        var series = chart.series.push(
            am5xy.ColumnSeries.new(root, {
                stacked: true,
                name: name,
                xAxis: xAxis,
                yAxis: yAxis,
                valueYField: fieldName,
                categoryXField: "TimeFrame",
                fill: color !== "none" && am5.color(color),
                maskBullets: false,
            })
        );

        series.data.processor = am5.DataProcessor.new(root, {
            numericFields: [
                "United Kingdom",
                "France",
                "Germany",
                "Netherlands",
                "United States",
                "Rest of the world",
                "none",
            ],
        });

        var tooltip = am5.Tooltip.new(root, {
            labelText: "{name}, {categoryX}:{valueY}",
            tooltipY: 0,
        });
        tooltip.get("background").setAll({
            cornerRadius: 0,
            stroke: am5.color("#007365"),
        });
        series.set("tooltip", tooltip);

        if (showTotal) {
            series.bullets.push(function () {
                return am5.Bullet.new(root, {
                    locationY: 1,
                    sprite: am5.Label.new(root, {
                        text: "{valueYTotal}",
                        fill: am5.color(0x000000),
                        centerY: am5.p100,
                        centerX: am5.p50,
                        populateText: true,
                    }),
                });
            });
        }

        series.data.setAll(data);
        series.appear();
        if (!showTotal) {
            legend.data.push(series);
        }
    }

    makeSeries("United Kingdom", "United Kingdom", "#00b19c", false);
    makeSeries("France", "France", "#3bcd3f", false);
    makeSeries("Germany", "Germany", "#007365", false);
    makeSeries("Netherlands", "Netherlands", "#8dbac4", false);
    makeSeries("United States", "United States", "#002035", false);
    makeSeries("Rest of the world", "Rest of the world", "#33c1b0", false);
    makeSeries("none", "none", "none", true);

    legend.labels.template.setAll({
        fontSize: 12,
        // other settings...
    });
    chart.zoomOutButton.set("forceHidden", true);

                            </code></pre>
                        </div>

                    </div>
                </div>
            </div>
        </div>






    </main>

    <footer class="text-muted">
        <div class="container">
            <p class="float-right">
                <a href="#">Back to top</a>
            </p>


        </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <script src="./js/iframe.js"></script>

    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
        $(function () {
            var $tabButtonItem = $('#tab-button li'),
                $tabSelect = $('#tab-select'),
                $tabContents = $('.tab-contents'),
                activeClass = 'is-active';

            $tabButtonItem.first().addClass(activeClass);
            $tabContents.not(':first').hide();

            $tabButtonItem.find('a').on('click', function (e) {
                var target = $(this).attr('href');

                $tabButtonItem.removeClass(activeClass);
                $(this).parent().addClass(activeClass);
                $tabSelect.val(target);
                $tabContents.hide();
                $(target).show();
                e.preventDefault();
            });

            $tabSelect.on('change', function () {
                var target = $(this).val(),
                    targetSelectNum = $(this).prop('selectedIndex');

                $tabButtonItem.removeClass(activeClass);
                $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
                $tabContents.hide();
                $(target).show();
            });
        });
    </script>
</body>

</html>