  <!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Component Library</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        
          <div class="row">
            <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Context Menu</h4>
              <div class="mb-2">
                <a
                  href="./downloads/context-menu.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/context-menu/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
              <pre class="line-numbers">
<code class="language-markup">&lt;ul class="menu"&gt;
&lt;li class="menu-item"&gt;
    &lt;a href="#" class="menu-btn"&gt;
    &lt;i class="fa fa-folder-open"&gt;&lt;/i&gt;
    &lt;span class="menu-text"&gt;Open&lt;/span&gt;
    &lt;/a&gt;
&lt;/li&gt;
&lt;li class="menu-item menu-item-disabled"&gt;
    &lt;button type="button" class="menu-btn"&gt;
    &lt;span class="menu-text"&gt;Open in New Window&lt;/span&gt;
    &lt;/button&gt;
&lt;/li&gt;
&lt;li class="menu-separator"&gt;&lt;/li&gt;
&lt;li class="menu-item"&gt;
    &lt;button type="button" class="menu-btn"&gt;
    &lt;i class="fa fa-reply"&gt;&lt;/i&gt;
    &lt;span class="menu-text"&gt;Reply&lt;/span&gt;
    &lt;/button&gt;
&lt;/li&gt;
&lt;li class="menu-item"&gt;
    &lt;button type="button" class="menu-btn"&gt;
    &lt;i class="fa fa-star"&gt;&lt;/i&gt;
    &lt;span class="menu-text"&gt;Favorite&lt;/span&gt;
    &lt;/button&gt;
&lt;/li&gt;
&lt;li class="menu-item menu-item-submenu"&gt;
    &lt;button type="button" class="menu-btn"&gt;
    &lt;i class="fa fa-users"&gt;&lt;/i&gt;
    &lt;span class="menu-text"&gt;Social&lt;/span&gt;
    &lt;/button&gt;
    &lt;ul class="menu"&gt;
    &lt;li class="menu-item"&gt;
        &lt;button type="button" class="menu-btn"&gt;
        &lt;i class="fa fa-comment"&gt;&lt;/i&gt;
        &lt;span class="menu-text"&gt;Comment&lt;/span&gt;
        &lt;/button&gt;
    &lt;/li&gt;
    &lt;li class="menu-item menu-item-submenu"&gt;
        &lt;button type="button" class="menu-btn"&gt;
        &lt;i class="fa fa-share"&gt;&lt;/i&gt;
        &lt;span class="menu-text"&gt;Share&lt;/span&gt;
        &lt;/button&gt;
        &lt;ul class="menu"&gt;
        &lt;li class="menu-item"&gt;
            &lt;button type="button" class="menu-btn"&gt;
            &lt;i class="fa fa-twitter"&gt;&lt;/i&gt;
            &lt;span class="menu-text"&gt;Twitter&lt;/span&gt;
            &lt;/button&gt;
        &lt;/li&gt;
        &lt;li class="menu-item"&gt;
            &lt;button type="button" class="menu-btn"&gt;
            &lt;i class="fa fa-facebook-official"&gt;&lt;/i&gt;
            &lt;span class="menu-text"&gt;Facebook&lt;/span&gt;
            &lt;/button&gt;
        &lt;/li&gt;
        &lt;li class="menu-item"&gt;
            &lt;button type="button" class="menu-btn"&gt;
            &lt;i class="fa fa-google-plus"&gt;&lt;/i&gt;
            &lt;span class="menu-text"&gt;Google Plus&lt;/span&gt;
            &lt;/button&gt;
        &lt;/li&gt;
        &lt;li class="menu-item"&gt;
            &lt;button type="button" class="menu-btn"&gt;
            &lt;i class="fa fa-envelope"&gt;&lt;/i&gt;
            &lt;span class="menu-text"&gt;Email&lt;/span&gt;
            &lt;/button&gt;
        &lt;/li&gt;
        &lt;/ul&gt;
    &lt;/li&gt;
    &lt;/ul&gt;
&lt;/li&gt;
&lt;li class="menu-separator"&gt;&lt;/li&gt;
&lt;li class="menu-item"&gt;
    &lt;button type="button" class="menu-btn"&gt;
    &lt;i class="fa fa-download"&gt;&lt;/i&gt;
    &lt;span class="menu-text"&gt;Save&lt;/span&gt;
    &lt;/button&gt;
&lt;/li&gt;
&lt;li class="menu-item"&gt;
    &lt;button type="button" class="menu-btn"&gt;
    &lt;i class="fa fa-edit"&gt;&lt;/i&gt;
    &lt;span class="menu-text"&gt;Rename&lt;/span&gt;
    &lt;/button&gt;
&lt;/li&gt;
&lt;li class="menu-item"&gt;
    &lt;button type="button" class="menu-btn"&gt;
    &lt;i class="fa fa-trash"&gt;&lt;/i&gt;
    &lt;span class="menu-text"&gt;Delete&lt;/span&gt;
    &lt;/button&gt;
&lt;/li&gt;
&lt;/ul&gt;

&lt;div class="container-wrapper"&gt;
&lt;h1&gt;Context Menu&lt;/h1&gt;
&lt;h2&gt;(right-click anywhere)&lt;/h2&gt;
&lt;/div&gt;
</code>
</pre>
            </div>
            <div id="tab03" class="tab-contents">
<pre class="line-numbers"><code class="language-css">html {
width: 100%;
height: 100%;
background: radial-gradient(circle, #112636 0%, #112636 100%)
    no-repeat;
color: #fff;
}

.container-wrapper {
position: absolute;
top: 50%;
left: 50%;
transform: translate(-50%, -50%);
width: 100%;
margin: auto;
text-align: center;
}

h1,
h2 {
color: #fff;
}

/* Menu */

.menu {
position: absolute;
width: 200px;
padding: 2px;
margin: 0;
border: 1px solid #bbb;
background: #eee;
background: linear-gradient(
    to bottom,
    #fff 0%,
    #fff 100px,
    #fff 100%
);
z-index: 100;
border-radius: 3px;
box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.2);
opacity: 0;
transform: translate(0, 15px) scale(0.95);
transition: transform 0.1s ease-out, opacity 0.1s ease-out;
pointer-events: none;
}

.menu-item {
display: block;
position: relative;
margin: 0;
padding: 0;
white-space: nowrap;
}

.menu-btn {
display: block;
color: #444;
font-family: "Roboto", sans-serif;
font-size: 13px;
cursor: pointer;
border: 1px solid transparent;
white-space: nowrap;
padding: 6px 8px;
border-radius: 3px;
}

button.menu-btn {
background: none;
line-height: normal;
overflow: visible;
-webkit-user-select: none;
-moz-user-select: none;
-ms-user-select: none;
width: 100%;
text-align: left;
}

a.menu-btn {
outline: 0 none;
text-decoration: none;
}

.menu-text {
margin-left: 25px;
}

.menu-btn .fa {
position: absolute;
left: 8px;
top: 50%;
transform: translateY(-50%);
}

.menu-item:hover > .menu-btn {
color: #fff;
outline: none;
background-color: #2e3940;
background: linear-gradient(to bottom, #5d6d79, #2e3940);
border: 1px solid #2e3940;
}

.menu-item-disabled {
opacity: 0.5;
pointer-events: none;
}

.menu-item-disabled .menu-btn {
cursor: default;
}

.menu-separator {
display: block;
margin: 7px 5px;
height: 1px;
border-bottom: 1px solid #fff;
background-color: #aaa;
}

.menu-item-submenu::after {
content: "";
position: absolute;
right: 6px;
top: 50%;
transform: translateY(-50%);
border: 5px solid transparent;
border-left-color: #808080;
}

.menu-item-submenu:hover::after {
border-left-color: #fff;
}

.menu .menu {
top: 4px;
left: 99%;
}

.menu-show,
.menu-item:hover > .menu {
opacity: 1;
transform: translate(0, 0) scale(1);
pointer-events: auto;
}

.menu-item:hover > .menu {
transition-delay: 300ms;
}
</code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">var menu = document.querySelector(".menu");

function showMenu(x, y) {
    menu.style.left = x + "px";
    menu.style.top = y + "px";
    menu.classList.add("menu-show");
}

function hideMenu() {
    menu.classList.remove("menu-show");
}

function onContextMenu(e) {
    e.preventDefault();
    showMenu(e.pageX, e.pageY);
    document.addEventListener("mousedown", onMouseDown, false);
}

function onMouseDown(e) {
    hideMenu();
    document.removeEventListener("mousedown", onMouseDown);
}

document.addEventListener("contextmenu", onContextMenu, false);

            </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
      <script src="./js/iframe.js"></script>
  </body>
</html>
