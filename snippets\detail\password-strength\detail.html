
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Password strength</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Password strength ui</h4>
              <div class="mb-2">
                <a
                  href="./downloads/password-strength.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <!-- <li><a href="#tab03">CSS</a></li> -->
                <!-- <li><a href="#tab04">Javascript</a></li> -->
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/password-strength/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
<pre class="line-numbers">
<code class="language-markup">
    &lt;div class="form-container"&gt;
    &lt;form class="form-1" action=""&gt;
      &lt;label&gt;Enter password
        &lt;input class="input-1" type="password" placeholder="&#8226;&#8226;&#8226;&#8226;&#8226;&#8226;"&gt;
        &lt;/label&gt;
      &lt;div class="progress-bar_wrap"&gt;
        &lt;div class="progress-bar_item progress-bar_item-1"&gt;&lt;/div&gt;
        &lt;div class="progress-bar_item progress-bar_item-2"&gt;&lt;/div&gt;
        &lt;div class="progress-bar_item progress-bar_item-3"&gt;&lt;/div&gt;
      &lt;/div&gt;
      &lt;span class="progress-bar_text"&gt;Password is blank&lt;/span&gt;
    &lt;/form&gt;
  &lt;/div&gt;
  
  &lt;div class="form-container"&gt;
    &lt;form class="form-2" action=""&gt;
      &lt;label&gt;Enter password
        &lt;input class="input-2" type="password" placeholder="&#8226;&#8226;&#8226;&#8226;&#8226;&#8226;"&gt;
        &lt;/label&gt;
      &lt;div class="progress-bar_wrap"&gt;
        &lt;div class="progress-bar_item progress-bar_item-1"&gt;&lt;/div&gt;
        &lt;div class="progress-bar_item progress-bar_item-2"&gt;&lt;/div&gt;
        &lt;div class="progress-bar_item progress-bar_item-3"&gt;&lt;/div&gt;
      &lt;/div&gt;
      &lt;span class="progress-bar_text"&gt;Password is blank&lt;/span&gt;
    &lt;/form&gt;
  &lt;/div&gt;
  
  &lt;div class="form-container"&gt;
    &lt;form class="form-3" action=""&gt;
      &lt;label&gt;Enter password
        &lt;input class="input-3" type="password" placeholder="&#8226;&#8226;&#8226;&#8226;&#8226;&#8226;"&gt;
        &lt;/label&gt;
      &lt;span class="progress-bar_text"&gt;Password is blank&lt;/span&gt;
    &lt;/form&gt;
  &lt;/div&gt;
  
  &lt;div class="form-container"&gt;
    &lt;form class="form-4" action=""&gt;
      &lt;label&gt;Enter password
        &lt;input class="input-4" type="password" placeholder="&#8226;&#8226;&#8226;&#8226;&#8226;&#8226;"&gt;
        &lt;/label&gt;
      &lt;span class="progress-bar_text"&gt;Password is blank&lt;/span&gt;
    &lt;/form&gt;
  &lt;/div&gt;
</code>  
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">
body {
margin: 0;
padding: 0;
width: 100%;
height: 100%;
text-align: center;
}

.form-container {
display: block;
width: 500px;
margin: 60px auto;
text-align: left;
}

label {
display: block;
position: relative;
text-transform: uppercase;
color: #aaa;
}

input[type='password'] {
width: 100%;
box-sizing: border-box;
height: 55px;
display: inline-block;
border: 3px solid #2F96EF;
border-radius: 5px;
padding: 0 15px;
margin: 10px 0;
transition: .2s;
}

input[type='password']:focus {
outline: none;
    -moz-outline: none;
    -webkit-outline: none;
}

label:before {
content: "\f070";
color: #aaa;
font-size: 22px;
font-family: FontAwesome;
position: absolute;
right: 25px;
top: 44px;
}

.progress-bar_wrap {
width: 300px;
height: 5px;
background: #F6F6FA;
display: inline-block;
vertical-align: middle;
overflow: hidden;
border-radius: 5px;
}



.form-1 .progress-bar_item {
display: inline-block;
height: 100%;
width: 33.33%;
float: left;
visibility: hidden;
transition: background-color .2s, visisility .1s;
}

.form-1 .active {
visibility: visible;
}

.progress-bar_text {
display: inline-block;
color: #aaa;
margin-left: 5px;
transition: .2s;
}



.form-2 .progress-bar_item {
display: inline-block;
height: 100%;
width: 32.5%;
margin-right: .8%;
border-radius: 5px;
float: left;

transition: background-color .2s, visisility .1s;
}

.form-2 .progress-bar_item-1.active {
background-color: #FF4B47;
}

.form-2 .progress-bar_item-2.active {
background-color: #F9AE35;
}

.form-2 .progress-bar_item-3.active {
background-color: #2DAF7D;
}

              </code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">

$( document ).ready(function() {

    const changeText = function (el, text, color) {
        el.text(text).css('color', color);
    };
    
    $('.input-1').keyup(function(){
        let len = this.value.length;
        const pbText = $('.form-1 .progress-bar_text');
    
        if (len === 0) {
        $('.form-1 .progress-bar_item').each(function() {
            $(this).removeClass('active')
        });
        $('.form-1 .active').css('background-color', 'transparent');
        changeText(pbText, 'Password is blank');
        } else if (len > 0 && len <= 4) {
        $('.form-1 .progress-bar_item-1').addClass('active');
        $('.form-1 .progress-bar_item-2').removeClass('active');
        $('.form-1 .progress-bar_item-3').removeClass('active');
        $('.form-1 .active').css('background-color', '#FF4B47');
        changeText(pbText, 'Too weak');
        } else if (len > 4 && len <= 8) {
        $('.form-1 .progress-bar_item-2').addClass('active');
        $('.form-1 .progress-bar_item-3').removeClass('active');
        $('.form-1 .active').css('background-color', '#F9AE35');
        changeText(pbText, 'Could be stronger');
        } else {
        $('.form-1 .progress-bar_item').each(function() {
            $(this).addClass('active');
        });
        $('.form-1 .active').css('background-color', '#2DAF7D');
        changeText(pbText, 'Strong password');
        } 
    });
    
    $('.input-2').keyup(function(){
        let len = this.value.length;
        const pbText = $('.form-2 .progress-bar_text');
    
        if (len === 0) {
        $('.form-2 .progress-bar_item').each(function() {
            $(this).removeClass('active')
        });
        changeText(pbText, 'Password is blank');
        } else if (len > 0 && len <= 4) {
        $('.form-2 .progress-bar_item-1').addClass('active');
        $('.form-2 .progress-bar_item-2').removeClass('active');
        $('.form-2 .progress-bar_item-3').removeClass('active');
        changeText(pbText, 'Too weak');
        } else if (len > 4 && len <= 8) {
        $('.form-2 .progress-bar_item-2').addClass('active');
        $('.form-2 .progress-bar_item-3').removeClass('active');
        changeText(pbText, 'Could be stronger');
        } else {
        $('.form-2 .progress-bar_item').each(function() {
            $(this).addClass('active');
        });
        changeText(pbText, 'Strong password');
        } 
    });
    
    $('.input-3').keyup(function(){
        let len = this.value.length;
        const pbText = $('.form-3 .progress-bar_text');
    
        if (len === 0) {
        $(this).css('border-bottom-color', '#2F96EF');
        changeText(pbText, 'Password is blank', '#aaa');
        } else if (len > 0 && len <= 4) {
        $(this).css('border-bottom-color', '#FF4B47');
        changeText(pbText, 'Too weak', '#FF4B47');
        } else if (len > 4 && len <= 8) {
        $(this).css('border-bottom-color', '#F9AE35');
        changeText(pbText, 'Could be stronger', '#aaa');
        } else {
        $(this).css('border-bottom-color', '#2DAF7D');
        changeText(pbText, 'Strong password');
        } 
    });
    
    $('.input-4').keyup(function(){
        let len = this.value.length;
        const pbText = $('.form-4 .progress-bar_text');
    
        if (len === 0) {
        $(this).css('border-color', '#2F96EF');
        changeText(pbText, 'Password is blank', '#aaa');
        } else if (len > 0 && len <= 4) {
        $(this).css('border-color', '#FF4B47');
        changeText(pbText, 'Too weak', '#FF4B47');
        } else if (len > 4 && len <= 8) {
        $(this).css('border-color', '#F9AE35');
        changeText(pbText, 'Could be stronger', '#F9AE35');
        } else {
        $(this).css('border-color', '#2DAF7D');
        changeText(pbText, 'Strong password', '#2DAF7D');
        } 
    });
    
    });
                    
                  
                  
                  
                   
            </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
