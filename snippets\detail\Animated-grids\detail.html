
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
      <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
      <div class="col-sm-12 p-0">
        <div class="d-flex justify-content-between">
                <h4 class="size20 mb-2 pt-2">Animated grid layout</h4>
                <div class="mb-2">
                  <a
                    href="./downloads/Animated-grids.zip"
                    class="btn btn-sm btn-outline-primary btn-dark mr-2"
                    ><i class="fas fa-download"></i> Download zip</a
                  >
                  <button class="btn btn-sm btn-outline-info" id="fullScreen">
                    <i class="fas fa-expand"></i> View on Fullscreen
                  </button>
                </div>                     
          </div>   
    </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
           
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
          
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
            <!-- <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/multiselect/index.html"></iframe> -->
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/Animated-grids/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
 
                    &lt;h4 class="mt-3 heading"&gt;Animated grid layout&lt;/h4&gt;

                    &lt;div class="wrapper preload"&gt;
                    &lt;section class="grid-unit top-left"&gt;
                      &lt;div class="swing-panel"&gt;
                        &lt;span class="desc"&gt;Lorem ipsum dolor sit amet consectetur, adipisicing elit. Vel, accusantium! &lt;/span&gt;
                      &lt;/div&gt;
                      &lt;div class="sphere"&gt; &lt;/div&gt;
                      &lt;span class="entypo-book-open icon"&gt; &lt;/span&gt;
                      &lt;span class="label"&gt;Documents &lt;/span&gt;
                    &lt;/section&gt;
                    &lt;section class="grid-unit top-right"&gt;
                      &lt;div class="swing-panel"&gt;
                        &lt;span class="desc"&gt;Lorem ipsum dolor sit amet consectetur, adipisicing elit. Vel, accusantium! &lt;/span&gt;
                      &lt;/div&gt;
                          &lt;div class="sphere"&gt; &lt;/div&gt;
                      &lt;span class="entypo-vcard icon"&gt; &lt;/span&gt;
                      &lt;span class="label"&gt;About Me &lt;/span&gt;
                    &lt;/section&gt;
                    &lt;section class="grid-unit bottom-left"&gt;
                      &lt;div class="swing-panel"&gt;
                        &lt;span class="desc"&gt;Lorem ipsum dolor sit amet consectetur, adipisicing elit. Vel, accusantium! &lt;/span&gt;
                      &lt;/div&gt;
                          &lt;div class="sphere"&gt; &lt;/div&gt;
                      &lt;span class="entypo-tools icon"&gt; &lt;/span&gt;
                      &lt;span class="label"&gt;Settings &lt;/span&gt;
                    &lt;/section&gt;
                    &lt;section class="grid-unit bottom-right"&gt;
                      &lt;div class="swing-panel"&gt;
                        &lt;span class="desc"&gt;Lorem ipsum dolor sit amet consectetur, adipisicing elit. Vel, accusantium! &lt;/span&gt;
                      &lt;/div&gt;
                          &lt;div class="sphere"&gt; &lt;/div&gt;
                      &lt;span class="entypo-picture icon"&gt; &lt;/span&gt;
                      &lt;span class="label"&gt;Gallery &lt;/span&gt;
                    &lt;/section&gt;
                  &lt;/div&gt;

</code>
</pre>
           
        </div>  
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
           

            @import url(https://fonts.googleapis.com/css?family=Oxygen);
@import url(http://weloveiconfonts.com/api/?family=entypo);

[class*="entypo-"]:before {
  font-family: 'entypo', sans-serif;
}

* {
  box-sizing: border-box;
}

.preload * {
  transition: none !important;
}

html
{
  position: relative;
  min-height: 100%;
}

body {
  background: #fff;
}

body,
.wrapper{
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}

.grid-unit {
  position: relative;
  float: left;
  border-top: 10px solid #fff;
  border-left: 10px solid #fff;
  width: 50%;
  height: 300px;
  perspective: 800;
  overflow: hidden;
}

.top-left {
  background: rgba(236,208,120,1);
}

.top-right {
  background: rgba(217,91,67,1);
  border-right: 10px solid #fff;
}

.bottom-right {
  background: rgba(192,41,66,1);
  border-bottom: 10px solid #fff;
  border-right: 10px solid #fff;
}

.bottom-left {
  background: rgba(84,36,55,1);
  border-bottom: 10px solid #fff;
}

.swing-panel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%; 
  font-size: 2.5em;
  color: #fff;
  font-family: Oxygen, arial, sans-serif;
  transform-origin: left 50%;
  transform: rotateY(120deg);
  transition: transform .5s ease;
  letter-spacing: -.05em;
  text-shadow: 0 1px 0 rgba(0,0,0,.2);
}

.top-right .swing-panel,
.bottom-right .swing-panel{
  transform-origin: right 50%;
  transform: rotateY(-120deg);
}

.desc {
  display: block;
  position: absolute;
   padding: 5%;
}

.label {
  position: absolute;
  font-family: Oxygen, arial, sans-serif;
  color: #fff;
  font-size: 1.2em;
  opacity: 1;
  transition: opacity .5s ease;
}

.grid-unit:hover .label {
  opacity: 0;
}

.top-left .label {
  bottom: 100px;
  right: 80px;
  transform: rotate(45deg);
}

.top-right .label {
  bottom: 100px;
  left: 80px;
  transform: rotate(-45deg);
}

.bottom-left .label {
  top: 100px;
  right: 80px;
  transform: rotate(-45deg);
}

.bottom-right .label {
  top: 100px;
  left: 80px;
  transform: rotate(45deg);
}

.top-left .desc {
  top: 0;
  left: 0;
}

.top-right .desc {
  top: 0;
  right: 0;
}

.bottom-left .desc {
  bottom: 0;
  left: 0;
}

.bottom-right .desc {
  bottom: 0;
  right: 0;
}

.grid-unit:hover .swing-panel {
  transform: rotateY(0deg);
}

.sphere {
  position: absolute;
  width: 200px;
  height: 200px;
  background-color: rgba(255,255,255,1);
  border-radius: 500px;
  transition: background-color .25s ease;
  opacity: 1;
}

.top-left .sphere {
  right: -100px;
  bottom: -100px;
}

.top-right .sphere {
  left: -100px;
  bottom: -100px;
}

.bottom-right .sphere {
  left: -100px;
  top: -100px;
}

.bottom-left .sphere {
  right: -100px;
  top: -100px;
}

.grid-unit:hover .sphere {
  background-color: rgba(255,255,255,0);
}

.icon {
  position: absolute;
  font-size: 2em;
  transition: all .25s ease;
  z-index: 5;
}

.bottom-left .icon {
  top: 20px;
  right: 30px;
  color: rgba(84,36,55,1);
}

.bottom-right .icon {
  top: 20px;
  left: 22px;
  color: rgba(192,41,66,1);
}

.top-right .icon {
  bottom: 25px;
  left: 20px;
  color: rgba(217,91,67,1);
}

.top-left .icon {
  bottom: 25px;
  right: 35px;
  color: rgba(236,208,120,1);
}

.grid-unit:hover .icon {
  color: #fff;
  font-size: 4em;
}
          </code></pre>
        </div> 
        <div id="tab04" class="tab-contents">
          <pre class="line-numbers">
            <code class="language-javascript">
              jQuery(function(){

                $(window).load(function(){
                
                $('.wrapper').removeClass('preload');
                
                });
                
                });
            </code>
          </pre>
        </div>      
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="../../../js/select2.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <script src="./js/iframe.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
