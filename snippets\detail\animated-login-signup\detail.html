
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Animated login and sign up</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Animated login and sign up</h4>
              <div class="mb-2">
                <a
                  href="./downloads/animated-login-signup.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <!-- <li><a href="#tab03">CSS</a></li> -->
                <!-- <li><a href="#tab04">Javascript</a></li> -->
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/animated-login-signup/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
<pre class="line-numbers">
<code class="language-markup">
&lt;section class="user"&gt;
&lt;div class="user_options-container"&gt;
    &lt;div class="user_options-text"&gt;
    &lt;div class="user_options-unregistered"&gt;
        &lt;h2 class="user_unregistered-title"&gt;Don't have an account?&lt;/h2&gt;
        &lt;p class="user_unregistered-text"&gt;
        Lorem ipsum dolor sit amet consectetur adipisicing elit.
        Laboriosam, rem.
        &lt;/p&gt;
        &lt;button class="user_unregistered-signup" id="signup-button"&gt;
        Sign up
        &lt;/button&gt;
    &lt;/div&gt;

    &lt;div class="user_options-registered"&gt;
        &lt;h2 class="user_registered-title"&gt;Have an account?&lt;/h2&gt;
        &lt;p class="user_registered-text"&gt;
        Lorem ipsum dolor sit amet consectetur adipisicing elit.
        Laboriosam, rem.
        &lt;/p&gt;
        &lt;button class="user_registered-login" id="login-button"&gt;
        Login
        &lt;/button&gt;
    &lt;/div&gt;
    &lt;/div&gt;

    &lt;div class="form-options" id="form-options"&gt;
    &lt;div class="user_forms-login"&gt;
        &lt;h2 class="forms_title"&gt;Login&lt;/h2&gt;
        &lt;form class="forms_form"&gt;
        &lt;fieldset class="forms_fieldset"&gt;
            &lt;div class="forms_field"&gt;
            &lt;input
                type="email"
                placeholder="Email"
                class="forms_field-input"
                required
                autofocus
            /&gt;
            &lt;/div&gt;
            &lt;div class="forms_field"&gt;
            &lt;input
                type="password"
                placeholder="Password"
                class="forms_field-input"
                required
            /&gt;
            &lt;/div&gt;
        &lt;/fieldset&gt;
        &lt;div class="forms_buttons"&gt;
            &lt;button type="button" class="forms_buttons-forgot"&gt;
            Forgot password?
            &lt;/button&gt;
            &lt;input
            type="submit"
            value="Log In"
            class="forms_buttons-action"
            /&gt;
        &lt;/div&gt;
        &lt;/form&gt;
    &lt;/div&gt;
    &lt;div class="user_forms-signup"&gt;
        &lt;h2 class="forms_title"&gt;Sign Up&lt;/h2&gt;
        &lt;form class="forms_form"&gt;
        &lt;fieldset class="forms_fieldset"&gt;
            &lt;div class="forms_field"&gt;
            &lt;input
                type="text"
                placeholder="Full Name"
                class="forms_field-input"
                required
            /&gt;
            &lt;/div&gt;
            &lt;div class="forms_field"&gt;
            &lt;input
                type="email"
                placeholder="Email"
                class="forms_field-input"
                required
            /&gt;
            &lt;/div&gt;
            &lt;div class="forms_field"&gt;
            &lt;input
                type="password"
                placeholder="Password"
                class="forms_field-input"
                required
            /&gt;
            &lt;/div&gt;
        &lt;/fieldset&gt;
        &lt;div class="forms_buttons"&gt;
            &lt;input
            type="submit"
            value="Sign up"
            class="forms_buttons-action"
            /&gt;
        &lt;/div&gt;
        &lt;/form&gt;
    &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
&lt;/section&gt;
</code>  
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">
/**
* * General variables
* */
    /**
* * General configs
* */
    * {
    box-sizing: border-box;
    margin: 0;
    }

    body {
    font-family: "Fira Sans", sans-serif;
    font-size: 12px;
    line-height: 1em;
    }

    button {
    background-color: transparent;
    padding: 0;
    border: 0;
    outline: 0;
    cursor: pointer;
    }

    input {
    background-color: transparent;
    padding: 0;
    border: 0;
    outline: 0;
    }
    input[type="submit"] {
    cursor: pointer;
    }
    input::-moz-placeholder {
    font-size: 0.85rem;
    font-family: "Fira Sans", sans-serif;
    font-weight: 300;
    letter-spacing: 0.1rem;
    color: #ccc;
    }
    input:-ms-input-placeholder {
    font-size: 0.85rem;
    font-family: "Fira Sans", sans-serif;
    font-weight: 300;
    letter-spacing: 0.1rem;
    color: #ccc;
    }
    input::placeholder {
    font-size: 0.85rem;
    font-family: "Fira Sans", sans-serif;
    font-weight: 300;
    letter-spacing: 0.1rem;
    color: #ccc;
    }

    /**
* * Bounce to the left side
* */
    @-webkit-keyframes bounce-left {
    0% {
        transform: translate3d(100%, -50%, 0);
    }
    50% {
        transform: translate3d(-30px, -50%, 0);
    }
    100% {
        transform: translate3d(0, -50%, 0);
    }
    }
    @keyframes bounce-left {
    0% {
        transform: translate3d(100%, -50%, 0);
    }
    50% {
        transform: translate3d(-30px, -50%, 0);
    }
    100% {
        transform: translate3d(0, -50%, 0);
    }
    }
    /**
* * Bounce to the left side
* */
    @-webkit-keyframes bounce-right {
    0% {
        transform: translate3d(0, -50%, 0);
    }
    50% {
        transform: translate3d(calc(100% + 30px), -50%, 0);
    }
    100% {
        transform: translate3d(100%, -50%, 0);
    }
    }
    @keyframes bounce-right {
    0% {
        transform: translate3d(0, -50%, 0);
    }
    50% {
        transform: translate3d(calc(100% + 30px), -50%, 0);
    }
    100% {
        transform: translate3d(100%, -50%, 0);
    }
    }
    /**
* * Show Sign Up form
* */
    @-webkit-keyframes showSignUp {
    100% {
        opacity: 1;
        visibility: visible;
        transform: translate3d(0, 0, 0);
    }
    }
    @keyframes showSignUp {
    100% {
        opacity: 1;
        visibility: visible;
        transform: translate3d(0, 0, 0);
    }
    }
    /**
* * Page background
* */
    .user {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100vh;
    background: #ccc;
    background-size: cover;
    }
    .user_options-container {
    position: relative;
    width: 80%;
    }
    .user_options-text {
    display: flex;
    justify-content: space-between;
    width: 100%;
    background-color: #002035;
    border-radius: 0px;
    }

    fieldset.forms_fieldset {
    border: none !important;
    }
    /**
* * Registered and Unregistered user box and text
* */
    .user_options-registered,
    .user_options-unregistered {
    width: 50%;
    padding: 75px 45px;
    color: #fff;
    font-weight: 300;
    }

    .user_registered-title,
    .user_unregistered-title {
    margin-bottom: 15px;
    font-size: 1.66rem;
    line-height: 1em;
    }

    .user_unregistered-text,
    .user_registered-text {
    font-size: 0.83rem;
    line-height: 1.4em;
    }

    .user_registered-login,
    .user_unregistered-signup {
    margin-top: 30px;
    border: 1px solid #ccc;
    border-radius: 0px;
    padding: 10px 30px;
    color: #fff;
    text-transform: uppercase;
    line-height: 1em;
    letter-spacing: 0.2rem;
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
    }
    .user_registered-login:hover,
    .user_unregistered-signup:hover {
    color: rgba(34, 34, 34, 0.85);
    background-color: #ccc;
    }

    /**
* * Login and signup forms
* */
    .form-options {
    position: absolute;
    top: 50%;
    left: 30px;
    width: calc(50% - 30px);
    min-height: 420px;
    background-color: #fff;
    border-radius: 0px;
    box-shadow: 2px 0 15px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    transform: translate3d(100%, -50%, 0);
    transition: transform 0.4s ease-in-out;
    }
    .form-options .user_forms-login {
    transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out;
    }
    .form-options .forms_title {
    margin-bottom: 45px;
    font-size: 1.5rem;
    font-weight: 500;
    line-height: 1em;
    text-transform: uppercase;
    color: #00af9b;
    letter-spacing: 0.1rem;
    }
    .form-options .forms_field:not(:last-of-type) {
    margin-bottom: 20px;
    }
    .form-options .forms_field-input {
    width: 100%;
    border-bottom: 1px solid #ccc;
    padding: 6px 20px 6px 6px;
    font-family: "Fira Sans", sans-serif;
    font-size: 1rem;
    font-weight: 300;
    color: gray;
    letter-spacing: 0.1rem;
    transition: border-color 0.2s ease-in-out;
    }
    .form-options .forms_field-input:focus {
    border-color: gray;
    }
    .form-options .forms_buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 35px;
    }
    .form-options .forms_buttons-forgot {
    font-family: "Fira Sans", sans-serif;
    letter-spacing: 0.1rem;
    color: #ccc;
    text-decoration: underline;
    transition: color 0.2s ease-in-out;
    }
    .form-options .forms_buttons-forgot:hover {
    color: #b3b3b3;
    }
    .form-options .forms_buttons-action {
    background-color: #00af9b;
    border-radius: 0px;
    padding: 10px 35px;
    font-size: 1rem;
    font-family: "Fira Sans", sans-serif;
    font-weight: 300;
    color: #fff;
    text-transform: uppercase;
    letter-spacing: 0.1rem;
    transition: background-color 0.2s ease-in-out;
    }
    .form-options .forms_buttons-action:hover {
    background-color: #00af9b;
    }
    .form-options .user_forms-signup,
    .form-options .user_forms-login {
    position: absolute;
    top: 70px;
    left: 40px;
    width: calc(100% - 80px);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out,
        transform 0.5s ease-in-out;
    }
    .form-options .user_forms-signup {
    transform: translate3d(120px, 0, 0);
    }
    .form-options .user_forms-signup .forms_buttons {
    justify-content: flex-end;
    }
    .form-options .user_forms-login {
    transform: translate3d(0, 0, 0);
    opacity: 1;
    visibility: visible;
    }

    /**
* * Triggers
* */
    .form-options.bounce-left {
    -webkit-animation: bounce-left 1s forwards;
    animation: bounce-left 1s forwards;
    }
    .form-options.bounce-left .user_forms-signup {
    -webkit-animation: showSignUp 1s forwards;
    animation: showSignUp 1s forwards;
    }
    .form-options.bounce-left .user_forms-login {
    opacity: 0;
    visibility: hidden;
    transform: translate3d(-120px, 0, 0);
    }
    .form-options.bounce-right {
    -webkit-animation: bounce-right 1s forwards;
    animation: bounce-right 1s forwards;
    }

    /**
* * Responsive 990px
* */
    @media screen and (max-width: 990px) {
    .form-options {
        min-height: 350px;
    }
    .form-options .forms_buttons {
        flex-direction: column;
    }
    .form-options .user_forms-login .forms_buttons-action {
        margin-top: 30px;
    }
    .form-options .user_forms-signup,
    .form-options .user_forms-login {
        top: 40px;
    }

    .user_options-registered,
    .user_options-unregistered {
        padding: 50px 45px;
    }
    }
              </code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">
const signupButton = document.getElementById("signup-button"),
loginButton = document.getElementById("login-button"),
userForms = document.getElementById("form-options");
signupButton.addEventListener(
"click",
() => {
    userForms.classList.remove("bounce-right");
    userForms.classList.add("bounce-left");
},
false
);

loginButton.addEventListener(
"click",
() => {
    userForms.classList.remove("bounce-left");
    userForms.classList.add("bounce-right");
},
false
);
            </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
