
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">

    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}
    


    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
          <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
          <i class="fas fa-bars"></i>
        </span>
      </div>

<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
        <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
                <div>
                    <h4 class="size20 mb-2 pt-2">Design Masonry layout</h4>                   
                </div>
              </div>   
        </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/category-mansory/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
    &lt;script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.isotope/3.0.6/isotope.pkgd.min.js" integrity="sha512-Zq2BOxyhvnRFXu0+WE6ojpZLOU2jdnqbrM1hmVdGzyeCa1DgM3X5Q4A/Is9xA1IkbUeDd7755dNNI/PzSf2Pew==" crossorigin="anonymous"&gt;&lt;/script&gt;
    &lt;script src="https://cdnjs.cloudflare.com/ajax/libs/masonry/4.2.2/masonry.pkgd.min.js" integrity="sha512-JRlcvSZAXT8+5SQQAvklXGJuxXTouyq8oIMaYERZQasB8SBDHZaUbeASsJWpk0UUrf89DP3/aefPPrlMR1h1yQ==" crossorigin="anonymous"&gt;&lt;/script&gt;
    &lt;section&gt;
        &lt;div class="container"&gt;
            &lt;div class="row no-gutters"&gt;
                &lt;div class="filtering col-sm-12 text-center"&gt;
                    &lt;span data-filter="*" class="active"&gt;All&lt;/span&gt;
                    &lt;span data-filter=".categoryA" class=""&gt;category A&lt;/span&gt;
                    &lt;span data-filter=".categoryB" class=""&gt;category B&lt;/span&gt;
                    &lt;span data-filter=".categoryC" class=""&gt;category C&lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="col-12 text-center w-100"&gt;
                    &lt;div class="grid form-row gallery text-center"&gt;
                        &lt;div class="col-lg-4 col-sm-6 mb-2 grid-item categoryC"&gt;
                            &lt;div class="portfolio-wrapper"&gt;
                                &lt;div class="portfolio-image"&gt;
                                    &lt;img src="https://picsum.photos/250/300?random=1" alt="..." /&gt;
                                &lt;/div&gt;
                                &lt;div class="portfolio-overlay"&gt;
                                    &lt;div class="portfolio-content"&gt;
                                        &lt;a class="popimg ml-0" href="#"&gt;
                                            &lt;i class="ti-zoom-in display-24 display-md-23 display-lg-22 display-xl-20"&gt;&lt;/i&gt;
                                        &lt;/a&gt;
                                        &lt;h4&gt;Lorem, ipsum dolor.&lt;/h4&gt;
                                        &lt;p&gt;[category C]&lt;/p&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="col-lg-4 col-sm-6 mb-2 grid-item categoryB categoryC"&gt;
                            &lt;div class="portfolio-wrapper"&gt;
                                &lt;div class="portfolio-image"&gt;
                                    &lt;img src="https://picsum.photos/200/200?random=2" alt="..." /&gt;
                                &lt;/div&gt;
                                &lt;div class="portfolio-overlay"&gt;
                                    &lt;div class="portfolio-content"&gt;
                                        &lt;a class="popimg ml-0" href="#"&gt;
                                            &lt;i class="ti-zoom-in display-24 display-md-23 display-lg-22 display-xl-20"&gt;&lt;/i&gt;
                                        &lt;/a&gt;
                                        &lt;h4&gt;Lorem, ipsum dolor.&lt;/h4&gt;
                                        &lt;p&gt;[category B, category C]&lt;/p&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="col-lg-4 col-sm-6 mb-2 grid-item categoryA"&gt;
                            &lt;div class="portfolio-wrapper"&gt;
                                &lt;div class="portfolio-image"&gt;
                                    &lt;img src="https://picsum.photos/200/200?random=2" alt="..." /&gt;
                                &lt;/div&gt;
                                &lt;div class="portfolio-overlay"&gt;
                                    &lt;div class="portfolio-content"&gt;
                                        &lt;a class="popimg ml-0" href="#"&gt;
                                            &lt;i class="ti-zoom-in display-24 display-md-23 display-lg-22 display-xl-20"&gt;&lt;/i&gt;
                                        &lt;/a&gt;
                                        &lt;h4&gt;Lorem ipsum dolor sit amet.&lt;/h4&gt;
                                        &lt;p&gt;[category A]&lt;/p&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="col-lg-4 col-sm-6 mb-2 grid-item categoryC"&gt;
                            &lt;div class="portfolio-wrapper"&gt;
                                &lt;div class="portfolio-image"&gt;
                                    &lt;img src="https://picsum.photos/450/350?random=2" alt="..." /&gt;
                                &lt;/div&gt;
                                &lt;div class="portfolio-overlay"&gt;
                                    &lt;div class="portfolio-content"&gt;
                                        &lt;a class="popimg ml-0" href="#"&gt;
                                            &lt;i class="ti-zoom-in display-24 display-md-23 display-lg-22 display-xl-20"&gt;&lt;/i&gt;
                                        &lt;/a&gt;
                                        &lt;h4&gt;Lorem, ipsum dolor.&lt;/h4&gt;
                                        &lt;p&gt;[category C]&lt;/p&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="col-lg-4 col-sm-6 mb-2 grid-item categoryA"&gt;
                            &lt;div class="portfolio-wrapper"&gt;
                                &lt;div class="portfolio-image"&gt;
                                    &lt;img src="https://picsum.photos/250/300?random=1" alt="..." /&gt;
                                &lt;/div&gt;
                                &lt;div class="portfolio-overlay"&gt;
                                    &lt;div class="portfolio-content"&gt;
                                        &lt;a class="popimg ml-0" href="#"&gt;
                                            &lt;i class="ti-zoom-in display-24 display-md-23 display-lg-22 display-xl-20"&gt;&lt;/i&gt;
                                        &lt;/a&gt;
                                        &lt;h4&gt;Lorem ipsum dolor sit.&lt;/h4&gt;
                                        &lt;p&gt;[category A]&lt;/p&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="col-lg-4 col-sm-6 mb-2 grid-item categoryB"&gt;
                            &lt;div class="portfolio-wrapper"&gt;
                                &lt;div class="portfolio-image"&gt;
                                    &lt;img src="https://picsum.photos/200/200?random=2" alt="..." /&gt;
                                &lt;/div&gt;
                                &lt;div class="portfolio-overlay"&gt;
                                    &lt;div class="portfolio-content"&gt;
                                        &lt;a class="popimg ml-0" href="#"&gt;
                                            &lt;i class="ti-zoom-in display-24 display-md-23 display-lg-22 display-xl-20"&gt;&lt;/i&gt;
                                        &lt;/a&gt;
                                        &lt;h4&gt;Lorem ipsum dolor sit.&lt;/h4&gt;
                                        &lt;p&gt;[category B]&lt;/p&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="col-lg-4 col-sm-6 mb-2 mb-lg-0 grid-item categoryC"&gt;
                            &lt;div class="portfolio-wrapper"&gt;
                                &lt;div class="portfolio-image"&gt;
                                    &lt;img src="https://picsum.photos/350/250" alt="..." /&gt;
                                &lt;/div&gt;
                                &lt;div class="portfolio-overlay"&gt;
                                    &lt;div class="portfolio-content"&gt;
                                        &lt;a class="popimg ml-0" href="#"&gt;
                                            &lt;i class="ti-zoom-in display-24 display-md-23 display-lg-22 display-xl-20"&gt;&lt;/i&gt;
                                        &lt;/a&gt;
                                        &lt;h4&gt;Lorem, ipsum dolor.&lt;/h4&gt;
                                        &lt;p&gt;[category C]&lt;/p&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="col-lg-4 col-sm-6 mb-2 mb-sm-0 grid-item categoryB"&gt;
                            &lt;div class="portfolio-wrapper"&gt;
                                &lt;div class="portfolio-image"&gt;
                                    &lt;img src="https://picsum.photos/300/350?random=2" alt="..." /&gt;
                                &lt;/div&gt;
                                &lt;div class="portfolio-overlay"&gt;
                                    &lt;div class="portfolio-content"&gt;
                                        &lt;a class="popimg ml-0" href="#"&gt;
                                            &lt;i class="ti-zoom-in display-24 display-md-23 display-lg-22 display-xl-20"&gt;&lt;/i&gt;
                                        &lt;/a&gt;
                                        &lt;h4&gt;Lorem ipsum dolor sit amet.&lt;/h4&gt;
                                        &lt;p&gt;[category B]&lt;/p&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="col-lg-4 col-sm-6 grid-item categoryA"&gt;
                            &lt;div class="portfolio-wrapper"&gt;
                                &lt;div class="portfolio-image"&gt;
                                    &lt;img src="https://picsum.photos/200/200?random=2" alt="..." /&gt;
                                &lt;/div&gt;
                                &lt;div class="portfolio-overlay"&gt;
                                    &lt;div class="portfolio-content"&gt;
                                        &lt;a class="popimg ml-0" href="#"&gt;
                                            &lt;i class="ti-zoom-in display-24 display-md-23 display-lg-22 display-xl-20"&gt;&lt;/i&gt;
                                        &lt;/a&gt;
                                        &lt;h4&gt;Lorem, ipsum dolor.&lt;/h4&gt;
                                        &lt;p&gt;[category A]&lt;/p&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/section&gt;
</code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
body{margin-top:20px;}
.filtering {
    margin-bottom: 40px;
}
.filtering span {
    border-bottom: 2px solid transparent;
    color: #282b2d;
    cursor: pointer;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 1px;
    margin-right: 20px;
    display: inline-block;
    margin-bottom: 5px;
}
.filtering span:last-child {
    margin: 0;
}
.filtering .active {
    border-color: #00af9b;
    color: #00af9b;
}
.portfolio-wrapper {
    position: relative;
    overflow: hidden;
}
.portfolio-overlay {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    transition: all 500ms ease;
}
.portfolio-wrapper .portfolio-image img {
    transform: scale(1.2);
    will-change: transform;
    transition: all 0.5s ease;
    width: 100%;
}
.portfolio-wrapper:hover .portfolio-image img {
    transform: none;
}
.portfolio-overlay:before {
    position: absolute;
    display: inline-block;
    top: 15px;
    right: 15px;
    bottom: 15px;
    left: 15px;
    border: 1px solid rgba(0, 0, 0, 0.36);
    content: "";
    opacity: 0;
    transition: all 0.5s ease;
    transform: scale(0.85);
}
.portfolio-overlay .portfolio-content {
    position: absolute;
    bottom: 50%;
    left: 0;
    width: 100%;
    text-align: center;
    opacity: 0;
    padding: 0 35px;
}
.portfolio-content h4 {
    color: #000;
    font-weight: 600;
    font-size: 20px;
    text-transform: capitalize;
    letter-spacing: 1px;
    margin-bottom: 15px;
}
.portfolio-content p {
    color: #000;
    font-weight: 500;
    letter-spacing: 1px;
    margin-bottom: 0;
}
.portfolio-content > a {
    line-height: 42px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: #000;
    display: inline-block;
    text-align: center;
    margin-bottom: 15px;
    font-weight: 800;
}
.portfolio-wrapper:hover .portfolio-overlay {
    background-color: rgba(204, 222, 2, 0.85);
}
.portfolio-wrapper:hover .portfolio-overlay:before {
    opacity: 1;
    visibility: visible;
    transform: none;
}
.portfolio-wrapper:hover .portfolio-overlay .portfolio-content {
    transform: translateY(50%);
    transition: transform 0.5s ease;
    opacity: 1;
}
@media screen and (max-width: 1199px) {
    .portfolio-content h4 {
        font-size: 18px;
    }
}
@media screen and (max-width: 991px) {
    .portfolio-content h4 {
        margin-bottom: 10px;
    }
    .portfolio-content p {
        font-size: 15px;
    }
    .portfolio-content > a {
        margin-bottom: 10px;
    }
}
@media screen and (max-width: 767px) {
    .portfolio-content h4 {
        font-size: 17px;
    }
    .portfolio-content p {
        font-size: 14px;
    }
}
@media screen and (max-width: 575px) {
    .portfolio-content h4 {
        font-size: 16px;
    }
}
.grid .grid-item {
    position: relative;
    overflow: hidden;
}
.grid .grid-item .portfolio-wrapper {
    position: relative;
    overflow: hidden;
}
.grid .grid-item .portfolio-overlay {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    transition: all 500ms ease;
}
.grid .grid-item .portfolio-wrapper .portfolio-image img {
    transform: none;
    will-change: transform;
    transition: none;
    width: 100%;
    min-height: 300px;
}
.grid .grid-item .portfolio-wrapper:hover .portfolio-image img {
    transform: none;
}
.grid .grid-item .portfolio-overlay:before {
    position: absolute;
    display: inline-block;
    top: 15px;
    right: 15px;
    bottom: 15px;
    left: 15px;
    border: 1px solid rgba(0, 0, 0, 0.36);
    content: "";
    opacity: 0;
    transition: all 0.5s ease;
    transform: scale(0.85);
}
.grid .grid-item .portfolio-overlay .portfolio-content {
    position: absolute;
    bottom: 50%;
    left: 0;
    width: 100%;
    text-align: center;
    opacity: 0;
}
.grid .grid-item .portfolio-content h4 {
    color: #fff;
    font-weight: 600;
    letter-spacing: 1px;
    font-size: 20px;
    margin-bottom: 10px;
}
.grid .grid-item .portfolio-content p {
    color: #fff;
    font-weight: 500;
    letter-spacing: 1px;
    margin-bottom: 0;
}
.grid .grid-item .portfolio-content a {
    line-height: 36px;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    color: #000;
    display: inline-block;
    text-align: center;
    margin-bottom: 10px;
    font-weight: 800;
}
.grid .grid-item .portfolio-wrapper:hover .portfolio-overlay {
    background-color: rgb(0 175 155 / 38%);
}
.grid .grid-item .portfolio-wrapper:hover .portfolio-overlay:before {
    opacity: 1;
    visibility: visible;
    transform: none;
}
.grid .grid-item .portfolio-wrapper:hover .portfolio-overlay .portfolio-content {
    transform: translateY(50%);
    transition: transform 0.5s ease;
    opacity: 1;
}
@media screen and (max-width: 1199px) {
    .grid .grid-item .portfolio-content h4 {
        font-size: 18px;
    }
}
@media screen and (max-width: 991px) {
    .grid .grid-item .portfolio-content h4 {
        margin-bottom: 10px;
    }
    .grid .grid-item .portfolio-content p {
        font-size: 15px;
    }
    .grid .grid-item .portfolio-content a {
        margin-bottom: 10px;
    }
}
@media screen and (max-width: 767px) {
    .grid .grid-item .portfolio-content h4 {
        font-size: 17px;
    }
    .grid .grid-item .portfolio-content p {
        font-size: 14px;
    }
}
@media screen and (max-width: 575px) {
    .grid .grid-item .portfolio-content h4 {
        font-size: 16px;
    }
    .grid .grid-item .portfolio-overlay:before {
        top: 10px;
        right: 10px;
        bottom: 10px;
        left: 10px;
    }
}
</code></pre>
        </div>
        <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">
$(function(){
    $(".grid").masonry({ itemSelector: ".grid-item" });
    
    $(".filtering").on("click", "span", function () {
        var a = $(".gallery").isotope({});
        var e = $(this).attr("data-filter");
        a.isotope({ filter: e });
    });
    $(".filtering").on("click", "span", function () {
        $(this).addClass("active").siblings().removeClass("active");
    });
}) 
            </code></pre>
        </div>
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
