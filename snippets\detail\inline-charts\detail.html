<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>In-line Charts</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Microcharts - In-line Charts</h4>
              <div class="mb-2">
                <a
                  href="./downloads/inline-charts.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/inline-charts/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
              <pre class="line-numbers">
<code class="language-markup">
&lt;div class="container"&gt;
&lt;div class="row"&gt;
    &lt;div class="col-sm-12"&gt;
    &lt;script src="https://cdn.amcharts.com/lib/4/core.js"&gt;&lt;/script&gt;
    &lt;script src="https://cdn.amcharts.com/lib/4/charts.js"&gt;&lt;/script&gt;
    &lt;script src="https://cdn.amcharts.com/lib/4/themes/animated.js"&gt;&lt;/script&gt;
    &lt;div class="py-4"&gt;
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam
        pulvinar non erat sed dapibus. Aenean vitae lobortis justo, ut
        posuere diam. Vestibulum eget massa maximus dui posuere posuere.
        Phasellus eget tortor ipsum. Integer sollicitudin ex lacus,
        condimentum mollis
        &lt;div id="chartdiv"&gt;&lt;/div&gt;
        neque finibus in. Nam et justo at nisi tincidunt auctor non quis
        nisl. Curabitur tempor pellentesque justo eu mattis. Suspendisse
        potenti. Aenean at convallis lacus. Aliquam vitae eros ornare,
        facilisis tellus at, rutrum nibh. Duis ac nisl ac neque hendrerit
        finibus nec non dolor. Morbi non viverra tortor, eu auctor lorem.
        Pellentesque posuere, justo eget sollicitudin viverra, quam nisl
        molestie sapien, in viverra turpis justo venenatis
        &lt;div id="chartdiv2"&gt;&lt;/div&gt;
        massa. Pellentesque ac diam sit amet arcu feugiat eleifend non sit
        amet dui.
    &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
&lt;/div&gt;
</code>
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">#chartdiv {
width: 80px;
height: 20px;
display: inline-block;
vertical-align: middle;
}

#chartdiv2 {
width: 20px;
height: 20px;
display: inline-block;
vertical-align: middle;
}

</code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">am4core.useTheme(am4themes_animated);
function am4themes_myTheme(target) {
if (target instanceof am4core.ColorSet) {
target.list = [
am4core.color("#00b19c"),
am4core.color("#3bcd3f"),
am4core.color("#007365"),
am4core.color("#8dbac4"),
am4core.color("#002035"),
am4core.color("#33c1b0"),
am4core.color("#62d765"),
am4core.color("#338f84"),
am4core.color("#a4c8d0"),
am4core.color("#334d5d"),
am4core.color("#66d0c4"),
am4core.color("#89e18c"),
am4core.color("#66aba3"),
am4core.color("#bbd6dc"),
am4core.color("#667986"),
];
}
}

am4core.useTheme(am4themes_myTheme);

var chart = am4core.create("chartdiv", am4charts.XYChart);

chart.data = [
{
date: new Date(2018, 0, 1, 8, 0, 0),
value: 57,
},
{
date: new Date(2018, 0, 1, 9, 0, 0),
value: 27,
},
{
date: new Date(2018, 0, 1, 10, 0, 0),
value: 24,
},
{
date: new Date(2018, 0, 1, 11, 0, 0),
value: 59,
},
{
date: new Date(2018, 0, 1, 12, 0, 0),
value: 33,
},
{
date: new Date(2018, 0, 1, 13, 0, 0),
value: 46,
},
{
date: new Date(2018, 0, 1, 14, 0, 0),
value: 20,
},
{
date: new Date(2018, 0, 1, 15, 0, 0),
value: 42,
},
{
date: new Date(2018, 0, 1, 16, 0, 0),
value: 59,
opacity: 1,
},
];

chart.padding(0, 0, 0, 0);

var dateAxis = chart.xAxes.push(new am4charts.DateAxis());
dateAxis.renderer.grid.template.disabled = true;
dateAxis.renderer.labels.template.disabled = true;
dateAxis.startLocation = 0.5;
dateAxis.endLocation = 0.7;
dateAxis.cursorTooltipEnabled = false;

var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
valueAxis.min = 0;
valueAxis.renderer.grid.template.disabled = true;
valueAxis.renderer.baseGrid.disabled = true;
valueAxis.renderer.labels.template.disabled = true;
valueAxis.cursorTooltipEnabled = false;

chart.cursor = new am4charts.XYCursor();
chart.cursor.lineY.disabled = true;

var series = chart.series.push(new am4charts.LineSeries());
series.tooltipText = "{date}: [bold]{value}";
series.dataFields.dateX = "date";
series.dataFields.valueY = "value";
series.tensionX = 0.8;
series.strokeWidth = 2;

// render data points as bullets
var bullet = series.bullets.push(new am4charts.CircleBullet());
bullet.circle.opacity = 0;
bullet.circle.propertyFields.opacity = "opacity";
bullet.circle.radius = 3;

// Create chart instance
var chart2 = am4core.create("chartdiv2", am4charts.PieChart);

// Add data
chart2.data = [
{
country: "Lithuania",
litres: 501.9,
},
{
country: "Czech Republic",
litres: 301.9,
},
{
country: "Ireland",
litres: 201.1,
},
{
country: "Germany",
litres: 165.8,
},
];

// Remove padding
chart2.padding(0, 0, 0, 0);

// Add and configure Series
var pieSeries = chart2.series.push(new am4charts.PieSeries());
pieSeries.dataFields.value = "litres";
pieSeries.dataFields.category = "country";
pieSeries.labels.template.disabled = true;
pieSeries.ticks.template.disabled = true;

chart2.chartContainer.minHeight = 20;
chart2.chartContainer.minWidth = 20;

            </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
