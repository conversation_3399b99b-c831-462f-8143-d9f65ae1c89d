
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}


    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
          <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
          <i class="fas fa-bars"></i>
        </span>
      </div>
<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
        <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">ChatGPT Animation [Text Response]</h4>
              <div class="mb-2">
                <a
                  href="./downloads/chatgpt-animation.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <li><a href="#tab04">Javascript</a></li>
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/chatgpt-animation/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
    &lt;div class="chat-container" id="chat-container"&gt;&lt;/div&gt;
  
    
    &lt;div class="typing-container"&gt;
      &lt;div class="typing-content"&gt;
        &lt;div class="typing-textarea"&gt;
          &lt;textarea id="chat-input" spellcheck="false" placeholder="Enter a prompt here" required&gt;&lt;/textarea&gt;
          &lt;span id="send-btn" class="material-symbols-rounded"&gt;send&lt;/span&gt;
        &lt;/div&gt;
        &lt;div class="typing-controls"&gt;
          &lt;span id="theme-btn" class="material-symbols-rounded"&gt;light_mode&lt;/span&gt;
          &lt;span id="delete-btn" class="material-symbols-rounded"&gt;delete&lt;/span&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  
    
    &lt;script src="https://cdnjs.cloudflare.com/ajax/libs/markdown-it/12.0.4/markdown-it.min.js"&gt;&lt;/script&gt;
    &lt;script src="https://cdnjs.cloudflare.com/ajax/libs/typed.js/2.0.11/typed.min.js"&gt;&lt;/script&gt;
    &lt;script src="script.js"&gt;&lt;/script&gt;
  </code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">@import url('https://fonts.googleapis.com/css?family=Fira+Sans');
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
              font-family: "Fira Sans", sans-serif;
            }
            :root {
              --text-color: #FFFFFF;
              --icon-color: #ACACBE;
              --icon-hover-bg: #5b5e71;
              --placeholder-color: #dcdcdc;
              --outgoing-chat-bg: #343541;
              --incoming-chat-bg: #444654;
              --outgoing-chat-border: #343541;
              --incoming-chat-border: #444654;
            }
            .light-mode {
              --text-color: #343541;
              --icon-color: #a9a9bc;
              --icon-hover-bg: #f1f1f3;
              --placeholder-color: #6c6c6c;
              --outgoing-chat-bg: #FFFFFF;
              --incoming-chat-bg: #F7F7F8;
              --outgoing-chat-border: #FFFFFF;
              --incoming-chat-border: #D9D9E3;
            }
            body {
              background: var(--outgoing-chat-bg);
            }
            
            /* Chats container styling */
            .chat-container {
              overflow-y: auto;
              max-height: 100vh;
              padding-bottom: 150px;
            }
            :where(.chat-container, textarea)::-webkit-scrollbar {
              width: 6px;
            }
            :where(.chat-container, textarea)::-webkit-scrollbar-track {
              background: var(--incoming-chat-bg);
              border-radius: 25px;
            }
            :where(.chat-container, textarea)::-webkit-scrollbar-thumb {
              background: var(--icon-color);
              border-radius: 25px;
            }
            .default-text {
              display: flex;
              align-items: center;
              justify-content: center;
              flex-direction: column;
              height: 70vh;
              padding: 0 10px;
              text-align: center;
              color: var(--text-color);
            }
            h1 {
              font-size: 1.3rem;
            }
            .default-text p {
              margin-top: 10px;
              font-size: 1.0rem;
            }
            .chat-container .chat {
              padding: 25px 10px;
              display: flex;
              justify-content: center;
              color: var(--text-color);
            }
            .chat-container .chat.outgoing {
              background: var(--outgoing-chat-bg);
              border: 1px solid var(--outgoing-chat-border);
            }
            .chat-container .chat.incoming {
              background: var(--incoming-chat-bg);
              border: 1px solid var(--incoming-chat-border);
            }
            .chat .chat-content {
              display: flex;
              max-width: 1200px;
              width: 100%;
              align-items: flex-start;
              justify-content: space-between;
            }
            span.material-symbols-rounded {
              user-select: none;
              cursor: pointer;
            }
            .chat .chat-content span {
              cursor: pointer;
              font-size: 1.3rem;
              color: var(--icon-color);
              visibility: hidden;
            }
            .chat:hover .chat-content:not(:has(.typing-animation), :has(.error)) span {
              visibility: visible;
            }
            .chat .chat-details {
              display: flex;
              align-items: center;
            }
            .chat .chat-details img.avt {
              width: 35px;
              height: 35px;
              align-self: flex-start;
              object-fit: cover;
              border-radius: 2px;
            }
            .chat .chat-details .chatMsg {
              white-space: pre-wrap;
              font-size: 1.05rem;
              padding: 0 50px 0 25px;
              color: var(--text-color);
              word-break: break-word;
            }
            .chat .chat-details p.error {
              color: #e55865;
            }
            .chat .typing-animation {
              padding-left: 25px;
              display: inline-flex;
            }
            .typing-animation .typing-dot {
              height: 7px;
              width: 7px;
              border-radius: 50%;
              margin: 0 3px;
              opacity: 0.7;
              background: var(--text-color);
              animation: animateDots 1.5s var(--delay) ease-in-out infinite;
            }
            .typing-animation .typing-dot:first-child {
              margin-left: 0;
            }
            @keyframes animateDots {
              0%,44% {
                transform: translateY(0px);
              }
              28% {
                opacity: 0.4;
                transform: translateY(-6px);
              }
              44% {
                opacity: 0.2;
              }
            }
            
            /* Typing container styling */
            .typing-container {
              position: fixed;
              bottom: 0;
              width: 100%;
              display: flex;
              padding: 20px 10px;
              justify-content: center;
              background: var(--outgoing-chat-bg);
              border-top: 1px solid var(--incoming-chat-border);
            }
            .typing-container .typing-content {
              display: flex;
              max-width: 950px;
              width: 100%;
              align-items: flex-end;
            }
            .typing-container .typing-textarea {
              width: 100%;
              display: flex;
              position: relative;
            }
            .typing-textarea textarea {
              resize: none;
              height: 55px;
              width: 100%;
              border: none;
              padding: 15px 45px 15px 20px;
              color: var(--text-color);
              font-size: 1rem;
              border-radius: 4px;
              max-height: 250px;
              overflow-y: auto;
              background: var(--incoming-chat-bg);
              outline: 1px solid var(--incoming-chat-border);
            }
            .typing-textarea textarea::placeholder {
              color: var(--placeholder-color);
            }
            .typing-content span {
              width: 55px;
              height: 55px;
              display: flex;
              border-radius: 4px;
              font-size: 1.35rem;
              align-items: center;
              justify-content: center;
              color: var(--icon-color);
            }
            .typing-textarea span {
              position: absolute;
              right: 0;
              bottom: 0;
              visibility: hidden;
            }
            .typing-textarea textarea:valid ~ span {
              visibility: visible;
            }
            .typing-controls {
              display: flex;
            }
            .typing-controls span {
              margin-left: 7px;
              font-size: 1.4rem;
              background: var(--incoming-chat-bg);
              outline: 1px solid var(--incoming-chat-border);
            }
            .typing-controls span:hover {
              background: var(--icon-hover-bg);
            }
            
            /* Reponsive Media Query */
            @media screen and (max-width: 600px) {
              .default-text h1 {
                font-size: 2.3rem;
              }
              :where(.default-text p, textarea, .chat p) {
                font-size: 0.95rem!important;
              }
              .chat-container .chat {
                padding: 20px 10px;
              }
              .chat-container .chat img {
                height: 32px;
                width: 32px;
              }
              .chat-container .chat p {
                padding: 0 20px;
              }
              .chat .chat-content:not(:has(.typing-animation), :has(.error)) span {
                visibility: visible;
              }
              .typing-container {
                padding: 15px 10px;
              }
              .typing-textarea textarea {
                height: 45px;
                padding: 10px 40px 10px 10px;
              }
              .typing-content span {
                height: 45px;
                width: 45px;
                margin-left: 5px;
              }
              span.material-symbols-rounded {
                font-size: 1.25rem!important;
              }
            }
            table {
              border: 1px solid #ddd;
              width: 100%;
            
            }
            table th {
              background: #000;
              color: #fff;
            }
            pre 
             {
              padding: 10px;
              background: #000;
              color: #fff;
            }
            h2 {
              font-size: 18px;
              font-weight: normal;
            }
            h1 {
              font-weight: normal;
            }
            table {
              width: 100%;
              border: 1px solid white;
              border-collapse: collapse;
            }
            
            table td, table th {
              border: 1px solid white;
              padding: 5px;
              font-weight: normal;
            }
            .mainUser {
              padding: 0 50px 0 25px;
            }
            
            ol, ul {
              margin-left: 25px;
            }
            .chatMsg img {
              width: 40%;
            }
            
            a {
              color: #00b19c;
              text-decoration: none;
            }
            </code></pre>
        </div>
        <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">const chatInput = document.querySelector("#chat-input");
                const sendButton = document.querySelector("#send-btn");
                const chatContainer = document.querySelector("#chat-container");
                const themeButton = document.querySelector("#theme-btn");
                const deleteButton = document.querySelector("#delete-btn");
                
                let userText = null;
                
                const showBannerMessage = () => {
                  localStorage.removeItem("all-chats");
                    // Load saved chats and theme from local storage and add them to the chat container
                    const themeColor = localStorage.getItem("themeColor");
                  
                    document.body.classList.toggle("light-mode", themeColor === "light_mode");
                    themeButton.innerText = document.body.classList.contains("light-mode") ? "dark_mode" : "light_mode";
                  
                
                      const defaultText = `<div class="default-text">
                                              <h1 style="font-size: 50px;">ChatGPT Response ui - POC</h1>
                                              <p style="font-size: 20px;">Using Markdown and Typed.js for animation</p>
                                          </div>`;
                      chatContainer.innerHTML = defaultText;
                  
                  
                    chatContainer.scrollTo(0, chatContainer.scrollHeight); // Scroll to the bottom of the chat container
                  };
                  
                  
                
                const createChatElement = (content, className) => {
                  const chatDiv = document.createElement("div");
                  chatDiv.classList.add("chat", className);
                  chatDiv.innerHTML = content;
                  return chatDiv; // Return the created chat div
                };
                
                const showResponseWithAnimation = (markdownResponse) => {
                  const responseDiv = createChatElement('<div class="chat-content"><div class="chat-details"><img class="avt" src="images/chatbot.jpg" alt="user-img"><div class="chatMsg"></div></div></div>', "incoming", "fadeIn");
                  chatContainer.appendChild(responseDiv);
                
                  const responseContent = responseDiv.querySelector(".chatMsg");
                
                  // Initialize Markdown-it parser
                  const md = window.markdownit();
                
                  // Convert Markdown to HTML
                  const htmlContent = md.render(markdownResponse);
                
                  
                
                  const typing = new Typed(responseContent, {
                    strings: [htmlContent],
                    typeSpeed: 1,
                    showCursor: false,
                    onComplete: () => {
                      // After the typing animation is completed, scroll to the bottom of the chat container
                      chatContainer.scrollTo(0, chatContainer.scrollHeight);
                    },
                  });
                
                  // Save the chats in local storage
                  const allChats = chatContainer.innerHTML;
                  console.log(allChats)
                  localStorage.setItem("all-chats", allChats);
                
                };
                
                const handleOutgoingChat = () => {
                  userText = chatInput.value.trim();
                  if (!userText) return;
                
                  const html = `<div class="chat-content">
                                  <div class="chat-details">
                                      <img src="images/user.jpg" alt="user-img" class="avt">
                                      <div class="mainUser">${userText}</div>
                                  </div>
                              </div>`;
                
                  chatContainer.querySelector(".default-text")?.remove();
                  const outgoingChatDiv = createChatElement(html, "outgoing");
                  chatContainer.appendChild(outgoingChatDiv);
                  chatContainer.scrollTo(0, chatContainer.scrollHeight);
                
                  const markdownResponse = `# Welcome to the API Response
                
                  This is some **bold** and *italic* text.
                  
                  ## Paragraphs
                  
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus scelerisque scelerisque orci, id volutpat neque ullamcorper in. Nulla ante mi, pulvinar vitae lorem sit amet, semper viverra lorem. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras euismod augue elementum efficitur vestibulum.
                  
                  This is the second paragraph.
                  
                  ## Table
                  
                  | Name  | Age | Profession |
                  |-------|-----|------------|
                  | Vimal  | 30  | Engineer  |
                  | Sneha | 28  |  Artist    |
                  | Sunil   | 35  | Manager |
                
                ## List
                - First item
                - Second item
                - Third item
                - Fourth item
                
                ## Ordered list
                1. First item
                2. Second item
                3. Third item
                4. Fourth item
                
                ## Indented list
                1. First item
                2. Second item
                3. Third item
                    1. Indented item
                    2. Indented item
                4. Fourth item
                  
                  ## Code Snippet
                  
                  \`\`\`javascript
                  function greet(name) {
                    console.log('Hello, ' + name + '!');
                  }
                  
                  greet('Vimal Thapliyal');
                  \`\`\`
                  
                  My favorite search engine is [Duck Duck Go](https://duckduckgo.com).
                
                 ## Images
                  ![The Smart Cube](https://smartrisk.thesmartcube.com/images/thesmartcube_new.svg)
                
                
                
                  `;
                
                  setTimeout(() => showResponseWithAnimation(markdownResponse), 500);
                //   const allChats = chatContainer.innerHTML;
                //   localStorage.setItem("all-chats", allChats);
                  chatInput.value = ""; // Clear the input field
                };
                
                deleteButton.addEventListener("click", () => {
                  if (confirm("Are you sure you want to delete all the chats?")) {
                  //  write logic to clear chat
                  }
                });
                
                themeButton.addEventListener("click", () => {
                  document.body.classList.toggle("light-mode");
                  localStorage.setItem("themeColor", themeButton.innerText);
                  themeButton.innerText = document.body.classList.contains("light-mode") ? "dark_mode" : "light_mode";
                });
                
                const initialInputHeight = chatInput.scrollHeight;
                
                chatInput.addEventListener("input", () => {
                  chatInput.style.height = `${initialInputHeight}px`;
                  chatInput.style.height = `${chatInput.scrollHeight}px`;
                });
                
                chatInput.addEventListener("keydown", (e) => {
                  if (e.key === "Enter" && !e.shiftKey && window.innerWidth > 800) {
                    e.preventDefault();
                    handleOutgoingChat();
                  }
                });
                
                document.addEventListener("DOMContentLoaded", showBannerMessage);
                sendButton.addEventListener("click", handleOutgoingChat);
                
</code></pre>
        </div>
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
      <script src="./js/iframe.js"></script>
    </body>
</html>
