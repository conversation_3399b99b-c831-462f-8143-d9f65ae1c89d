<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <meta name="author" content="Vimal Thapliyal">

    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./css/uikit.css">
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>
        #tab-button {
            display: table;
            table-layout: fixed;
            width: 100%;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        #tab-button li {
            display: table-cell;
            width: 20%;
        }

        #tab-button li a {
            display: block;
            padding: .5em;
            background: #eee;
            border: 1px solid #ddd;
            text-align: center;
            color: #000;
            text-decoration: none;
        }

        #tab-button li:not(:first-child) a {
            border-left: none;
        }

        #tab-button li a:hover,
        #tab-button .is-active a {
            border-bottom-color: transparent;
            background: #fff;
        }

        .tab-contents {
            padding: .5em 2em 1em;
            border: 1px solid #ddd;
        }



        .tab-button-outer {
            display: none;
        }

        .tab-contents {
            margin-top: 20px;
        }

        @media screen and (min-width: 768px) {
            .tab-button-outer {
                position: relative;
                z-index: 2;
                display: block;
            }

            .tab-select-outer {
                display: none;
            }

            .tab-contents {
                position: relative;
                top: -1px;
                margin-top: 0;
            }
        }




        code[class*="language-"],
        pre[class*="language-"] {
            color: black;
            text-shadow: 0 1px white;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            direction: ltr;
            text-align: left;
            white-space: pre;
            word-spacing: normal;
            word-break: normal;
            line-height: 1.5;

            -moz-tab-size: 4;
            -o-tab-size: 4;
            tab-size: 4;

            -webkit-hyphens: none;
            -moz-hyphens: none;
            -ms-hyphens: none;
            hyphens: none;
        }

        pre[class*="language-"]::-moz-selection,
        pre[class*="language-"] ::-moz-selection,
        code[class*="language-"]::-moz-selection,
        code[class*="language-"] ::-moz-selection {
            text-shadow: none;
            background: #b3d4fc;
        }

        pre[class*="language-"]::selection,
        pre[class*="language-"] ::selection,
        code[class*="language-"]::selection,
        code[class*="language-"] ::selection {
            text-shadow: none;
            background: #b3d4fc;
        }

        @media print {

            code[class*="language-"],
            pre[class*="language-"] {
                text-shadow: none;
            }
        }

        /* Code blocks */
        pre[class*="language-"] {
            padding: 1em;
            margin: .5em 0;
            overflow: auto;
        }

        :not(pre)>code[class*="language-"],
        pre[class*="language-"] {
            background: #f5f2f0;
        }

        /* Inline code */
        :not(pre)>code[class*="language-"] {
            padding: .1em;
            border-radius: .3em;
        }

        .token.comment,
        .token.prolog,
        .token.doctype,
        .token.cdata {
            color: slategray;
        }

        .token.punctuation {
            color: #999;
        }

        .namespace {
            opacity: .7;
        }

        .token.property,
        .token.tag,
        .token.boolean,
        .token.number,
        .token.constant,
        .token.symbol {
            color: #905;
        }

        .token.selector,
        .token.attr-name,
        .token.string,
        .token.char,
        .token.builtin {
            color: #690;
        }

        .token.operator,
        .token.entity,
        .token.url,
        .language-css .token.string,
        .style .token.string,
        .token.variable {
            color: #a67f59;
            background: hsla(0, 0%, 100%, .5);
        }

        .token.atrule,
        .token.attr-value,
        .token.keyword {
            color: #07a;
        }

        .token.function {
            color: #DD4A68;
        }

        .token.regex,
        .token.important {
            color: #e90;
        }

        .token.important {
            font-weight: bold;
        }

        .token.entity {
            cursor: help;
        }

        pre.line-numbers {
            position: relative;
            padding-left: 3.8em;
            counter-reset: linenumber;
        }

        pre.line-numbers>code {
            position: relative;
        }

        .line-numbers .line-numbers-rows {
            position: absolute;
            pointer-events: none;
            top: 0;
            font-size: 100%;
            left: -3.8em;
            width: 3em;
            /* works for line-numbers below 1000 lines */
            letter-spacing: -1px;
            border-right: 1px solid #999;

            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;

        }

        .line-numbers-rows>span {
            pointer-events: none;
            display: block;
            counter-increment: linenumber;
        }

        .line-numbers-rows>span:before {
            content: counter(linenumber);
            color: #999;
            display: block;
            padding-right: 0.8em;
            text-align: right;
        }

        .close-menu-icon {
            position: relative;
            top: 10px;
        }

        .hamSection {
            top: 67px;
            padding-top: 0
        }

        .close-menu-icon {
            top: 5px
        }
    </style>
</head>

<body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
            <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu" style="display: none;" class="close-menu-icon" onclick="openNav()">
            <i class="fas fa-bars"></i>
        </span>
    </div>

    <main role="main">
        <div class="container-fluid mt-1">
            <div class="row">
                <script src="./menuNav.js"></script>
                <div id="rightSection" class="col-sm-10">
                    <div class="col-sm-12 p-0">
                        <div class="d-flex justify-content-between">
                            <h4 class="size20 mb-2 pt-2">Dual Axis forecasted line chart</h4>
                            <div class="mb-2">
                                <a href="./downloads/dual-axis-forecasted.zip"
                                    class="btn btn-sm btn-outline-primary btn-dark mr-2"><i class="fas fa-download"></i>
                                    Download zip</a>
                                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                                    <i class="fas fa-expand"></i> View on Fullscreen
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="tabs">
                        <div class="tab-button-outer">
                            <ul id="tab-button">
                                <li><a href="#tab01">Preview</a></li>
                                <li><a href="#tab02">HTML</a></li>
                                <li><a href="#tab03">CSS</a></li>
                                <li><a href="#tab04">Javascript</a></li>
                                <!-- <li><a href="#tab04">Javascript</a></li> -->
                            </ul>
                        </div>
                        <div class="tab-select-outer">
                            <select id="tab-select">
                                <li><a href="#tab01">Preview</a></li>
                                <li><a href="#tab02">HTML</a></li>

                            </select>
                        </div>

                        <div id="tab01" class="tab-contents">
                            <!-- <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/multiselect/index.html"></iframe> -->
                            <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%"
                                height="100%" src="./snippets/preview/dual-axis-forecasted-line/index.html"></iframe>
                        </div>
                        <div id="tab02" class="tab-contents">

                            <pre class="line-numbers">
<code class="language-markup"> &lt;div id="chartdiv" style="width: 100%; height: 550px"&gt;&lt;/div&gt;
    &lt;div&gt;
        &lt;div style="display: flex; justify-content: end; align-items: center"&gt;
            &lt;button class="zoomBtn" id="3M"&gt;3M&lt;/button&gt;
            &lt;button class="zoomBtn" id="6M"&gt;6M&lt;/button&gt;
            &lt;button class="zoomBtn active" id="1Y"&gt;1Y&lt;/button&gt;
            &lt;button class="zoomBtn" id="2Y"&gt;2Y&lt;/button&gt;
            &lt;button class="zoomBtn" id="Max"&gt;Max&lt;/button&gt;
        &lt;/div&gt;
    &lt;/div&gt;</code>
</pre>

                        </div>
                        <div id="tab03" class="tab-contents">
                            <pre class="line-numbers"><code class="language-css">
button.zoomBtn {
    background: #fff;
    border: 1px solid #000;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
    margin-right: 5px;
}

button.zoomBtn.active {
    background: #00b19c;
    border: 1px solid #00b19c;
    color: #fff;
}
                            </code></pre>
                        </div>
                        <div id="tab04" class="tab-contents">
                            <pre class="line-numbers">
        <code class="language-javascript">

        am5.ready(function () {
            const jsonData = [
                {
                    date: "Aug-2018",
                    Actuals: "2045.5",
                    Unit: "USD per ton",
                },
                {
                    date: "Sep-2018",
                    Actuals: "2023.0",
                    Unit: "USD per ton",
                },
                {
                    date: "Oct-2018",
                    Actuals: "2034.2",
                    Unit: "USD per ton",
                },
                {
                    date: "Nov-2018",
                    Actuals: "1937.8",
                    Unit: "USD per ton",
                },
                {
                    date: "Dec-2018",
                    Actuals: "1931.5",
                    Unit: "USD per ton",
                },
                {
                    date: "Jan-2019",
                    Actuals: "1845.9",
                    Unit: "USD per ton",
                },
                {
                    date: "Feb-2019",
                    Actuals: "1859.6",
                    Unit: "USD per ton",
                },
                {
                    date: "Mar-2019",
                    Actuals: "1872.2",
                    Unit: "USD per ton",
                },
                {
                    date: "Apr-2019",
                    Actuals: "1849.0",
                    Unit: "USD per ton",
                },
                {
                    date: "May-2019",
                    Actuals: "1775.3",
                    Unit: "USD per ton",
                },
                {
                    date: "Jun-2019",
                    Actuals: "1754.1",
                    Unit: "USD per ton",
                },
                {
                    date: "Jul-2019",
                    Actuals: "1792.8",
                    Unit: "USD per ton",
                },
                {
                    date: "Aug-2019",
                    Actuals: "1740.9",
                    Unit: "USD per ton",
                },
                {
                    date: "Sep-2019",
                    Actuals: "1749.6",
                    Unit: "USD per ton",
                },
                {
                    date: "Oct-2019",
                    Actuals: "1718.8",
                    Unit: "USD per ton",
                },
                {
                    date: "Nov-2019",
                    Actuals: "1772.3",
                    Unit: "USD per ton",
                },
                {
                    date: "Dec-2019",
                    Actuals: "1770.4",
                    Unit: "USD per ton",
                },
                {
                    date: "Jan-2020",
                    Actuals: "1771.7",
                    Unit: "USD per ton",
                },
                {
                    date: "Feb-2020",
                    Actuals: "1685.9",
                    Unit: "USD per ton",
                },
                {
                    date: "Mar-2020",
                    Actuals: "1611.1",
                    Unit: "USD per ton",
                },
                {
                    date: "Apr-2020",
                    Actuals: "1457.2",
                    Unit: "USD per ton",
                },
                {
                    date: "May-2020",
                    Actuals: "1459.8",
                    Unit: "USD per ton",
                },
                {
                    date: "Jun-2020",
                    Actuals: "1564.0",
                    Unit: "USD per ton",
                },
                {
                    date: "Jul-2020",
                    Actuals: "1639.4",
                    Unit: "USD per ton",
                },
                {
                    date: "Aug-2020",
                    Actuals: "1733.9",
                    Unit: "USD per ton",
                },
                {
                    date: "Sep-2020",
                    Actuals: "1745.3",
                    Unit: "USD per ton",
                },
                {
                    date: "Oct-2020",
                    Actuals: "1802.8",
                    Unit: "USD per ton",
                },
                {
                    date: "Nov-2020",
                    Actuals: "1932.1",
                    Unit: "USD per ton",
                },
                {
                    date: "Dec-2020",
                    Actuals: "2017.9",
                    Unit: "USD per ton",
                },
                {
                    date: "Jan-2021",
                    Actuals: "2003.8",
                    Unit: "USD per ton",
                },
                {
                    date: "Feb-2021",
                    Actuals: "2079.6",
                    Unit: "USD per ton",
                },
                {
                    date: "Mar-2021",
                    Actuals: "2191.6",
                    Unit: "USD per ton",
                },
                {
                    date: "Apr-2021",
                    Actuals: "2323.8",
                    Unit: "USD per ton",
                },
                {
                    date: "May-2021",
                    Actuals: "2433.8",
                    Unit: "USD per ton",
                },
                {
                    date: "Jun-2021",
                    Actuals: "2439.1",
                    Unit: "USD per ton",
                },
                {
                    date: "Jul-2021",
                    Actuals: "2492.0",
                    Unit: "USD per ton",
                },
                {
                    date: "Aug-2021",
                    Actuals: "2610.6",
                    Unit: "USD per ton",
                },
                {
                    date: "Sep-2021",
                    Actuals: "2839.7",
                    Unit: "USD per ton",
                },
                {
                    date: "Oct-2021",
                    Actuals: "2955.2",
                    Unit: "USD per ton",
                },
                {
                    date: "Nov-2021",
                    Actuals: "2641.4",
                    Unit: "USD per ton",
                },
                {
                    date: "Dec-2021",
                    Actuals: "2695.4",
                    Unit: "USD per ton",
                },
                {
                    date: "Jan-2022",
                    Actuals: "3003.1",
                    Unit: "USD per ton",
                },
                {
                    date: "Feb-2022",
                    Actuals: "3260.8",
                    Unit: "USD per ton",
                },
                {
                    date: "Mar-2022",
                    Actuals: "3537.9",
                    Unit: "USD per ton",
                },
                {
                    date: "Apr-2022",
                    Actuals: "3256.6",
                    Unit: "USD per ton",
                },
                {
                    date: "May-2022",
                    Actuals: "2826.4",
                    Unit: "USD per ton",
                },
                {
                    date: "Jun-2022",
                    Actuals: "2562.9",
                    Unit: "USD per ton",
                },
                {
                    date: "Jul-2022",
                    Actuals: "2401.6",
                    Unit: "USD per ton",
                },
                {
                    date: "Aug-2022",
                    Actuals: "2431.3",
                    Unit: "USD per ton",
                },
                {
                    date: "Sep-2022",
                    Actuals: "2281.1",
                    Unit: "USD per ton",
                },
                {
                    date: "Oct-2022",
                    Actuals: "2243.3",
                    Unit: "USD per ton",
                },
                {
                    date: "Nov-2022",
                    Actuals: "2248.6",
                    Unit: "USD per ton",
                },
                {
                    date: "Dec-2022",
                    Actuals: "2394.9",
                    Unit: "USD per ton",
                },
                {
                    date: "Jan-2023",
                    Actuals: "2489.0",
                    Unit: "USD per ton",
                },
                {
                    date: "Feb-2023",
                    Actuals: "2417.3",
                    Unit: "USD per ton",
                },
                {
                    date: "Mar-2023",
                    Actuals: "2290.1",
                    Unit: "USD per ton",
                },
                {
                    date: "Apr-2023",
                    Actuals: "2341.0",
                    Unit: "USD per ton",
                },
                {
                    date: "May-2023",
                    Actuals: "2267.6",
                    Unit: "USD per ton",
                },
                {
                    date: "Jun-2023",
                    Actuals: "2181.1",
                    Unit: "USD per ton",
                },
                {
                    date: "Jul-2023",
                    Actuals: "2152.4",
                    Unit: "USD per ton",
                },
                {
                    date: "Aug-2023",
                    Actuals: "2134.2",
                    Unit: "USD per ton",
                },
                {
                    date: "Sep-2023",
                    Actuals: "2162.8",
                    Forecasts: "2162.8",
                    Unit: "USD per ton",
                },
                {
                    date: "Oct-2023",
                    Forecasts: "2192.1",
                    Unit: "USD per ton",
                },
                {
                    date: "Nov-2023",
                    Forecasts: "2163.2",
                    Unit: "USD per ton",
                },
                {
                    date: "Dec-2023",
                    Forecasts: "2200.5",
                    Unit: "USD per ton",
                },
                {
                    date: "Jan-2024",
                    Forecasts: "2243.2",
                    Unit: "USD per ton",
                },
                {
                    date: "Feb-2024",
                    Forecasts: "2291.9",
                    Unit: "USD per ton",
                },
                {
                    date: "Mar-2024",
                    Forecasts: "2325.0",
                    Unit: "USD per ton",
                },
                {
                    date: "Apr-2024",
                    Forecasts: "2304.8",
                    Unit: "USD per ton",
                },
                {
                    date: "May-2024",
                    Forecasts: "2225.0",
                    Unit: "USD per ton",
                },
                {
                    date: "Jun-2024",
                    Forecasts: "2268.7",
                    Unit: "USD per ton",
                },
                {
                    date: "Jul-2024",
                    Forecasts: "2335.5",
                    Unit: "USD per ton",
                },
                {
                    date: "Aug-2024",
                    Forecasts: "2382.8",
                    Unit: "USD per ton",
                },
                {
                    date: "Sep-2024",
                    Forecasts: "2335.2",
                    Unit: "USD per ton",
                },
            ];
            const jsonData1 = [
                {
                    date: "Aug-2018",
                    Actuals: 482.1,
                },
                {
                    date: "Sep-2018",
                    Actuals: 478.1,
                },
                {
                    date: "Oct-2018",
                    Actuals: 446.5,
                },
                {
                    date: "Nov-2018",
                    Actuals: 441.3,
                },
                {
                    date: "Dec-2018",
                    Actuals: 426.5,
                },
                {
                    date: "Jan-2019",
                    Actuals: 417.6,
                },
                {
                    date: "Feb-2019",
                    Actuals: 414.6,
                },
                {
                    date: "Mar-2019",
                    Actuals: 401.3,
                },
                {
                    date: "Apr-2019",
                    Actuals: 409.9,
                },
                {
                    date: "May-2019",
                    Actuals: 445.8,
                },
                {
                    date: "Jun-2019",
                    Actuals: 413.7,
                },
                {
                    date: "Jul-2019",
                    Actuals: 359.8,
                },
                {
                    date: "Aug-2019",
                    Actuals: 356.7,
                },
                {
                    date: "Sep-2019",
                    Actuals: 365.8,
                },
                {
                    date: "Oct-2019",
                    Actuals: 369.6,
                },
                {
                    date: "Nov-2019",
                    Actuals: 353.6,
                },
                {
                    date: "Dec-2019",
                    Actuals: 347.5,
                },
                {
                    date: "Jan-2020",
                    Actuals: 354.4,
                },
                {
                    date: "Feb-2020",
                    Actuals: 366.6,
                },
                {
                    date: "Mar-2020",
                    Actuals: 334,
                },
                {
                    date: "Apr-2020",
                    Actuals: 312.4,
                },
                {
                    date: "May-2020",
                    Actuals: 311.3,
                },
                {
                    date: "Jun-2020",
                    Actuals: 321.3,
                },
                {
                    date: "Jul-2020",
                    Actuals: 340.5,
                },
                {
                    date: "Aug-2020",
                    Actuals: 340.5,
                },
                {
                    date: "Sep-2020",
                    Actuals: 342.6,
                },
                {
                    date: "Oct-2020",
                    Actuals: 339.7,
                },
                {
                    date: "Nov-2020",
                    Actuals: 345.5,
                },
                {
                    date: "Dec-2020",
                    Actuals: 357.6,
                },
                {
                    date: "Jan-2021",
                    Actuals: 371.4,
                },
                {
                    date: "Feb-2021",
                    Actuals: 363,
                },
                {
                    date: "Mar-2021",
                    Actuals: 359.2,
                },
                {
                    date: "Apr-2021",
                    Actuals: 360.5,
                },
                {
                    date: "May-2021",
                    Actuals: 383.7,
                },
                {
                    date: "Jun-2021",
                    Actuals: 386.8,
                },
                {
                    date: "Jul-2021",
                    Actuals: 394.3,
                },
                {
                    date: "Aug-2021",
                    Actuals: 447.4,
                },
                {
                    date: "Sep-2021",
                    Actuals: 591.8,
                },
                {
                    date: "Oct-2021",
                    Actuals: 642.6,
                },
                {
                    date: "Nov-2021",
                    Actuals: 532.7,
                },
                {
                    date: "Dec-2021",
                    Actuals: 442.5,
                },
                {
                    date: "Jan-2022",
                    Actuals: 494.5,
                },
                {
                    date: "Feb-2022",
                    Actuals: 497.2,
                },
                {
                    date: "Mar-2022",
                    Actuals: 469.4,
                },
                {
                    date: "Apr-2022",
                    Actuals: 468.1,
                },
                {
                    date: "May-2022",
                    Actuals: 446,
                },
                {
                    date: "Jun-2022",
                    Actuals: 441.7,
                },
                {
                    date: "Jul-2022",
                    Actuals: 441,
                },
                {
                    date: "Aug-2022",
                    Actuals: 433.7,
                },
                {
                    date: "Sep-2022",
                    Actuals: 406.1,
                },
                {
                    date: "Oct-2022",
                    Actuals: 388.2,
                },
                {
                    date: "Nov-2022",
                    Actuals: 387.9,
                },
                {
                    date: "Dec-2022",
                    Actuals: 413.6,
                },
                {
                    date: "Jan-2023",
                    Actuals: 432.3,
                },
                {
                    date: "Feb-2023",
                    Actuals: 431,
                },
                {
                    date: "Mar-2023",
                    Actuals: 426.2,
                },
                {
                    date: "Apr-2023",
                    Actuals: 422.4,
                },
                {
                    date: "May-2023",
                    Actuals: 411.5,
                },
                {
                    date: "Jun-2023",
                    Actuals: 395,
                },
                {
                    date: "Jul-2023",
                    Actuals: 395.1,
                },
                {
                    date: "Aug-2023",
                    Actuals: 400.2,
                    Forecasts: 400.2,
                },
                {
                    date: "Sep-2023",
                    Forecasts: 410,
                },
                {
                    date: "Oct-2023",
                    Forecasts: 417.2,
                },
                {
                    date: "Nov-2023",
                    Forecasts: 412.8,
                },
                {
                    date: "Dec-2023",
                    Forecasts: 404.7,
                },
                {
                    date: "Jan-2024",
                    Forecasts: 419.6,
                },
                {
                    date: "Feb-2024",
                    Forecasts: 411.9,
                },
                {
                    date: "Mar-2024",
                    Forecasts: 406.9,
                },
                {
                    date: "Apr-2024",
                    Forecasts: 414.1,
                },
                {
                    date: "May-2024",
                    Forecasts: 410,
                },
                {
                    date: "Jun-2024",
                    Forecasts: 404.9,
                },
                {
                    date: "Jul-2024",
                    Forecasts: 410.6,
                },
                {
                    date: "Aug-2024",
                    Forecasts: 436.1,
                },
                {
                    date: "Sep-2024",
                    Forecasts: 454.2,
                },
            ];
            var root = am5.Root.new("chartdiv");

            // Set themes
            root.setThemes([am5themes_Animated.new(root)]);

            root.dateFormatter.setAll({
                dateFormat: "MMM-yyyy",
                dateFields: ["date"],
            });

            // Create chart
            var chart = root.container.children.push(
                am5xy.XYChart.new(root, {
                    focusable: true,
                    panX: true,
                    panY: true,
                    wheelX: "panX",
                    wheelY: "zoomX",
                    pinchZoomX: true,
                    maxTooltipDistance: -1,
                    layout: root.verticalLayout,
                })
            );
            chart.children.unshift(
                am5.Label.new(root, {
                    text: "Aluminium cash-settlement(LME)",
                    fontSize: 12,
                    fontWeight: "bold",
                    textAlign: "center",
                    x: am5.percent(50),
                    centerX: am5.percent(50),
                })
            );

            // Create axes
            var xAxis = chart.xAxes.push(
                am5xy.DateAxis.new(root, {
                    zoomX: false,
                    start: 0,
                    baseInterval: { timeUnit: "month", count: 1 },
                    gridIntervals: [
                        // { timeUnit: "day", count: 1 },
                        { timeUnit: "month", count: 1 },
                    ],
                    dataGroup: true,
                    minGridDistance: 10,
                    keepSelection: true,
                    tooltipDateFormat: "MMM-yyyy",

                    renderer: am5xy.AxisRendererX.new(root, {
                        strokeOpacity: 1,
                        strokeWidth: 1,
                    }),

                    tooltip: am5.Tooltip.new(root, {}),
                })
            );

            xAxis.get("dateFormats")["month"] = "MMM-yyyy";
            xAxis.get("periodChangeDateFormats")["month"] = "MMM-yyyy";

            var xRenderer = xAxis.get("renderer");
            xRenderer.labels.template.setAll({
                rotation: -60,
                centerY: am5.p50,
            });
            xRenderer.grid.template.setAll({
                stroke: am5.color(0xfff),
                strokeWidth: 0,
                strokeOpacity: 0,
            });
            xRenderer.labels.template.setAll({
                fontSize: "12px",
            });

            var yAxis1 = chart.yAxes.push(
                am5xy.ValueAxis.new(root, {
                    zoomY: false,
                    renderer: am5xy.AxisRendererY.new(root, {
                        strokeOpacity: 1,
                        strokeWidth: 1,
                    }),
                    tooltip: am5.Tooltip.new(root, {}),
                })
            );
            var yRenderer1 = yAxis1.get("renderer");
            yRenderer1.grid.template.setAll({
                stroke: am5.color(0xfff),
                strokeWidth: 0,
                strokeOpacity: 0,
            });
            yRenderer1.labels.template.setAll({
                fontSize: "12px",
            });
            yAxis1.children.unshift(
                am5.Label.new(root, {
                    text: "USD per ton",
                    textAlign: "center",
                    y: am5.p50,
                    rotation: -90,
                    fontWeight: "bold",
                    fontSize: 12,
                })
            );

            var yAxis2 = chart.yAxes.push(
                am5xy.ValueAxis.new(root, {
                    zoomY: false,
                    // syncWithAxis: yAxis1,
                    min: 0,
                    // max: 700,
                    strictMinMax: true,
                    renderer: am5xy.AxisRendererY.new(root, {
                        opposite: true,
                        strokeOpacity: 1,
                        strokeWidth: 1,
                    }),
                    tooltip: am5.Tooltip.new(root, {}),
                })
            );
            var yRenderer2 = yAxis2.get("renderer");
            yRenderer2.grid.template.setAll({
                stroke: am5.color(0xfff),
                strokeWidth: 0,
                strokeOpacity: 0,
            });
            yRenderer2.labels.template.setAll({
                fontSize: "12px",
            });
            yAxis2.children.push(
                am5.Label.new(root, {
                    text: "USD per ton",
                    textAlign: "center",
                    y: am5.p50,
                    rotation: -90,
                    fontWeight: "bold",
                    fontSize: 12,
                })
            );

            let actualsSeries = chart.series.push(
                am5xy.LineSeries.new(root, {
                    name: "Actuals",
                    xAxis: xAxis,
                    yAxis: yAxis1,
                    valueYField: "Actuals",
                    valueXField: "date",
                    fill: am5.color("#00b19c"),
                    stroke: am5.color("#00b19c"),
                    strokeWidth: 2,
                    minBulletDistance: 30,
                })
            );
            var tooltip = am5.Tooltip.new(root, {
                labelText: "[bold]{name}[/]: {valueY}",
            });
            tooltip.get("background").setAll({
                cornerRadius: 0,
                stroke: am5.color("#00b19c"),
                fillOpacity: 0.8,
            });
            actualsSeries.set("tooltip", tooltip);

            actualsSeries.bullets.push(function () {
                return am5.Bullet.new(root, {
                    sprite: am5.Circle.new(root, {
                        radius: 5,
                        fill: actualsSeries.get("fill"),
                        stroke: root.interfaceColors.get("background"),
                        strokeWidth: 2,
                        tooltip: am5.Tooltip.new(root, {}),
                    }),
                });
            });
            actualsSeries.data.processor = am5.DataProcessor.new(root, {
                numericFields: ["Actuals", "Forecasts"],
                dateFields: ["date"],
                dateFormat: "MMM-yyyy",
                emptyAs: 0,
            });

            let actualsSeries1 = chart.series.push(
                am5xy.LineSeries.new(root, {
                    name: "China Alumina - Forecasts",
                    xAxis: xAxis,
                    yAxis: yAxis2,
                    valueYField: "Actuals",
                    valueXField: "date",
                    fill: am5.color("#007365"),
                    stroke: am5.color("#007365"),
                    strokeWidth: 2,
                    minBulletDistance: 30,
                })
            );
            var tooltip1 = am5.Tooltip.new(root, {
                labelText: "[bold]{name}[/]: {valueY}",
            });
            tooltip1.get("background").setAll({
                cornerRadius: 0,
                stroke: am5.color("#007365"),
                fillOpacity: 0.8,
            });
            actualsSeries1.set("tooltip", tooltip1);

            actualsSeries1.bullets.push(function () {
                return am5.Bullet.new(root, {
                    sprite: am5.Circle.new(root, {
                        radius: 5,
                        fill: actualsSeries1.get("fill"),
                        stroke: root.interfaceColors.get("background"),
                        strokeWidth: 2,

                        // tooltipText: "{valueY}",

                        tooltip: am5.Tooltip.new(root, {}),
                    }),
                });
            });
            actualsSeries1.data.processor = am5.DataProcessor.new(root, {
                numericFields: ["Actuals", "Forecasts"],
                dateFields: ["date"],
                dateFormat: "MMM-yyyy",
                emptyAs: 0,
            });

            // Create a series for Forecasts
            let forecastsSeries = chart.series.push(
                am5xy.LineSeries.new(root, {
                    name: "Forecasts",
                    xAxis: xAxis,
                    yAxis: yAxis1,
                    valueYField: "Forecasts",
                    valueXField: "date",
                    fill: am5.color("#3bcd3f"),
                    stroke: am5.color("#3bcd3f"),
                    minBulletDistance: 30,

                    tooltip: am5.Tooltip.new(root, {
                        labelText: "[bold]{name}[/]: {valueY}",
                    }),
                })
            );
            var tooltip2 = am5.Tooltip.new(root, {
                labelText: "[bold]{name}[/]: {valueY}",
            });
            tooltip2.get("background").setAll({
                cornerRadius: 0,
                stroke: am5.color("#3bcd3f"),
                fillOpacity: 0.8,
            });
            forecastsSeries.set("tooltip", tooltip2);

            forecastsSeries.strokes.template.setAll({
                strokeWidth: 2,
                strokeDasharray: [5, 5],
            });
            forecastsSeries.data.processor = am5.DataProcessor.new(root, {
                numericFields: ["Actuals", "Forecasts"],
                dateFields: ["date"],
                dateFormat: "MMM-yyyy",
                emptyAs: 0,
            });
            forecastsSeries.bullets.push(function () {
                return am5.Bullet.new(root, {
                    sprite: am5.Circle.new(root, {
                        radius: 5,
                        fill: forecastsSeries.get("fill"),
                        stroke: root.interfaceColors.get("background"),
                        strokeWidth: 2,

                        // tooltipText: "{valueY}",

                        tooltip: am5.Tooltip.new(root, {}),
                    }),
                });
            });

            let forecastsSeries1 = chart.series.push(
                am5xy.LineSeries.new(root, {
                    name: "China Alumina - Forecasts",
                    xAxis: xAxis,
                    yAxis: yAxis2,
                    valueYField: "Forecasts",
                    valueXField: "date",
                    fill: am5.color("#8dbac4"),
                    stroke: am5.color("#8dbac4"),
                    minBulletDistance: 30,

                    tooltip: am5.Tooltip.new(root, {
                        labelText: "[bold]{name}[/]: {valueY}",
                    }),
                })
            );
            var tooltip3 = am5.Tooltip.new(root, {
                labelText: "[bold]{name}[/]: {valueY}",
            });
            tooltip3.get("background").setAll({
                cornerRadius: 0,
                stroke: am5.color("#8dbac4"),
                fillOpacity: 0.8,
            });
            forecastsSeries1.set("tooltip", tooltip3);

            forecastsSeries1.strokes.template.setAll({
                strokeWidth: 2,
                strokeDasharray: [5, 5],
            });
            forecastsSeries1.data.processor = am5.DataProcessor.new(root, {
                numericFields: ["Actuals", "Forecasts"],
                dateFields: ["date"],
                dateFormat: "MMM-yyyy",
                emptyAs: 0,
            });
            forecastsSeries1.bullets.push(function () {
                return am5.Bullet.new(root, {
                    sprite: am5.Circle.new(root, {
                        radius: 5,
                        fill: forecastsSeries1.get("fill"),
                        stroke: root.interfaceColors.get("background"),
                        strokeWidth: 2,

                        // tooltipText: "{valueY}",

                        tooltip: am5.Tooltip.new(root, {}),
                    }),
                });
            });

            chart.set("cursor", am5xy.XYCursor.new(root, {}));

            actualsSeries.data.setAll(jsonData);
            forecastsSeries.data.setAll(jsonData);
            actualsSeries1.data.setAll(jsonData1);
            forecastsSeries1.data.setAll(jsonData1);

            // Add legend
            var legend = chart.children.push(
                am5.Legend.new(root, { x: am5.percent(50), centerX: am5.percent(50) })
            );
            legend.data.push(actualsSeries);
            legend.data.push(forecastsSeries);
            legend.data.push(actualsSeries1);
            legend.data.push(forecastsSeries1);
            legend.labels.template.setAll({
                fontSize: 12,
                // other settings...
            });

            chart.zoomOutButton.set("forceHidden", true);

            document.getElementById("3M").addEventListener("click", function () {
                zoomChart("3M");
                this.classList.add("active");
                document.getElementById("6M").classList.remove("active");
                document.getElementById("1Y").classList.remove("active");
                document.getElementById("2Y").classList.remove("active");
                document.getElementById("Max").classList.remove("active");
            });

            document.getElementById("6M").addEventListener("click", function () {
                zoomChart("6M");
                this.classList.add("active");
                document.getElementById("3M").classList.remove("active");
                document.getElementById("1Y").classList.remove("active");
                document.getElementById("2Y").classList.remove("active");
                document.getElementById("Max").classList.remove("active");
            });

            document.getElementById("1Y").addEventListener("click", function () {
                zoomChart("1Y");
                this.classList.add("active");
                document.getElementById("3M").classList.remove("active");
                document.getElementById("6M").classList.remove("active");
                document.getElementById("2Y").classList.remove("active");
                document.getElementById("Max").classList.remove("active");
            });

            document.getElementById("2Y").addEventListener("click", function () {
                zoomChart("2Y");
                this.classList.add("active");
                document.getElementById("3M").classList.remove("active");
                document.getElementById("6M").classList.remove("active");
                document.getElementById("1Y").classList.remove("active");
                document.getElementById("Max").classList.remove("active");
            });

            document.getElementById("Max").addEventListener("click", function () {
                zoomChart("Max");
                this.classList.add("active");
                document.getElementById("3M").classList.remove("active");
                document.getElementById("6M").classList.remove("active");
                document.getElementById("1Y").classList.remove("active");
                document.getElementById("2Y").classList.remove("active");
            });
            let dates = [...jsonData, ...jsonData1].map(function (e) {
                return new Date(e.date);
            });
            let minDate = new Date(Math.min.apply(null, dates));
            let maxDate = new Date(Math.max.apply(null, dates));
            maxDate.setMonth(maxDate.getMonth() + 1);

            function zoomChart(range) {
                let startDate;
                let endDate;

                switch (range) {
                    case "3M":
                        startDate = new Date();
                        endDate = new Date();
                        startDate.setMonth(startDate.getMonth() - 3);
                        break;
                    case "6M":
                        startDate = new Date();
                        endDate = new Date();
                        startDate.setMonth(startDate.getMonth() - 6);
                        break;
                    case "1Y":
                        startDate = new Date();
                        endDate = new Date();
                        startDate.setFullYear(startDate.getFullYear() - 1);
                        break;
                    case "2Y":
                        startDate = new Date();
                        endDate = new Date();
                        startDate.setFullYear(startDate.getFullYear() - 2);
                        break;
                    case "Max":
                        startDate = minDate;
                        endDate = maxDate;

                        break;
                    default:
                        console.error("Invalid range");
                        return;
                }

                xAxis.zoomToDates(startDate, endDate);
            }
            setTimeout(function () {
                zoomChart("1Y");
            }, 200);
        });

</code>
          </pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>






    </main>

    <footer class="text-muted">
        <div class="container">
            <p class="float-right">
                <a href="#">Back to top</a>
            </p>


        </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="../../../js/select2.min.js"></script>
    <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <script src="./js/iframe.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
        $(function () {
            var $tabButtonItem = $('#tab-button li'),
                $tabSelect = $('#tab-select'),
                $tabContents = $('.tab-contents'),
                activeClass = 'is-active';

            $tabButtonItem.first().addClass(activeClass);
            $tabContents.not(':first').hide();

            $tabButtonItem.find('a').on('click', function (e) {
                var target = $(this).attr('href');

                $tabButtonItem.removeClass(activeClass);
                $(this).parent().addClass(activeClass);
                $tabSelect.val(target);
                $tabContents.hide();
                $(target).show();
                e.preventDefault();
            });

            $tabSelect.on('change', function () {
                var target = $(this).val(),
                    targetSelectNum = $(this).prop('selectedIndex');

                $tabButtonItem.removeClass(activeClass);
                $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
                $tabContents.hide();
                $(target).show();
            });
        });
    </script>
</body>

</html>