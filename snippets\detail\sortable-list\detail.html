
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Sortable listing</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Sortable list</h4>
              <div class="mb-2">
                <a
                  href="./downloads/sortable-list.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <!-- <li><a href="#tab03">CSS</a></li> -->
                <!-- <li><a href="#tab04">Javascript</a></li> -->
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/sortable-list/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
<pre class="line-numbers">
<code class="language-markup">&lt;div class="container mt-3"&gt;
    &lt;div class="row"&gt;
        &lt;div class="col-sm-12"&gt;
         &lt;h1&gt;Drag & drop sortable list&lt;/h1&gt;
         &lt;p&gt;Drag and drop to reorder. Keyboard and text-based: change the number input and hit enter or the update button.&lt;/p&gt;
         &lt;form id="sort-it"&gt;
             &lt;ol&gt;
               &lt;li&gt;This is item #1 
                   &lt;label for="custom-number-1"&gt;New order:&lt;/label&gt;
                 &lt;input id="custom-number-1" name="custom-number-1" type="number" min="1"&gt;
               &lt;/li&gt;
                 
               &lt;li&gt;This is item #2 
                 &lt;label for="custom-number-2"&gt;New order:&lt;/label&gt;
                 &lt;input id="custom-number-2" name="custom-number-2" type="number" min="1"&gt;
               &lt;/li&gt;
                 
               &lt;li&gt;This is item #3 
                 &lt;label for="custom-number-3"&gt;New order:&lt;/label&gt;
                 &lt;input id="custom-number-3" name="custom-number-3" type="number" min="1"&gt;
               &lt;/li&gt;
                 
               &lt;li&gt;This is item #4 
                 &lt;label for="custom-number-4"&gt;New order:&lt;/label&gt;
                 &lt;input id="custom-number-4" name="custom-number-4" type="number" min="1"&gt;
               &lt;/li&gt;
                 
               &lt;li&gt;This is item #5 
                 &lt;label for="custom-number-5"&gt;New order:&lt;/label&gt;
                 &lt;input id="custom-number-5" name="custom-number-5" type="number" min="1"&gt;
               &lt;/li&gt;
             &lt;/ol&gt;
               &lt;input type="submit" class="btn btn-primary btn-sm" id="manual-sort" name="manual-sort" value="Update"&gt;
         &lt;/form&gt;
        &lt;/div&gt;
    &lt;/div&gt;
 
 &lt;/div&gt;</code>  
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">body {font-family: sans-serif;}

#sort-it input { font-size: 1em; } /* prevent zoom in mobile */

#sort-it ol { 
    /* list style is faked with number inputs */
    list-style: none;
    padding: 0;
}

#sort-it li {
    position: relative;
    min-height: 1em;
    cursor: move;
    padding: 0.5em 0.5em 0.5em 2.5em;
    background: #545b6224;
    border: 1px solid #ccc;
    margin: 0.25em 0;
    border-radius: 0;
    max-width: 54em;
}
    
#sort-it li input { 
    /* Move these to visually fake the ol numbers */
    position: absolute;
    width: 1.75em;
    left: .25em;
    top: .25em;
    border: 0;
    text-align: center;
    background: transparent
}
    
#sort-it li label { 
    /* visually hidden offscreen so it still benefits screen readers */
    position: absolute;
    left: -9999px;
}

/* sortable plugin styles when dragged */
#sort-it .dragged {
    position: absolute;
    opacity: 0.5;
    z-index: 2000;
}

#sort-it  li.placeholder {
    position: relative;
    background: #ccc; 
}
.btn-primary {
    border: 2px solid #007266;
    background: #007266;
    border-radius: 0;
}
.btn-primary:focus,.btn-primary:hover{
    border: 2px solid #007266;
    background: #007266;
}
.btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active, .show>.btn-primary.dropdown-toggle {
    color: #fff;
    border: 2px solid #007266;
    background: #007266;
}

              </code></pre>
            </div>
            <div id="tab04" class="tab-contents">
<pre class="line-numbers"><code class="language-javascript">$(function(){
    $('#sort-it ol').sortable({
        onDrop: function(item) {
            $(item).removeClass("dragged").removeAttr("style");
            $("body").removeClass("dragging");

            getInitialOrder('#sort-it li');
        }
    });
    
    getInitialOrder('#sort-it li');
    
//bind stuff to number inputs
    $('#sort-it ol input[type="number"]').focus(function(){
        $(this).select();	
    }).change(function(){
        updateAllNumbers($(this), '#sort-it input');
    }).keyup(function(){
        updateAllNumbers($(this), '#sort-it input');
    });

//bind to form submission
$('#sort-it').submit(function(e){
    reorderItems('#sort-it li', '#sort-it ol');
    e.preventDefault();
})

}); // end doc ready

function getInitialOrder(obj){
    var num = 1;
    $(obj).each(function(){
    //set object initial order data based on order in DOM
        $(this).find('input[type="number"]').val(num).attr('data-initial-value', num); 
        num++;
    });
    $(obj).find('input[type="number"]').attr('max', $(obj).length); //give it an html5 max attr based on num of objects
}

function updateAllNumbers(currObj, targets){
    var delta = currObj.val() - currObj.attr('data-initial-value'), //if positive, the object went down in order. If negative, it went up.
            c = parseInt(currObj.val(), 10), //value just entered by user
            cI = parseInt(currObj.attr('data-initial-value'), 10), //original object val before change
            top = $(targets).length;
    
    //if the user enters a number too high or low, cap it
    if(c > top){
        currObj.val(top);
    }else if(c < 1){
        currObj.val(1);
    }
    
    $(targets).not($(currObj)).each(function(){ //change all the other objects
        var v = parseInt($(this).val(), 10); //value of object changed		
            
        if (v >= c && v < cI && delta < 0){ //object going up in order pushes same-numbered and in-between objects down
            $(this).val(v + 1);
        } else if (v <= c && v > cI && delta > 0){ //object going down in order pushes same-numbered and in-between objects up
            $(this).val(v - 1);
        }
    }).promise().done(function(){
        //after all the fields update based on new val, set their data element so further changes can be tracked 
        //(but ignore if no value given yet)
        $(targets).each(function(){
            if($(this).val() !== ""){
                $(this).attr('data-initial-value', $(this).val());
            }
        });
    });
}

function reorderItems(things, parent){
for(var i = 1; i <= $(things).length; i++){
$(things).each(function(){
    var x = parseInt($(this).find('input').val(), 10);
    if(x === i){
    $(this).appendTo(parent);
    }
});
}
}

            </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
