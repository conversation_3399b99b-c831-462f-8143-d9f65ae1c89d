
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    .close-menu-icon {
        position: relative;
        top: 10px;
    }
    .hamSection {
        top: 67px;padding-top: 0}
.close-menu-icon{top: 5px}
    


    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
          <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
          <i class="fas fa-bars"></i>
        </span>
      </div>

<main role="main">


  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div id="rightSection" class="col-sm-10">
        <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
                <div>
                    <h4 class="size20 mb-2 pt-2">Document List</h4>                   
                </div>
              </div>   
        </div>
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
            <li><a href="#tab03">CSS</a></li>
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/document-list/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
&lt;link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css" rel="stylesheet"&gt;
&lt;div class="container"&gt;
            &lt;div class="row"&gt;
                &lt;div class="col-sm-4"&gt;
                    &lt;div class="panel panel-dark-outline tabs-panel"&gt;
                        &lt;div class="panel-heading"&gt;
                            &lt;ul class="nav nav-tabs pull-left type-document"&gt;
                                &lt;li class="active"&gt;&lt;a data-toggle="tab" href=".documents-panel" aria-expanded="true"&gt; &lt;i class="fa fa-file"&gt;&lt;/i&gt; Documents&lt;/a&gt;&lt;/li&gt;
                                &lt;li class=""&gt;&lt;a data-toggle="tab" href=".images-panel" aria-expanded="false"&gt;&lt;i class="fa fa-file-image-o"&gt;&lt;/i&gt; Images &lt;/a&gt;&lt;/li&gt;
                            &lt;/ul&gt;
                            &lt;div class="clear"&gt;&lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="panel-body tab-content"&gt;
                            &lt;div class="tab-pane active documents-panel"&gt;
                                &lt;a class="label label-success  label-left" href="#"&gt;Excel&lt;/a&gt;
                                &lt;a class="label label-info label-left" href="#"&gt;Word&lt;/a&gt; 
                                &lt;a class="label label-warning label-left" href="#"&gt;Powerpoint&lt;/a&gt;
                                &lt;a class="label label-danger label-left" href="#"&gt;PDF&lt;/a&gt;
                                &lt;a class="label label-dark label-left" href="#"&gt;Video&lt;/a&gt;
                                &lt;div class="clear"&gt;&lt;/div&gt;
                                &lt;div class="v-spacing-xs"&gt;&lt;/div&gt;
                                &lt;h4 class="no-margin-top"&gt; Folders&lt;/h4&gt;
                                &lt;ul class="folders list-unstyled"&gt;
                                    &lt;li&gt; 
                                        &lt;a href="#"&gt;
                                            &lt;i class="fa fa-folder"&gt;&lt;/i&gt; Web projects
                                        &lt;/a&gt;
                                    &lt;/li&gt;
                                    &lt;li&gt; 
                                        &lt;a href="#"&gt;
                                            &lt;i class="fa fa-folder"&gt;&lt;/i&gt; Presentation files
                                        &lt;/a&gt;
                                    &lt;/li&gt;
                                    &lt;li&gt; 
                                        &lt;a href="#"&gt;
                                            &lt;i class="fa fa-folder"&gt;&lt;/i&gt; Books
                                        &lt;/a&gt;
                                    &lt;/li&gt;
                                    &lt;li&gt; 
                                        &lt;a href="#"&gt;
                                            &lt;i class="fa fa-folder"&gt;&lt;/i&gt; Contest
                                        &lt;/a&gt;
                                    &lt;/li&gt;
                                    &lt;li&gt; 
                                        &lt;a href="#"&gt;
                                            &lt;i class="fa fa-folder"&gt;&lt;/i&gt; Our Projects
                                        &lt;/a&gt;
                                    &lt;/li&gt;
                                    &lt;li&gt; 
                                        &lt;a href="#"&gt;
                                            &lt;i class="fa fa-folder"&gt;&lt;/i&gt; Our Music
                                        &lt;/a&gt;
                                    &lt;/li&gt;
                                    &lt;li&gt; 
                                        &lt;a href="#"&gt;
                                            &lt;i class="fa fa-folder"&gt;&lt;/i&gt; Messenger sounds
                                        &lt;/a&gt;
                                    &lt;/li&gt;
                                &lt;/ul&gt;
                                &lt;div class="v-spacing-xs"&gt;&lt;/div&gt;
                                &lt;a class="btn btn-block btn-success"&gt; &lt;i class="fa fa-plus"&gt; &lt;/i&gt; Upload&lt;/a&gt;
                            &lt;/div&gt;
                            &lt;div class="tab-pane images-panel"&gt;
                                &lt;h4 class="no-margin-top"&gt; Folders&lt;/h4&gt;
                                &lt;ul class="folders list-unstyled"&gt;
                                    &lt;li&gt; 
                                        &lt;a href="#"&gt;
                                            &lt;i class="fa fa-folder"&gt;&lt;/i&gt; April meeting
                                        &lt;/a&gt;
                                    &lt;/li&gt;
                                    &lt;li&gt; 
                                        &lt;a href="#"&gt;
                                            &lt;i class="fa fa-folder"&gt;&lt;/i&gt; Application presentation
                                        &lt;/a&gt;
                                    &lt;/li&gt;
                                    &lt;li&gt; 
                                        &lt;a href="#"&gt;
                                            &lt;i class="fa fa-folder"&gt;&lt;/i&gt; Staff profile pictures
                                        &lt;/a&gt;
                                    &lt;/li&gt;
                                    &lt;li&gt; 
                                        &lt;a href="#"&gt;
                                            &lt;i class="fa fa-folder"&gt;&lt;/i&gt; Trip to Yosemite
                                        &lt;/a&gt;
                                    &lt;/li&gt;
                                &lt;/ul&gt;
                                &lt;div class="v-spacing-xs"&gt;&lt;/div&gt;
                                &lt;a class="btn btn-block btn-success"&gt; &lt;i class="fa fa-plus"&gt; &lt;/i&gt; Upload&lt;/a&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="col-sm-8 tab-content no-bg no-border"&gt;
                    &lt;div class="tab-pane active documents documents-panel"&gt;
                        &lt;div class="document success"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;i class="fa fa-file-excel-o text-success"&gt;&lt;/i&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; Excel database 2017 &lt;/span&gt;
                                &lt;span class="document-description"&gt; 1.2 MB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="document"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;i class="fa fa-file-excel-o text-success"&gt;&lt;/i&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; Excel database 2016 &lt;/span&gt;
                                &lt;span class="document-description"&gt; 1.1 MB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="document info"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;i class="fa fa-file-word-o text-info"&gt;&lt;/i&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; Word file 2017 &lt;/span&gt;
                                &lt;span class="document-description"&gt; 932 KB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="document"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;i class="fa fa-file-word-o text-info"&gt;&lt;/i&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; Word file 2016 &lt;/span&gt;
                                &lt;span class="document-description"&gt; 426 MB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="document warning"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;i class="fa fa-file-powerpoint-o text-warning"&gt;&lt;/i&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; Presentation 2017 &lt;/span&gt;
                                &lt;span class="document-description"&gt; 2.7 MB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="document"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;i class="fa fa-file-powerpoint-o text-warning"&gt;&lt;/i&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; Presentation 2016 &lt;/span&gt;
                                &lt;span class="document-description"&gt; 1.9 MB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="document danger"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;i class="fa fa-file-pdf-o text-danger"&gt;&lt;/i&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; PDF file 2017 &lt;/span&gt;
                                &lt;span class="document-description"&gt; 5.3 MB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="document"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;i class="fa fa-file-pdf-o text-danger"&gt;&lt;/i&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; PDF file 2016 &lt;/span&gt;
                                &lt;span class="document-description"&gt; 4.4 MB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="document dark"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;i class="fa fa-file-video-o text-dark"&gt;&lt;/i&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name text-dark"&gt; Video file 2017 &lt;/span&gt;
                                &lt;span class="document-description"&gt; 18.2 MB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="document"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;i class="fa fa-file-video-o text-dark"&gt;&lt;/i&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; Video file 2016 &lt;/span&gt;
                                &lt;span class="document-description"&gt; 23.7 MB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="tab-pane documents images-panel"&gt;
                        &lt;div class="document"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;img src="https://picsum.photos/250/250?random=1"&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; Forest.png &lt;/span&gt;
                                &lt;span class="document-description"&gt; 1.2 MB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="document"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;img src="https://picsum.photos/250/250?random=1"&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; Developer.png &lt;/span&gt;
                                &lt;span class="document-description"&gt; 2.5 MB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="document"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;img src="https://picsum.photos/250/250?random=1"&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; Meeting.png &lt;/span&gt;
                                &lt;span class="document-description"&gt; 1.1 MB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="document"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;img src="https://picsum.photos/250/250?random=1"&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; Hiking.png &lt;/span&gt;
                                &lt;span class="document-description"&gt; 3.5 MB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="document"&gt;
                            &lt;div class="document-body"&gt;
                                &lt;img src="https://picsum.photos/250/250?random=1"&gt;
                            &lt;/div&gt;
                            &lt;div class="document-footer"&gt;
                                &lt;span class="document-name"&gt; Developers meeting.png &lt;/span&gt;
                                &lt;span class="document-description"&gt; 862 KB &lt;/span&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt; 
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;    
</code>
</pre>
           
        </div>
        <div id="tab03" class="tab-contents">
          <pre class="line-numbers"><code class="language-css">
body{
    margin-top:20px;
    background:#eee;
}

.type-document{
    width:100%; 
    margin-bottom:20px;
}

.documents {
    padding:20px;    
}

.tabs {
    margin-bottom: 20px;
}

.tabs .nav-tabs {
    border-bottom: 1px solid #dce2e9;
}

.tabs .nav-tabs > li > a {
    font-size: 15px;
    padding: 10px 20px;
}

.tabs .nav-tabs > li.active > a {
    border: 1px solid #dce2e9;
    border-bottom-color: #fff;
}

.tabs-panel .panel-heading {
    padding: 0px 10px 2px 10px;
}

.tabs-panel .panel-heading > .nav.nav-tabs {
    padding: 0;
}

.tabs-panel .panel-heading:after {
    padding-bottom: 3px;
}

.tabs-panel .panel-heading .panel-options {
    margin-top: 10px;
}
.tabs-panel .panel-body {
    padding: 20px 15px 15px 15px;
    font-size: 14px;
}

.tabs-panel .nav-tabs {
    border: none;
}

.tabs-panel .nav-tabs > li {
    float: left;
    margin-bottom: -1px;
}

.tabs-panel .nav-tabs > li > a {
    margin-right: 2px;
    line-height: 1.42857143;
    font-weight: 600;
    font-size: 15px;
    color: #555;
    border: 1px solid transparent;
    border-radius: 4px 4px 0 0;
}

.tabs-panel .nav-tabs > li > a > i {
    font-size: 13px;
    margin-right: 2px;
}

.tabs-panel .nav-tabs > li > a:hover {
    border-color: #eee #eee #ddd;
    background-color: rgba(0,0,0,0);
    border: 1px solid rgba(0,0,0,0);
    color: #000;
}

.tabs-panel .nav-tabs > li.active > a,
.tabs-panel .nav-tabs > li.active > a:hover,
.tabs-panel .nav-tabs > li.active > a:focus {
    color: #4a4d56;
    cursor: default;
    background-color: inherit;
    border: none;
    border-bottom-color: transparent;
}

.tabs-panel .nav-tabs > li.active > a:after,
.tabs-panel .nav-tabs > li.active > a:hover:after,
.tabs-panel .nav-tabs > li.active > a:focus:after {
    display: block;
    position: absolute;
    width: calc(100% - 4px);
    left: 2px;
    content: "";
    padding-bottom: 12px;
    border-bottom: 4px solid #314557;
}

.tabs-panel .tab-content {
    background-color: #fff;
    border: none;
}

.tabs-panel.panel-success .panel-heading:after,
.tabs-panel.panel-info .panel-heading:after,
.tabs-panel.panel-danger .panel-heading:after,
.tabs-panel.panel-warning .panel-heading:after,
.tabs-panel.panel-dark .panel-heading:after {
    display: none;
}

.tabs-panel.panel-success-outline .nav-tabs > li.active > a:after,
.tabs-panel.panel-success-outline .nav-tabs > li.active > a:hover:after,
.tabs-panel.panel-success-outline .nav-tabs > li.active > a:focus:after {
    border-bottom: 4px solid #0ec8a2;
}

.tabs-panel.panel-info-outline .nav-tabs > li.active > a:after,
.tabs-panel.panel-info-outline .nav-tabs > li.active > a:hover:after,
.tabs-panel.panel-info-outline .nav-tabs > li.active > a:focus:after {
    border-bottom: 4px solid #2da9e9;
}

.tabs-panel.panel-warning-outline .nav-tabs > li.active > a:after,
.tabs-panel.panel-warning-outline .nav-tabs > li.active > a:hover:after,
.tabs-panel.panel-warning-outline .nav-tabs > li.active > a:focus:after {
    border-bottom: 4px solid #ff9e2a;
}

.tabs-panel.panel-danger-outline .nav-tabs > li.active > a:after,
.tabs-panel.panel-danger-outline .nav-tabs > li.active > a:hover:after,
.tabs-panel.panel-danger-outline .nav-tabs > li.active > a:focus:after {
    border-bottom: 4px solid #f95858;
}

.tabs-panel.panel-success .panel-heading .nav.nav-tabs > li > a,
.tabs-panel.panel-info .panel-heading .nav.nav-tabs > li > a,
.tabs-panel.panel-danger .panel-heading .nav.nav-tabs > li > a,
.tabs-panel.panel-warning .panel-heading .nav.nav-tabs > li > a,
.tabs-panel.panel-dark .panel-heading .nav.nav-tabs > li > a {
    color: #fff;
}

.tabs-panel.panel-success .panel-heading li.active > a:after,
.tabs-panel.panel-success .panel-heading li.active > a:hover:after,
.tabs-panel.panel-success .panel-heading li.active > a:focus:after {
    display: block;
    margin: 0 auto;
    width: 18px;
    height: 18px;
    background-color: #0ec8a2;
    transform: rotate(45deg);
    border: none;
    margin-top: 4px;
    left:40%;
}

.tabs-panel.panel-info .panel-heading li.active > a:after,
.tabs-panel.panel-info .panel-heading li.active > a:hover:after,
.tabs-panel.panel-info .panel-heading li.active > a:focus:after {
    display: block;
    margin: 0 auto;
    width: 18px;
    height: 18px;
    background-color: #2da9e9;
    transform: rotate(45deg);
    border: none;
    margin-top: 4px;
    left:40%;
}

.tabs-panel.panel-warning .panel-heading li.active > a:after,
.tabs-panel.panel-warning .panel-heading li.active > a:hover:after,
.tabs-panel.panel-warning .panel-heading li.active > a:focus:after {
    display: block;
    margin: 0 auto;
    width: 18px;
    height: 18px;
    background-color: #ff9e2a;
    transform: rotate(45deg);
    border: none;
    margin-top: 4px;
    left:40%;
}

.tabs-panel.panel-danger .panel-heading li.active > a:after,
.tabs-panel.panel-danger .panel-heading li.active > a:hover:after,
.tabs-panel.panel-danger .panel-heading li.active > a:focus:after {
    display: block;
    margin: 0 auto;
    width: 18px;
    height: 18px;
    background-color: #f95858;
    transform: rotate(45deg);
    border: none;
    margin-top: 4px;
    left:40%;
}

.tabs-panel.panel-dark .panel-heading li.active > a:after,
.tabs-panel.panel-dark .panel-heading li.active > a:hover:after,
.tabs-panel.panel-dark .panel-heading li.active > a:focus:after {
    display: block;
    margin: 0 auto;
    width: 18px;
    height: 18px;
    background-color: #314557;
    transform: rotate(45deg);
    border: none;
    margin-top: 4px;
    left:40%;
}

.nav-tabs {
    border-bottom: 1px solid #98a3af;
}

.nav-tabs > li {
    float: left;
    margin-bottom: -1px;
    opacity: 0.5;
}

.nav-tabs > li > a {
    margin-right: 2px;
    line-height: 1.42857143;
    font-weight: 600;
    font-size: 16px;
    color: #555;
    border: 1px solid transparent;
    border-radius: 4px 4px 0 0;
}
.nav-tabs > li > a:hover {
    border-color: #eee #eee #ddd;
    background-color: rgba(0,0,0,0);
    border: 1px solid rgba(0,0,0,0);
    color: #000;
}

.nav-tabs > li.active {
    opacity: 1;
}

.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
    color: #555;
    cursor: default;
    background-color: #fff;
    border: 1px solid #dce2e9;
    border-bottom-color: transparent;
}

.tabs-panel .nav-tabs > li.active > a,
.tabs-panel .nav-tabs > li.active > a:hover,
.tabs-panel .nav-tabs > li.active > a:focus {
    margin-top: -1px;
    border: 1px solid rgba(0,0,0,0) !important;
    border-bottom: none;
}

.tab-content {
    background-color: #fff;
    border: 1px solid #dce2e9;
    border-radius: 4px;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-top: none;
}

.tab-content > .tab-pane {
    display: none;
}

.tab-content > .active {
    display: block;
}

.nav-tabs.nav-justified {
    width: 100%;
    border-bottom: 0;
}

.nav-tabs.nav-justified > li {
    float: none;
}

.nav-tabs.nav-justified > li > a {
    margin-bottom: 5px;
    text-align: center;
}

.nav-tabs.nav-justified > .dropdown .dropdown-menu {
    top: auto;
    left: auto;
}

.nav-tabs.nav-justified > li > a {
    margin-right: 0;
    border-radius: 4px;
}

.nav-tabs.nav-justified > .active > a,
.nav-tabs.nav-justified > .active > a:hover,
.nav-tabs.nav-justified > .active > a:focus {
    border: 1px solid #dce2e9;
}

@media (min-width: 768px) {
    .nav-tabs.nav-justified > li {
    display: table-cell;
    width: 1%;
    }

    .nav-tabs.nav-justified > li > a {
    margin-bottom: 0;
    border-bottom: 1px solid #98a3af;
    border-radius: 4px 4px 0 0;
    }
    .nav-tabs.nav-justified > .active > a,
    .nav-tabs.nav-justified > .active > a:hover,
    .nav-tabs.nav-justified > .active > a:focus {
    border-bottom-color: #fff;
    }
}

@media (max-width: 767px) {
    .nav-justified > li > a {
    border: 1px solid #98a3af;
    }

    .nav-justified + .tab-content {
    border: 1px solid #dce2e9;
    }
}

.document {
    float: left;
    width: calc(33% - 20px);
    max-width: 240px;
    margin: 0px 10px 20px;
    background-color: #fff;
    border-radius: 3px;
    border: 1px solid #dce2e9;
}

.document .document-body {
    height: 130px;
    text-align: center;
    border-radius: 3px 3px 0 0;
    background-color: #fdfdfe;
}

.document .document-body i {
    font-size: 45px;
    line-height: 120px;
}

.document .document-body img {
    width: 100%;
    height: 100%;
}

.document .document-footer {
    border-top: 1px solid #ebf1f5;
    height: 46px;;
    padding: 5px 12px;
    border-radius: 0 0 2px 2px;
}

.document .document-footer .document-name {
    display: block;
    margin-bottom: 0;
    font-size: 15px;
    font-weight: 600;
    width: 100%;
    line-height: normal;
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.document .document-footer .document-description {
    display: block;
    margin-top: -1px;
    font-size: 11px;
    font-weight: 600;
    color: #8998a6;
    width: 100%;
    line-height: normal;
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.document.info .document-footer >*, .document.success .document-footer >*,
.document.danger .document-footer >*, .document.warning .document-footer >*,
.document.dark .document-footer >* {
    color: #fff !important;
}

.document.info .document-footer {
    background-color: #2da9e9;
}

.document.success .document-footer {
    background-color: #0ec8a2;
}

.document.warning .document-footer {
    background-color: #ff9e2a;
}

.document.danger .document-footer {
    background-color: #f95858;
}

.document.dark .document-footer {
    background-color: #314557;
}

.folders {
    width: 100%;
}

.folders li {
    font-size: 14px;
    padding: 3px 4px 3px 12px;
}

.folders li a {
    text-decoration: none;
    color: #4a4d56;
}

.folders li a i {
    color: #5e6168;
    font-size: 16px;
    margin-right: 5px;
}

@media screen and (max-width: 600px) {
    .document  {
    width: 100%;
    margin: 5px 0;
    max-width: none;
    }
}
.v-spacing-xs {
    margin-bottom: 10px;
}

/* Drag and drop */

.dropzone {
    border: 2px dashed #cfdbe2;
    width: 100%;
    height: auto;
    text-align: center;
    border-radius: 5px;
    padding: 5%;
}

.dropzone-image {
    display: block;
    margin: 0 auto 5%;
    width: 100%;
    max-width: 190px;
    height: auto;
    opacity: 0.75;
}

.dropzone a.btn {
    padding: 9px 28px 8px;
}

.dropzone-thin {
    width: 100%;
}

.dropzone-thin .dropzone-image {
    min-width: 35px;
    width: 10%;
    max-width: 80px;
    display: inline-block;
    margin: 0 10px 0 0;
}

.dropzone-thin .dz-message > span {
    display: inline-block;
    vertical-align: middle;
    font-size: 15px;
    font-weight: 600;
}

.dz-preview {
    padding: 10px 0;
    border-bottom: 1px solid #edf2f4;
}

.dz-preview:nth-child(2) {
    margin-top: 30px;
}

.dz-preview:last-child {
    border-bottom: none;
}

.dz-image {
    display: inline-block;
}

.dz-image img {
    width: 50px;
    height: 50px;
    border-radius: 5px;
}

.dz-details {
    display: inline-block;
    width: calc(100% - 60px);
    text-align: left;
    vertical-align: middle;
    padding-left: 20px;
}

.dz-error-message {
    display: none;
}

.dz-success-mark, .dz-error-mark {
    display: none;
}

.dropzone:hover {
    background-color: #fbfdff;
    cursor: pointer;
}

.dropzone:hover .dropzone-image {
    opacity: 1;
}

a.label.label-success.label-left {
    background: #0ec8a2;
    color: #fff;
    padding: 0px 10px;
}
a.label.label-info.label-left {
    background: #2da9e9;
    color: #fff;
    padding: 0 10px;
}
a.label.label-warning.label-left{
    background: #ff9e2a;
    color: #fff;
    padding: 0 10px;
}
a.label.label-danger.label-left{
    background: #f95858;
    color: #fff;
    padding: 0 10px;
}
a.label.label-danger.label-left{
    background: #f95858;
    color: #fff;
    padding: 0 10px;
}
a.label.label-dark.label-left{
    background: #314557;
    color: #fff;
    padding: 0 10px;
}
.btn.btn-block.btn-success{
    color: #fff;
}
ul.nav.nav-tabs.pull-left.type-document {
    justify-content: space-around;
    padding: 10px;
    display: flex;
}
.tabs-panel .nav-tabs > li.active > a:after, .tabs-panel .nav-tabs > li.active > a:hover:after, .tabs-panel .nav-tabs > li.active > a:focus:after {
    display: block;
    position: absolute;
    width: 89%;
    left: 22px;
    content: "";
    padding-bottom: 12px;
    border-bottom: 2px solid #314557;
}
</code></pre>
        </div>
        <!-- <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">

            </code></pre>
        </div> -->
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
