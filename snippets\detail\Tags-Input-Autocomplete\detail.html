
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    


    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>

<main role="main">



  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div class="col-sm-10">
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
           
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
          
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/Tags-Input-Autocomplete/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
  &lt;!doctype html&gt;
  &lt;html lang="en"&gt;
  
  &lt;head&gt;
      &lt;meta charset="utf-8"&gt;
      &lt;meta name="viewport" content="width=device-width, initial-scale=1"&gt;
      &lt;link rel="shortcut icon" href="images/Fav_icon.ico" type="image/vnd.microsoft.icon" id="favicon" /&gt;
  
      &lt;!-- CSRF Token --&gt;
      &lt;meta name="csrf-token" content="Bie7ebTsNyWNtvSVjshwM5jZ0fr36oi1EAnpeSlZ"&gt;
      &lt;title&gt;Login&lt;/title&gt;
      &lt;meta http-equiv="X-UA-Compatible" content="IE=edge" /&gt;
      &lt;link href="https://fonts.googleapis.com/css2?family=Fira+Sans:wght@200;300;400;500;700&display=swap"
          rel="stylesheet"&gt;
      &lt;link href="css/line-awesome.css?v=1.1.3" rel="stylesheet"&gt;
      &lt;link href="css/login.css?v=1.1.3" rel="stylesheet"&gt;
  &lt;/head&gt;
  &lt;body&gt;
      &lt;input type="hidden" name="base_path" id="base_path" value="https://amplifipro.thesmartcube.com" /&gt;
      &lt;input type="hidden" name="ie_message" id="ie_message" value="1" /&gt;
      &lt;div id="app"&gt;
          &lt;main&gt;
              &lt;div class="container-fluid adjustContainerLogin px-0"&gt;
                  &lt;div class="row px-0 mx-0"&gt;
                      &lt;div class="col-sm-6"&gt;
                          &lt;div class="leftSide"&gt;
                              &lt;iframe style="border:0;z-index:1"
                                  src="https://assets.thesmartcube.com/smartcube/app/uploads/2020/09/Category-Intelligence.html"
                                  height="200px"&gt;&lt;/iframe&gt;
                              &lt;div class="text" style="color: #FFFFFF"&gt;CATEGORY&lt;br&gt;INTELLIGENCE&lt;/div&gt;
                              &lt;div class="TagLine" style="color: #FFFFFF"&gt;Intelligence. Accelerated&lt;/div&gt;
                          &lt;/div&gt;
                      &lt;/div&gt;
                      &lt;div class="col-sm-6 mobileComp"&gt;
  
                          &lt;img src="images/smartcube.svg" class="logo" alt=""&gt;
                          &lt;div class="container tab-container signInSection"&gt;
                              &lt;div id="the-message"&gt;
                              &lt;/div&gt;
                              &lt;nav&gt;
                              &lt;/nav&gt;
                              &lt;div class="tab-content" id="nav-tabContent"&gt;
                                  &lt;div class="tab-pane fade show active" id="signin" role="tabpanel"
                                      aria-labelledby="signin-tab"&gt;
                                      &lt;div class="card1"&gt;
                                          &lt;div class="card-header d-none"&gt;Login&lt;/div&gt;
                                          &lt;div class="text-center"&gt;&lt;img src="images/jnj.png" width="165" /&gt; &lt;/div&gt;
                                          &lt;div class=""&gt;
                                              &lt;form method="POST" id="form_login" class=""
                                                  action="https://amplifipro.thesmartcube.com/login"&gt;
                                                  &lt;input type="hidden" name="page_referer" id="page_referer" value=""&gt;
                                                  &lt;input type="hidden" name="_token"
                                                      value="Bie7ebTsNyWNtvSVjshwM5jZ0fr36oi1EAnpeSlZ"&gt;
                                                  &lt;div class="form-group mb-4"&gt;&lt;/div&gt;
                                                  &lt;div class="form-group mb-4"&gt;
                                                      &lt;!-- &lt;label for="email" class="col-md-4 col-form-label text-md-right"&gt;E-Mail Address&lt;/label&gt; --&gt;
  
                                                      &lt;div class=""&gt;
                                                          &lt;input id="email" type="text" class="form-control" name="email"
                                                              value="" placeholder="Email" autocomplete="email" autofocus&gt;
  
                                                      &lt;/div&gt;
                                                  &lt;/div&gt;
  
                                                  &lt;div class="form-group mb-4"&gt;
                                                      &lt;div class="relativePos"&gt;
                                                          &lt;input id="password" placeholder="Password" type="password"
                                                              class="form-control" name="password"
                                                              autocomplete="current-password"&gt;
                                                          &lt;i class="las la-eye eyeClick"&gt;&lt;/i&gt;
  
                                                      &lt;/div&gt;
                                                  &lt;/div&gt;
  
                                                  &lt;div class="form-group text-right formContainerTopAdjust "&gt;
  
                                                      &lt;a class="forgetPassBtn" href="#" id="forget-password"&gt;Forgot
                                                          password?&lt;/a&gt;
  
                                                  &lt;/div&gt;
  
                                                  &lt;div class="form-group row mb-0"&gt;
                                                      &lt;div class="col-sm-12 text-center"&gt;
                                                          &lt;button type="submit"
                                                              class="btn btn-sm whiteBG btn-primary signInbn"&gt;
                                                              Sign in
                                                          &lt;/button&gt;
  
  
                                                      &lt;/div&gt;
  
                                                  &lt;/div&gt;
  
                                              &lt;/form&gt;
                                              &lt;p class="size11 text-center d-block mt-4"&gt;
                                                  By logging into Amplifi PRO, you agree to the &lt;a
                                                      href="javascript:void(0);" data-toggle="modal"
                                                      data-target="#termsConditionModal" id="terms-cons"&gt;Terms &
                                                      conditions&lt;/a&gt; and &lt;a
                                                      href="https://www.thesmartcube.com/privacy-policy/" target="_blank"
                                                      id="privacy-policy"&gt;Privacy policy&lt;/a&gt;.
  
                                              &lt;/p&gt;
                                          &lt;/div&gt;
                                      &lt;/div&gt;
                                  &lt;/div&gt;
                              &lt;/div&gt;
                          &lt;/div&gt;
                          &lt;div class="forgetPasswordSection col-sm-12" style="display:none;"&gt;
                              &lt;nav&gt;
                                  &lt;div class="nav nav-tabs nav-fill" id="nav-tab" role="tablist"&gt;
                                      &lt;a class="nav-item noClick w-25 text-left pl-0 nav-link active"
                                          id="forgetPassword-tab" data-toggle="tab" href="#" role="tab"
                                          aria-controls="forgetPassword" aria-selected="true"&gt;Forgot password?&lt;/a&gt;
                                      &lt;a class="nav-item noClick nav-link" id="nothing-tab" data-toggle="tab"
                                          href="#nothing" role="tab" aria-controls="nothing"
                                          aria-selected="false"&gt;&nbsp;&lt;/a&gt;
  
                                  &lt;/div&gt;
                              &lt;/nav&gt;
  
                              &lt;form method="POST" id="ForgotForm" action=""&gt;
                                  &lt;input type="hidden" name="_token" value="Bie7ebTsNyWNtvSVjshwM5jZ0fr36oi1EAnpeSlZ"&gt;
  
                                  &lt;p class="fmessage error pt-3" style="color:#ff0000;"&gt;&lt;/p&gt;
  
  
                                  &lt;div class="fmessage_success alert alert-success" style="display:none"&gt;
                                      &lt;p class="mb-0 p-1"&gt;Link to reset password sent to your registered email Id&lt;/p&gt;
                                  &lt;/div&gt;
  
                                  &lt;div class="row"&gt;
                                      &lt;div class="form-group col-sm-8"&gt;
                                          &lt;div class="loader-info forgotLoader" style="display: none;"&gt;&lt;/div&gt;
                                          &lt;input class="form-control " autocomplete="off"
                                              placeholder="Enter your e-mail address" id="emailId" name="email"
                                              autofocus="" type="email" required&gt;
                                      &lt;/div&gt;
                                      &lt;div class="form-actions col"&gt;
                                          &lt;div class="row pt-1"&gt;
                                              &lt;button id="forgotSubmit" class="btn whiteBG size16 btn-sm btn-primary"
                                                  type="button"&gt;
                                                  Submit&lt;/button&gt;&nbsp;
                                              &lt;button id="back-btn"
                                                  class="back-btn whiteBG size16  btn-sm  btn btn-secondary  fback"
                                                  type="button"
                                                  onclick="window.location.href='https://amplifipro.thesmartcube.com/login'"&gt;Cancel&lt;/button&gt;
                                          &lt;/div&gt;
  
                                      &lt;/div&gt;
                                  &lt;/div&gt;
                              &lt;/form&gt;
                          &lt;/div&gt;
                          &lt;div class="loginFooter"&gt;
                              &copy; &lt;a href="https://www.thesmartcube.com/" target="_blank"&gt;thesmartcube.com.&lt;/a&gt; All
                              rights reserved.
                              &lt;span&gt;For the best visual experience access Amplifi PRO using Chrome/Edge&lt;/span&gt;
                          &lt;/div&gt;
                      &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;div class="modal fade" id="termsConditionModal" role="dialog"&gt;
                      &lt;div class="modal-dialog modal-lg"&gt;
                          &lt;!-- Modal content--&gt;
                          &lt;div class="modal-content"&gt;
                              &lt;div class="modal-header"&gt;
  
                                  &lt;h4 class="modal-title"&gt;Amplifi PRO - Terms of use
                                  &lt;/h4&gt;
  
                                  &lt;button type="button" class="close" id="terms-cons-close"
                                      data-dismiss="modal"&gt;&times;&lt;/button&gt;
                              &lt;/div&gt;
                              &lt;div class="modal-body"&gt;
                                  &lt;div class=""&gt;
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;1. General &lt;/h3&gt;
                                      &lt;p&gt;1.1. These terms of use (“the Terms of Use” or “the Agreement” apply to the use
                                          by an individual, institutional or corporate subscriber (‘Subscriber’) of the
                                          reports, data, software tools, information and editorial content (‘Licensed
                                          Materials’) contained in Amplifi PRO &lt;/p&gt;
  
                                      &lt;p&gt;1.2. Access to Amplifi PRO is open only to approved Individuals and clients of
                                          The Smart Cube and The Smart Cube hereby reserves the right to refuse access to
                                          any individual for any reason whatsoever and at its sole discretion. Where
                                          applicable, access is also conditional on payment by the Subscriber of all
                                          amounts due under their client contract (“the Relevant Contract”).&lt;/p&gt;
  
                                      &lt;p&gt;1.3. Individuals that sign up using the Amplifi PRO online registration facility
                                          hereby represent that all information provided during the account sign up
                                          process and at any time thereafter (“Account Information”) will be true,
                                          accurate, complete, and current and that they will promptly update their Account
                                          Information as necessary such that it is, at all times, true, accurate,
                                          complete, and current. &lt;/p&gt;
  
                                      &lt;p&gt;The Smart Cube may use all Account Information entered into Amplifi PRO, subject
                                          to compliance with our Privacy Policy that may be accessed &lt;a
                                              href="https://www.thesmartcube.com/privacy-policy/"&gt;here&lt;/a&gt;.&lt;/p&gt;
  
                                      &lt;p&gt;1.4. Subscribers are given access to the Licensed Materials in accordance with
                                          Section 2 of this Agreement and any additional terms (“the Special Terms”) that
                                          may be agreed between The Smart Cube and Subscriber in a formal contract (“the
                                          Relevant Contract”).&lt;/p&gt;
  
                                      &lt;p&gt;1.5. Individuals and corporate representatives may contact The Smart Cube via &lt;a
                                              href="mailto:<EMAIL>"&gt;<EMAIL>&lt;/a&gt; or by using
                                          the contact details in Amplifi PRO for further details of the Special Terms that
                                          may be available.&lt;/p&gt;
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;2. License Terms and Use Restrictions &lt;/h3&gt;
                                      &lt;p&gt;2.1 Subscribers will be given access to Amplifi PRO on a Per User License, a Site
                                          or Multisite License, an Enterprise License or a Function License (each a
                                          ‘License’) as approved by The Smart Cube and more specifically described in
                                          these terms and conditions. All Licenses are personal to the Subscriber and may
                                          not be assigned or transferred. Except where specifically provided otherwise,
                                          sections 2.2, 3, 4, 5, 6, 7 and 8 of these terms and conditions apply to all
                                          Licenses.&lt;/p&gt;
  
                                      &lt;p&gt;&lt;strong&gt;Per User License&lt;/strong&gt;; this license permits a single individual to
                                          access Amplifi PRO and to use the Licensed Materials in the course of the
                                          Subscriber’s normal business. &lt;/p&gt;
  
                                      &lt;p&gt;&lt;strong&gt;Function, Site or Multisite License&lt;/strong&gt;; this license permits a
                                          subset of the Subscriber’s employees and/or other workers as set out in the
                                          relevant Sales Order to access Amplifi PRO and to use the Licensed Materials in
                                          the course of the Subscriber’s normal business.&lt;/p&gt;
  
                                      &lt;p&gt;&lt;strong&gt;Enterprise License&lt;/strong&gt;; this license permits all the employees
                                          and/or other workers of the Subscriber Group to access Amplifi PRO and to use
                                          the Licensed Materials in the course of the Subscriber Group’s normal business.
                                          The addition of Affiliates to the Subscriber Group is subject to the prior
                                          written consent of The Smart Cube. References in these terms and conditions to
                                          Subscriber include the Subscriber Group provided that the Subscriber will be
                                          liable for acts and omissions of Affiliates as though such acts and/or omissions
                                          were the Subscriber’s own.&lt;/p&gt;
  
                                      &lt;p&gt;2.2 In these terms and conditions;&lt;/p&gt;
  
                                      &lt;p&gt;“Affiliate” in respect of a corporate entity means any other corporate entity
                                          which directly or indirectly, controls, is controlled by or is under common
                                          control with such entity and the term “control” (including the terms “controlled
                                          by” and “under common control with”) in relation to an entity means the
                                          ownership of 51% or more of the voting securities in that entity;&lt;/p&gt;
  
                                      &lt;p&gt;“Authorised User(s)” means; (i) Per User License - the named individual(s)
                                          authorised by The Smart Cube during the registration process; (ii) Site License
                                          - all the Subscriber’s employees and/or other workers normally located at the
                                          physical site(s) as authorised by The Smart Cube Order Form; (iii) Enterprise
                                          License - all employees and other workers in the Subscriber Group; &lt;/p&gt;
  
                                      &lt;p&gt;“Derived Materials” means materials created by or on behalf of the Subscriber
                                          incorporating the Licensed Materials in combination with other information
                                          and/or data;&lt;/p&gt;
  
                                      &lt;p&gt;“Subscriber Group” means a corporate subscriber and its Affiliates as at the date
                                          authorisation by the Smart Cube, which may be updated by Subscriber upon written
                                          notice to The Smart Cube;&lt;/p&gt;
  
                                      &lt;p&gt;“Unauthorised” in relation to a person means any person other than a Subscriber
                                          or any other person within or outside a Subscriber Group who is neither a
                                          Subscriber nor an Authorised User but excludes administrative and support staff
                                          who provide technical and other support services to a Subscriber or Authorised
                                          Users but do not otherwise use the Licensed Materials.&lt;/p&gt;
  
                                      &lt;p&gt;2.3 Passwords are for the personal use of the individual to whom they are issued
                                          and may not be made available to others for the purpose of using Amplifi PRO. If
                                          The Smart Cube suspects that a password is being used by an Unauthorised person
                                          it may cancel the password.&lt;/p&gt;
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;3. Intellectual Property Rights and Usage of
                                          Licensed Materials&lt;/h3&gt;
                                      &lt;p&gt;3.1. All intellectual property rights, including but not limited to copyright and
                                          database rights, in Amplifi PRO and the Licensed Materials (in both machine
                                          readable and printed form) are and remain the property of The Smart Cube or its
                                          third party licensors.&lt;/p&gt;
  
                                      &lt;p&gt;3.2 Subscribers and Authorised Users acquire no proprietary rights in Amplifi PRO
                                          or the Licensed Materials and except as expressly permitted by these terms and
                                          conditions may not use Amplifi PRO or the Licensed Materials in any way that
                                          infringes the intellectual property rights in them. In particular Subscribers
                                          and Authorised Users may not; (i) make Amplifi PRO or any part of the Licensed
                                          Materials available to Unauthorised persons; (ii) re-sell Amplifi PRO or any
                                          part of the Licensed Materials to others; or (iii) obscure or remove any
                                          copyright notices that appear on Licensed Materials extracted from Amplifi PRO.
                                      &lt;/p&gt;
  
                                      &lt;p&gt;3.3. Subject to all of the terms of this Agreement and any published or Special
                                          Terms of their License, Subscribers and Authorised Users may be able to download
                                          and/or print some or all of the Licensed Materials under the Amplifi PRO
                                          functionality. Downloading, printing and copying of the Licensed Materials by
                                          any other means is strictly prohibited. In particular, Subscribers and
                                          Authorised Users may not use any "deep link," "page scrape," "robot," "spider,"
                                          or other automatic device, program, script, algorithm, or methodology, or any
                                          similar or equivalent manual process, to access, acquire, copy, or monitor any
                                          portion or in any way reproduce or circumvent the navigational structure or
                                          presentation of Amplifi PRO to obtain or attempt to obtain any materials,
                                          documents, or information or through any other means not purposely made
                                          available.&lt;/p&gt;
  
                                      &lt;p&gt;3.4 Subscribers and Authorised Users may within the terms of the applicable
                                          License create Derived Data and use such Derived Data in the course of the
                                          Subscriber’s business provided that; (i) the Subscriber acknowledges The Smart
                                          Cube as a data source in relation to all Derived Data; and (ii) the Subscriber
                                          does not use or authorise the use of Derived Data in products or services that
                                          are competitive with Amplifi PRO.&lt;/p&gt;
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;4. Verification and Audit &lt;/h3&gt;
                                      &lt;p&gt;4.1. The Subscriber shall, within 7 business days of a written request from The
                                          Smart Cube provide; (i) a list of all individuals who have access to the
                                          Licensed Materials; or (ii) a certificate signed by an authorized representative
                                          of the Subscriber confirming that the Subscriber has complied in all material
                                          respects with these terms and specifically that the Licensed Materials have not
                                          been distributed or transmitted, in any form, to any Unauthorised person.&lt;/p&gt;
  
                                      &lt;p&gt;4.2. The Smart Cube or any other person authorised by The Smart Cube shall have
                                          the right, after giving written notice of ten business days, to enter the
                                          Subscriber’s premises during normal business hours solely to inspect the
                                          Subscriber’s records relating to the use and distribution of the Licensed
                                          Materials. The Smart Cube shall treat as confidential all information relating
                                          to the Subscriber’s business that it acquires in the course of such an
                                          inspection. The Smart Cube shall not exercise this right of inspection more than
                                          once in each calendar year.&lt;/p&gt;
  
                                      &lt;p&gt;4.3. If an audit performed by The Smart Cube under section 4.2 reveals that the
                                          Subscriber is in breach of these terms and conditions the Subscriber will
                                          reimburse The Smart Cube (i) the reasonable cost incurred by The Smart Cube in
                                          performing the audit; (ii) all fees payable in relation to any Unauthorised
                                          person revealed by the audit as having access to Amplifi PRO or the Licensed
                                          Materials; and (iii) interest on the above amounts from the date they become
                                          payable until the date of payment at the highest rate permitted by applicable
                                          law.&lt;/p&gt;
  
                                      &lt;p&gt;4.4. The rights of The Smart Cube under this section shall continue for the term
                                          of the subscription and for 12 months thereafter.&lt;/p&gt;
  
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;5. Subscriber’s Equipment &lt;/h3&gt;
                                      &lt;p&gt;It is the Subscriber’s responsibility to ensure that it has the equipment
                                          necessary to access Amplifi PRO and receive and use the Licensed Materials.&lt;/p&gt;
  
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;6. Availability of Amplifi PRO &lt;/h3&gt;
                                      &lt;p&gt;The Smart Cube shall use all reasonable endeavours in accordance with good
                                          industry practice to ensure that Amplifi PRO is available to Subscribers and
                                          Authorised Users excluding downtime for regular or emergency maintenance which
                                          shall be kept to a minimum. Time is not of the essence in respect to the
                                          delivery of any particular Amplifi PRO service or the Licensed Materials and The
                                          Smart Cube’s sole obligation is to effect such delivery as soon as is
                                          practically possible.&lt;/p&gt;
  
                                      &lt;p&gt;Subject to the above:&lt;/p&gt;
  
                                      &lt;p&gt;1. The service level target for platform availability is 99.95% or better. &lt;/p&gt;
  
                                      &lt;p&gt;2. A Smart Cube helpdesk service will be available to Subscriber and will be
                                          reached through e-mail setup for the purpose&lt;/p&gt;
  
                                      &lt;p&gt;3. In case of any incident on the website, Subscriber would be notified via
                                          e-mail. Whenever possible, the home page of the platform will also be updated
                                          with appropriate message to notify users of any outage.&lt;/p&gt;
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;7. Limitations on Liability &lt;/h3&gt;
                                      &lt;p&gt;7.1 Amplifi PRO and the Licensed Materials are provided by The Smart Cube on an
                                          ‘as is’ basis and The Smart Cube excludes to the extent permitted by law all
                                          implied warranties relating to fitness for a particular purpose.&lt;/p&gt;
  
                                      &lt;p&gt;7.2 The Smart Cube accepts no liability in connection with use of Amplifi PRO or
                                          the Licensed Materials and in particular shall not be liable in contract, tort
                                          (including negligence) or otherwise for the following losses arising out of, or
                                          in connection with, this Agreement: (a) indirect or consequential loss or
                                          damage; or (b) (whether they arise directly or indirectly) loss of business,
                                          revenue, opportunity, profits, goodwill or data. SUBSCRIBER HEREBY WAIVES ALL
                                          CLAIMS AGAINST THE SMART CUBE, ITS OFFICERS, EMPLOYEES, AND AGENTS, FOR ANY
                                          DIRECT, INDIRECT, INCIDENTAL, CONSEQUENTIAL OR SPECIAL DAMAGES OF ANY KIND OR
                                          NATURE, INCLUDING BUT NOT LIMITED TO LOST BUSINESS, REVENUE, OPPORTUNITIES,
                                          PROFITS, GOODWILL, AND DATA AND DAMAGES ARISING FROM OR IN ANY WAY CONNECTED
                                          WITH THIS AGREEMENT, WHETHER ALLEGED TO ARISE FROM BREACH OF CONTRACT, EXPRESS
                                          OR IMPLIED WARRANTY, OR IN TORT INCLUDING WITHOUT LIMITATION, NEGLIGENCE, EXCEPT
                                          AS OTHERWISE SPECIFICALLY PERMITTED UNDER THIS AGREEMENT.&lt;/p&gt;
  
                                      &lt;p&gt;7.3 The Smart Cube shall be under no liability for any failure, delay or omission
                                          by it arising from any cause beyond its control, including, but not limited to
                                          acts of God, acts or regulations of any governmental or supra-national
                                          authority, war or national emergency, global pandemics, denial of service
                                          attacks, fire, civil disobedience, strikes, lock-outs and industrial disputes.
                                          If the force majeure event continues for a period of thirty days or more,
                                          Subscriber may terminate this Agreement upon written notice to The Smart Cube.
                                      &lt;/p&gt;
  
                                      &lt;p&gt;7.4 The Smart Cube accepts no liability for any links to third party websites
                                          that may be included in Amplifi PRO for the general benefit of Subscribers and
                                          Authorised Users and such websites and materials are expressly not to be
                                          regarded as Licensed Materials. The Smart Cube is not responsible for the
                                          content, accuracy or opinions expressed on such web sites, such web sites are
                                          not investigated, monitored or checked for accuracy or completeness, and the
                                          inclusion of a link does not imply any approval or endorsement of the linked web
                                          site. &lt;/p&gt;
  
                                      &lt;p&gt;In no event will The Smart Cube be liable to any party for any direct, indirect,
                                          incidental, special, exemplary, punitive or consequential damages of any type
                                          whatsoever related to or arising from hyperlinks to web content referenced or
                                          accessed through Amplifi PRO, or for the use or downloading of, or access to any
                                          materials, information, products, or services, including, without limitation,
                                          any lost profits, business interruption, lost savings or loss of programs or
                                          other data, even if The Smart Cube is expressly advised of the possibility of
                                          such damages.&lt;/p&gt;
  
                                      &lt;p&gt;Subscribers and Authorised Users agree to indemnify, defend and hold harmless The
                                          Smart Cube, its subsidiaries and affiliates, its officers, directors, employees
                                          and agents from any claim, cost, expense, judgment or other loss relating to
                                          their use of Amplifi PRO and the linked websites including, without limitation
                                          of the foregoing, any action you take which is in violation of the terms and
                                          conditions of this Agreement. The failure of The Smart Cube to exercise or
                                          enforce any right or provision of these terms shall not constitute a waiver of
                                          such right or provision.&lt;/p&gt;
  
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;8. Suspension and Termination&lt;/h3&gt;
                                      &lt;p&gt;8.1 The Smart Cube may without notice and without compensation suspend access to
                                          Amplifi PRO by Subscriber and/or one or more Authorised Users if (i) the
                                          Subscriber is in default of its payment obligations under a License, Relevant
                                          Contract or Customer Intelligence report purchase and has failed to cure such
                                          default within ten business days of receipt of The Smart Cube’s written notice
                                          describing such failure or (ii) The Smart Cube has reasonable grounds to suspect
                                          the Subscriber or such Authorised User (s) to be in breach of these terms and
                                          conditions, though The Smart Cube shall use reasonable efforts to notify
                                          Subscriber or Authorized User reasonably in advance of suspension.&lt;/p&gt;
  
                                      &lt;p&gt;8.2. Subject to the terms of a Relevant Contract, The Smart Cube may withdraw
                                          access to Amplifi PRO at any time.&lt;/p&gt;
  
                                      &lt;p&gt;8.3. On expiry of the Relevant Contract with The Smart Cube, the Subscriber shall
                                          cease all use of the Licensed Materials in accordance with the Relevant Contract
                                          immediately and revert to the standard usage of the Licensed Materials in
                                          accordance with this Agreement.&lt;/p&gt;
  
                                      &lt;p&gt;8.4. Expiry or termination of a subscription or Relevant Contact shall be without
                                          prejudice to the accrued rights and obligations of the parties and, in
                                          particular, sections 3, 4 and 7 shall survive termination for whatever reason.
                                      &lt;/p&gt;
  
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;9. Confidentiality&lt;/h3&gt;
                                      &lt;p&gt;
                                          Each of the parties acknowledges that, whether in the performance of this
                                          Agreement or otherwise, it will receive or become aware of information relating
                                          to the other party, its clients, business or affairs, which information is
                                          confidential to the other party ("Confidential Information"). Each party will
                                          keep all Confidential Information of the other party confidential and take
                                          reasonable steps to keep it secure and protected against theft, damage, loss or
                                          unauthorised access and will not at any time without the prior written consent
                                          of the other party use or disclose any of the other party’s Confidential
                                          Information other than for the sole purpose of the performance of its
                                          obligations and the exercise of its rights under this Agreement. Notwithstanding
                                          the above, The Smart Cube shall be entitled to disclose the Confidential
                                          Information of the Subscriber to its affiliates, independent contractors and
                                          professional advisers on a strictly need to know basis for the sole purpose of
                                          the performance of its obligations under this Agreement provided always that
                                          such persons are aware of the obligations of confidentiality under this
                                          Agreement and The Smart Cube shall use reasonable endeavors to procure
                                          compliance with such obligations of confidentiality. The obligations of
                                          confidentiality shall not apply to any Confidential Information to the extent to
                                          which it: (a) is in the public domain; (b) is lawfully received by the recipient
                                          from a third party on an unrestricted basis; (c) is already known to the
                                          recipient before receipt from the discloser; or (d) is required to be disclosed
                                          by law, regulation or pursuant to an order of a competent authority, regulatory
                                          body, recognised stock exchange or to a professional adviser, provided the
                                          recipient provides the discloser with reasonable written notice prior to any
                                          such disclosure.
                                      &lt;/p&gt;
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;10. Content&lt;/h3&gt;
                                      &lt;p&gt;10.1. Materials and features may be added to and removed from Amplifi PRO and the
                                          Licensed Materials without notice.&lt;/p&gt;
  
                                      &lt;p&gt;10.2. The Materials and information included in Amplifi PRO and the Licensed
                                          Materials are provided for reference purposes only. They are not intended either
                                          as a substitute for professional advice or judgement or to provide legal or
                                          other advice with respect to particular circumstances.&lt;/p&gt;
  
                                      &lt;p&gt;10.3. Every effort is made to keep Amplifi PRO and the Licensed Materials up to
                                          date and/or in accordance with published schedules, but users are advised to
                                          obtain independent verification or advice before relying on any piece of
                                          information in circumstances where loss or damage may result. &lt;/p&gt;
  
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;11. Custom Intelligence&lt;/h3&gt;
                                      &lt;p&gt;11.1. Subscribers to Amplifi PRO may submit requests for Custom Intelligence
                                          reports from The Smart Cube by Choosing a Report Type, Selecting the Scope and
                                          Submitting a Request.&lt;/p&gt;
  
                                      &lt;p&gt;11.2. Following a Request, The Smart Cube will respond with a Proposal to produce
                                          the requested Customer Intelligence Report that will incorporate the price of
                                          the report and the terms and conditions, including invoicing and payment terms
                                          that shall apply to the report, if ordered by the Subscriber.&lt;/p&gt;
  
                                      &lt;p&gt;11.3. For the avoidance of doubt, Custom Intelligence reports are not part of the
                                          Licensed Materials and are not subject to or otherwise governed by the terms of
                                          this Agreement.&lt;/p&gt;
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;12. Compliance with Applicable Laws&lt;/h3&gt;
                                      &lt;p&gt;Each Party agrees to abide at all times with all the applicable laws and
                                          regulations (including, but not limited to, privacy laws, data protection
                                          legislations, anti-trust laws etc.) that may be in place from time to time. &lt;/p&gt;
  
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;13. Waiver&lt;/h3&gt;
                                      &lt;p&gt;The failure of either Party at any time to enforce any provisions of this
                                          Agreement or to exercise any right herein provided, shall not be considered as a
                                          waiver of such right or any other provision or in any way effect the validity of
                                          this Agreement.&lt;/p&gt;
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;14. Severability&lt;/h3&gt;
                                      &lt;p&gt;The invalidity of any portion of this Agreement by a court with legal
                                          jurisdiction shall not affect the remaining portions of this Agreement or any
                                          part thereof, and this Agreement shall be construed, as if the invalid portion
                                          or portions had not been inserted therein.&lt;/p&gt;
  
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;15. Notices&lt;/h3&gt;
                                      &lt;p&gt;Any notice required or permitted to be given hereunder shall be sent by email,
                                          registered post or equivalent, facsimile, courier or other electronic
                                          transmission..&lt;/p&gt;
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;16. Law &lt;/h3&gt;
                                      &lt;p&gt;The Agreement shall be construed and have effect in accordance with the laws of
                                          England and Wales.&lt;/p&gt;
  
                                      &lt;h3 class="font-size16  py-2 mb-0 h3"&gt;17. Entirety of Agreement &lt;/h3&gt;
                                      &lt;p&gt;This Agreement represents the entire agreement and understanding of the Parties
                                          and all prior or concurrent agreements, whether written or oral, in regard to
                                          the subject matter hereof are and have been merged herein and superseded hereby.
                                      &lt;/p&gt;
  
                                  &lt;/div&gt;
                              &lt;/div&gt;
                          &lt;/div&gt;
  
                      &lt;/div&gt;
                  &lt;/div&gt;
  
                  &lt;!-- Modal Popup for Internet Explorer Message--&gt;
                  &lt;div class="modal fade" id="IEBrowserMessage" tabindex="-1" role="dialog"
                      aria-labelledby="favouriteModal" aria-hidden="true"&gt;
                      &lt;div class="modal-dialog" role="document"&gt;
                          &lt;div class="modal-content"&gt;
                              &lt;div class="modal-header"&gt;
                                  &lt;h5 class="modal-title" id="IEBrowserTitle"&gt;&nbsp;&lt;/h5&gt;
                                  &lt;button type="button" class="close" data-dismiss="modal" aria-label="Close"&gt;
                                      &lt;span aria-hidden="true"&gt;&times;&lt;/span&gt;
                                  &lt;/button&gt;
                              &lt;/div&gt;
                              &lt;div class="modal-body"&gt;
                                  &lt;div id="IEBrowserText"&gt;For the best visual experience access Amplifi PRO using
                                      Chrome/Edge&lt;/div&gt;
                              &lt;/div&gt;
                              &lt;div class="modal-footer"&gt;
                                  &lt;button type="button" class="btn btn-sm btn-primary" data-dismiss="modal"&gt;OK&lt;/button&gt;
                              &lt;/div&gt;
                          &lt;/div&gt;
                      &lt;/div&gt;
                  &lt;/div&gt;
                  &lt;script src="js/jquery-3.3.1.min.js?v=1.1.3"&gt;&lt;/script&gt;
                  &lt;script type="application/javascript" src="js/jquery.validate.min.js?v=1.1.3"&gt;&lt;/script&gt;
                  &lt;script src="js/bootstrap.min.js?v=1.1.3"&gt;&lt;/script&gt;
                  &lt;script src="js/base.js?v=1.1.3"&gt;&lt;/script&gt;
                  &lt;script type="application/javascript" src="js/login.js?v=1.1.3"&gt;&lt;/script&gt;
                  &lt;script type="application/javascript" src="js/forgot-password.js?v=1.1.3"&gt;&lt;/script&gt;
                  &lt;script src="js/getBrowserDetails.js?v=1.1.3"&gt;&lt;/script&gt;
                  &lt;script&gt;
                      $(function () {
                          $('.forgetPassBtn').click(function () {
                              $('.forgetPasswordSection').show();
                              $('.signInSection').hide();
                          })
                          $('.back-btn').click(function () {
                              $('.forgetPasswordSection').hide();
                              $('.signInSection').show();
                          })
  
                          $('.eyeClick').click(function () {
                              if ($(this).hasClass('la-eye')) {
                                  $('.eyeClick').removeClass('la-eye');
                                  $('.eyeClick').addClass('la-low-vision');
                                  $('#password').attr('type', 'text');
                              } else {
                                  $('.eyeClick').removeClass('la-low-vision');
                                  $('.eyeClick').addClass('la-eye');
                                  $('#password').attr('type', 'password');
                              }
  
                          });
  
                          // Check if user browser is IE
                          var isIEMessageActive = jQuery("#ie_message").val();
                          if (isIEMessageActive == 1) {
                              var userBrowser = jQuery.browser.name;
                              if (userBrowser == 'msie') {
                                  jQuery('#IEBrowserMessage').modal('show');
                              }
                          }
                      });
                  &lt;/script&gt;
          &lt;/main&gt;
      &lt;/div&gt;
  &lt;/body&gt;
  &lt;/html&gt;

</code>
</pre>
           
        </div>
        
        <!-- <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">

            </code></pre>
        </div> -->
       
      </div>
    </div></div>
  </div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
