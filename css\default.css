body {
  /*	height:100%;*/
  font-family: "Fira Sans" !important;
  font-size: 12px;
  height: 100%;
}
.bg-dark {
  background-color: #112636 !important;
}
.card-body p {
  margin-bottom: 10px;
}
.card {
  height: 342px;
}
footer {
  background-color: #112636 !important;
  min-height: 44px;
  padding-top: 10px;
  margin-bottom: 0px;
}
footer a {
  color: #fff;
}
footer a:hover {
  color: #fff;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #112636 !important;
  color: #fff;
}
.shadow-sm {
  box-shadow: 0 1px 1px 1px rgba(18, 106, 211, 0.08) !important;
}
.bg-dark {
  background-color: #fff !important;
  /* box-shadow: 0 1px 1px 1px rgba(18,106,211,.08); */
}
body {
  background: #ffffff;
}
.btn {
  border-radius: 0 !important;
}
.navbar-dark .navbar-brand {
  color: #212529;
}
.navbar-dark .navbar-brand:focus,
.navbar-dark .navbar-brand:hover {
  color: #212529;
}
.navbar-dark .navbar-brand sup {
  color: #212529;
}
.jumbotron {
  padding: 2rem 1rem;
  margin-bottom: 2rem;
  background-color: #fff;
  border-radius: 0.3rem;
}
/* footer {

    visibility: hidden;
} */

.jumbotron {
  padding: 2rem 1rem;
  margin-bottom: 0rem;
  background-color: #fff;
  border-radius: 0.3rem;
}
button#seeMore {
  margin-top: 20px;
}
.astrodivider {
  margin: 30px auto;
  width: 90%;
  max-width: 100%;
  position: relative;
}

.astrodividermask {
  overflow: hidden;
  height: 20px;
}

.astrodividermask::after {
  content: "";
  display: block;
  margin: -25px auto 0px;
  width: 100%;
  height: 25px;
  border-radius: 125px / 12px;
  box-shadow: rgb(7 7 7 / 77%) -1px 0px 4px 0px;
}
.astrodivider span {
  width: 50px;
  height: 50px;
  position: absolute;
  bottom: 100%;
  margin-bottom: -25px;
  left: 50%;
  margin-left: -25px;
  border-radius: 100%;
  box-shadow: 0 2px 4px #343a40;
  background: #fff;
}
.astrodivider i {
  position: absolute;
  top: 4px;
  bottom: 4px;
  left: 4px;
  right: 4px;
  border-radius: 100%;
  border: 1px dashed #212529;
  text-align: center;
  line-height: 40px;
  font-style: normal;
  color: #212529;
}

.view {
  margin: 4px;
  /* float: left; */
  border: 10px solid #f8f9fa;
  overflow: hidden;
  position: relative;
  text-align: center;
  box-shadow: -1px 4px 6px 1px #b5c6cd;
  cursor: default;
  background: #fff no-repeat center center;
}

.view .mask,
.view .content {
  width: 100%;
  height: 230px;
  position: absolute;
  overflow: hidden;
  top: 0;
  left: 0;
}

.view img {
  display: block;
  position: relative;
}

.view h2 {
  text-transform: uppercase;
  color: #fff;
  text-align: center;
  position: relative;
  font-size: 17px;
  font-family: "Fira Sans", serif;
  padding: 10px;
  /*background: rgba(0, 0, 0, 0.8);*/
  margin: 20px 0 0 0;
}

.view p {
  font-family: "Fira Sans", serif;
  font-style: italic;
  font-size: 14px;
  position: relative;
  color: #fff;
  padding: 0px 20px 0px;
  text-align: center;
}

.view a.info {
  display: inline-block;
  text-decoration: none;
  padding: 7px 14px;
  background: #000;
  color: #fff;
  font-family: "Fira Sans", serif;
  text-transform: uppercase;
  box-shadow: 0 0 1px #000;
}

.view a.info:hover {
  box-shadow: 0 0 5px #000;
}
.view img {
  display: block;
  position: relative;
  width: 100%;
  height: 230px;
}

.view-tenth img {
  transform: scaleY(1);
  transition: all 0.7s ease-in-out;
}

.view-tenth .mask {
  background-color: rgb(33 37 41 / 89%);
  transition: all 0.5s linear;
  opacity: 0;
}

.view-tenth h2 {
  border-bottom: 1px solid rgba(0, 0, 0, 0.3);
  background: transparent;
  margin: 20px 40px 0px 40px;
  transform: scale(0);
  color: #fff;
  transition: all 0.5s linear;
  opacity: 0;
}

.view-tenth p {
  color: #333;
  opacity: 0;
  transform: scale(0);
  transition: all 0.5s linear;
}

.view-tenth a.info {
  opacity: 0;
  transform: scale(0);
  transition: all 0.5s linear;
}

.view-tenth:hover img {
  -webkit-transform: scale(10);
  transform: scale(10);
  opacity: 0;
}

.view-tenth:hover .mask {
  opacity: 1;
}

.view-tenth:hover h2,
.view-tenth:hover p,
.view-tenth:hover a.info {
  transform: scale(1);
  opacity: 1;
}
.view p {
  font-family: "Fira Sans", serif;
  font-style: italic;
  font-size: 14px;
  position: relative;
  color: #fff !important;
  padding: 10px 20px 0px;
  text-align: center;
}
#seeMore.btn-outline-primary {
  border-color: #fff;
  color: #fff;
}

div.wrapper div {
  position: absolute;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0.1vw solid #212529;
  overflow: hidden;
}
div.wrapper div:before,
div.wrapper div:after {
  content: "";
  display: block;
  position: absolute;
}

/* .wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 78vw;
    height: 70vh;
    border: 0;
    transform: translate(-50%, -50%);
    overflow: visible;
  } */
.wrapper .icon {
  position: relative;
  display: inline-block;
  width: 12vw;
  height: 12vw;
  margin: 0;
  border: 0;
  overflow: visible;
}
.wrapper .icon .moon,
.wrapper .icon .planetoid,
.wrapper .icon .planet {
  border-radius: 50%;
  background: #ffffff;
}
.wrapper .icon.icon5 .planet {
  top: 15%;
  left: 25%;
  width: 62%;
  height: 62%;
  border-color: rgb(0, 177, 156) rgb(0, 177, 156) rgb(0, 177, 156) transparent;
  transform: rotate(-47deg);
  overflow: visible;
}
.wrapper .icon.icon5 .planet:after {
  top: -25%;
  left: 50%;
  width: 1%;
  height: 1%;
  border: 0.1vw solid rgb(0, 177, 156);
  border-radius: 50%;
}
.wrapper .icon.icon5 .rocket {
  top: 0;
  left: 33%;
  width: 40%;
  height: 100%;
  border-color: transparent;
  transform: rotate(47deg);
}
.wrapper .icon.icon5 .rocket .body {
  top: 0;
  left: 15%;
  width: 70%;
  height: 90%;
  border-color: transparent;
  animation: icon5body 0.5s ease infinite;
}
.wrapper .icon.icon5 .rocket .body:before,
.wrapper .icon.icon5 .rocket .body:after {
  top: -10%;
  width: 200%;
  height: 118%;
  border-radius: 50%;
  border: 0.1vw solid #00b19c;
}
.wrapper .icon.icon5 .rocket .body:after {
  left: -110%;
}
.wrapper .icon.icon5 .rocket .thruster.left,
.wrapper .icon.icon5 .rocket .thruster.right {
  top: 60%;
  left: 7.5%;
  width: 25%;
  height: 25%;
  border-color: transparent;
}
.wrapper .icon.icon5 .rocket .thruster.left:before,
.wrapper .icon.icon5 .rocket .thruster.left:after,
.wrapper .icon.icon5 .rocket .thruster.right:before,
.wrapper .icon.icon5 .rocket .thruster.right:after {
  border-radius: 50%;
  border: 0.1vw solid #00b19c;
}
.wrapper .icon.icon5 .rocket .thruster.left:before,
.wrapper .icon.icon5 .rocket .thruster.right:before {
  top: -10%;
  width: 200%;
  height: 120%;
}
.wrapper .icon.icon5 .rocket .thruster.left:after,
.wrapper .icon.icon5 .rocket .thruster.right:after {
  top: 60%;
  left: 30%;
  width: 132%;
  padding-top: 132%;
}
.wrapper .icon.icon5 .rocket .thruster.right {
  left: 64.5%;
  transform: rotatey(180deg);
}
.wrapper .icon.icon5 .rocket .thruster.mid {
  top: 60%;
  left: 23%;
  width: 51%;
  height: 12%;
  border-color: transparent;
}
.wrapper .icon.icon5 .rocket .thruster.mid:before {
  top: 0;
  left: 0;
  width: 90%;
  padding-top: 90%;
  border-radius: 50%;
  border: 0.1vw solid #00b19c;
  border-color: #00b19c #00b19c transparent #00b19c;
}
.wrapper .icon.icon5 .line {
  height: 1%;
  border-color: #00b19c transparent transparent transparent;
  transform: rotate(-45deg);
  transform-origin: top right;
  overflow: visible;
}
.wrapper .icon.icon5 .line:before {
  height: 1%;
  border: 0.1vw solid #00b19c;
  border-color: #00b19c transparent transparent transparent;
}
.wrapper .icon.icon5 .line.left {
  width: 24%;
  top: 60%;
  left: 4%;
  animation: icon5line1 0.5s ease infinite reverse;
}
.wrapper .icon.icon5 .line.left:before {
  top: -15%;
  left: -60%;
  width: 20%;
}
.wrapper .icon.icon5 .line.mid {
  width: 44%;
  top: 50%;
  left: 5.5%;
  animation: icon5line2 0.5s ease infinite;
}
.wrapper .icon.icon5 .line.mid:before {
  top: -105%;
  left: 110%;
  width: 10%;
}
.wrapper .icon.icon5 .line.right {
  width: 14%;
  top: 80%;
  left: 17%;
  animation: icon5line3 0.5s ease 1s infinite reverse;
}
.wrapper .icon.icon5 .line.right:before {
  width: 0;
  border-color: transparent;
}

@keyframes icon5body {
  0% {
    top: 0%;
    left: 15%;
  }
  100% {
    top: 0.5%;
    left: 15.5%;
  }
}
@keyframes icon5line1 {
  0% {
    top: 60%;
    left: 4%;
  }
  100% {
    top: 60.5%;
    left: 4.5%;
  }
}
@keyframes icon5line2 {
  0% {
    top: 50%;
    left: 5.5%;
  }
  100% {
    top: 50.5%;
    left: 6%;
  }
}
@keyframes icon5line3 {
  0% {
    top: 80%;
    left: 17%;
  }
  100% {
    top: 80.5%;
    left: 17.5%;
  }
}
.jumbotron {
  padding-top: 0px;
  border-radius: 0px;
  padding-bottom: 5px;
}

.fancyTab {
  text-align: center;
  padding: 15px 0;
  background-color: #ecf1f3;
  box-shadow: 0 0 0 1px #ddd;
  top: 15px;
  transition: top 0.2s;
  position: relative;
}

.fancyTab.active {
  top: 0;
  transition: top 0.2s;
}

.whiteBlock {
  display: none;
}

.fancyTab.active .whiteBlock {
  display: block;
  height: 2px;
  bottom: -2px;
  background-color: #fff;
  width: 99%;
  position: absolute;
  z-index: 1;
}

.fancyTab a {
  font-family: "Fira Sans";
  font-size: 1.35em;
  font-weight: 300;
  color: #333;
}

/*.fancyTab .hidden-xs {
    white-space:nowrap;
  }*/

.fancyTabs {
  border-bottom: 2px solid #ddd;
  margin: 15px 0 0;
}

li.fancyTab a {
  padding-top: 15px;
  top: -15px;
  padding-bottom: 0;
}

li.fancyTab.active a {
  padding-top: inherit;
}

.fancyTab .fa,
.fancyTab .fas.iconFont {
  font-size: 30px;
  width: 100%;
  padding: 15px 0 5px;
  color: #6c757d;
}

.fancyTab.active .iconFont {
  color: #00ae9b;
}
.fancyTab a:focus {
  outline: none;
}

.fancyTabContent {
  border-color: transparent;
  box-shadow: 0 -2px 0 -1px #fff, 0 0 0 1px #ddd;
  padding: 0;
  position: relative;
  background-color: #fff;
  padding-bottom: 5px;
}

.nav-tabs > li.fancyTab.active > a,
.nav-tabs > li.fancyTab.active > a:focus,
.nav-tabs > li.fancyTab.active > a:hover {
  border-width: 0;
}

.nav-tabs > li.fancyTab:hover {
  background-color: #f9f9f9;
  box-shadow: 0 0 0 1px #ddd;
}

.nav-tabs > li.fancyTab.active:hover {
  background-color: #fff;
  box-shadow: 1px 1px 0 1px #fff, 0 0px 0 1px #ddd, -1px 1px 0 0px #ddd inset;
}

.nav-tabs > li.fancyTab:hover a {
  border-color: transparent;
}

.nav.nav-tabs .fancyTab a[data-toggle="tab"] {
  background-color: transparent;
  border-bottom: 0;
}

.nav-tabs > li.fancyTab:hover a {
  border-right: 1px solid transparent;
}

.nav-tabs > li.fancyTab > a {
  margin-right: 0;
  border-top: 0;
  padding-bottom: 30px;
  margin-bottom: -30px;
}

.nav-tabs > li.fancyTab {
  margin-right: 0;
  margin-bottom: 0;
}

.nav-tabs > li.fancyTab:last-child a {
  border-right: 1px solid transparent;
}

.nav-tabs > li.fancyTab.active:last-child {
  border-right: 0px solid #ddd;
  box-shadow: 0px 2px 0 0px #fff, 0px 0px 0 1px #ddd;
}

.fancyTab:last-child {
  box-shadow: 0 0 0 1px #ddd;
}

.tabs .nav-tabs li.fancyTab.active a {
  box-shadow: none;
  top: 0;
}

.fancyTab.active {
  background: #fff;
  box-shadow: 1px 1px 0 1px #fff, 0 0px 0 1px #ddd, -1px 1px 0 0px #ddd inset;
  padding-bottom: 30px;
}

.fancyTab .arrow-down {
  display: none;
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-top: 22px solid #ddd;
  position: absolute;
  top: -1px;
  left: calc(50% - 20px);
}

.fancyTab .arrow-down-inner {
  width: 0;
  height: 0;
  border-left: 18px solid transparent;
  border-right: 18px solid transparent;
  border-top: 12px solid #fff;
  position: absolute;
  top: -22px;
  left: -18px;
}

.fancyTab.active .arrow-down {
  display: block;
}

@media (max-width: 1200px) {
  .fancyTab .fa,
  .fancyTab .fas.iconFont {
    font-size: 26px;
  }

  .fancyTab .hidden-xs {
    font-size: 22px;
  }
}

@media (max-width: 992px) {
  .fancyTab .fa,
  .fancyTab .fas.iconFont {
    font-size: 23px;
  }

  .fancyTab .hidden-xs {
    font-size: 18px;
    font-weight: normal;
  }
}

@media (max-width: 768px) {
  .fancyTab > a {
    font-size: 18px;
  }

  .nav > li.fancyTab > a {
    padding: 15px 0;
    margin-bottom: inherit;
  }

  .fancyTab .fa,
  .fancyTab .fas.iconFont {
    font-size: 20px;
  }

  .nav-tabs > li.fancyTab > a {
    border-right: 1px solid transparent;
    padding-bottom: 0;
  }

  .fancyTab.active .iconFont {
    color: #333;
  }
}
.fancyTabContent:focus {
  outline: none;
}
div.tab-pane {
  outline: none !important;
}
.size20 {
  font-size: 20px;
}
#leftNav ul.listStyle li {
  list-style: none;
  border-bottom: 1px solid #eee;
  padding: 5px 0;
  margin-right: 20px;
}
#leftNav .listStyle {
  list-style: none;
  margin-top: 11px;
  margin-left: 10px;
  padding-left: 14px;
  color: #00ae9b;
  font-size: 15px;
}
#leftNav .panel-heading-a {
  text-decoration: none;
  color: black;
  content: "\f0d7";
  font-size: 1rem;
  display: block;
}
#leftNav .panel-heading-a:before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f107";
  color: #000;
  padding-left: 5px;
  padding-right: 3px;
}
.bd-sidebar {
  overflow-y: auto;
  position: sticky;
  z-index: 1000;
  top: 0;
  height: calc(100vh - 0.1rem);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-left: 0;
}
#leftNav ul.listStyle li.active a {
  color: #000 !important;
  text-decoration: underline !important;
}
#rightSection {
  transition: margin-left 0.5s;
  padding-top: 38px;
}
.close-menu-icon {
  display: block;
  cursor: pointer;
  font-size: 20px;
}
.pl-30 {
  padding-left: 30px !important;
}
#menuHolder {
  padding-right: 0;
  padding-left: 12px;
}
.hamSection {
  position: absolute;
  top: 67px;
  left: 16px;
  z-index: 9999;
  background: #fff;
  height: 30px;
  width: 200px;
  /* border-top: 3px solid #f7f4f4; */
  padding-top: 7px;
}
.leftList {
  padding-top: 20px;
}
.hamSection .fa-bars {
  padding-left: 4px;
}
.hamSection .fas {
  transform: scale(1.5, 1);
}
div#leftNav section {
  margin-bottom: 5px;
  margin-top: 5px;
}
#leftNav .listStyle {
  list-style: none;
  margin-top: 11px;
  margin-left: -5px;
  padding-left: 14px;
  color: #00ae9b;
  font-size: 15px;
}
#leftNav ul.listStyle li {
  list-style: none;
  border-bottom: none;
  padding: 5px 0;
  margin-right: 20px;
  padding-left: 20px;
}
#leftNav ul.listStyle li {
  border-left: 1px dotted #808080;
}
#leftNav ul.listStyle li.active a {
  color: #000 !important;
  text-decoration: none !important;
  position: relative;
}
#leftNav ul.listStyle li.active {
  position: relative;
  text-decoration: none !important;
}
#leftNav ul.listStyle li.active a::after {
  content: " ";
  position: absolute;
  border-bottom: 1px dotted #808080;
  width: 16px;
  height: 10px;
  left: -20px;
  top: 0px;
}
.filter a {
  display: block;
}
.badge-primary {
  background-color: #112636;
  border-radius: 0px;
  border-color: #0000;
  color: #fff;
  padding: 4px 10px;
  min-width: 35px;
}
#tab01 iframe {
  background-color: #fff !important;
}
.marquee {
  position: relative;
  width: 76%;
  /* max-width: 100%; */
  height: 29px;
  overflow-x: hidden;
  /* z-index: 999999; */
  /* border: 1px solid red; */
  overflow-y: hidden;
}

.track {
  position: absolute;
  white-space: nowrap;
  will-change: transform;
  animation: marquee 20s linear infinite;
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-50%);
  }
}
