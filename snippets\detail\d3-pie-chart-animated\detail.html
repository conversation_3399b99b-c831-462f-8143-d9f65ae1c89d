
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>D3 Animated PIE Chart</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">D3 Animated pie chart</h4>
              <div class="mb-2">
                <a
                  href="./downloads/d3-pie-chart-animated.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <!-- <li><a href="#tab03">CSS</a></li> -->
                <!-- <li><a href="#tab04">Javascript</a></li> -->
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/d3-pie-chart-animated/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
<pre class="line-numbers">
<code class="language-markup">&lt;div class="pie-container"&gt;
    &lt;div class="row"&gt;
      &lt;div class="col-md-5" id="pieChart"&gt;&lt;/div&gt;
      &lt;div id="pieText" class="col-md-7 text-container"&gt;
        &lt;div class="panel"&gt;
          &lt;div class="content-wrapper"&gt;
            &lt;h1 id="segmentTitle"&gt;Select Fragment&lt;/h1&gt;
            &lt;p id="segmentText"&gt;
              Detailed information about internal systems and business
              operations.
            &lt;/p&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;</code>  
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">
                @import url(https://fonts.googleapis.com/css?family=Fira+Sans);
                body {
                  font-size: 15px;
                  font-family: "Fira Sans", sans-serif;
                  color: #fff;
                  background-color: #111;
                }
          
                .pie-container {
                  transition: padding 0.7s ease;
                  padding: 10px 13%;
                }
          
                #pieChart {
                  max-width: none !important;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: 0 auto;
                  counter-reset: break-point 80;
                }
                #pieChart svg {
                  overflow: visible;
                  transition: width 0.7s ease;
                }
                #pieChart path {
                  cursor: pointer;
                }
          
                #pieText {
                  overflow: visible;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                }
                #pieText .panel {
                  background-color: #111;
                  border: groove 4px #222;
                  box-shadow: 8px 10px 20px #000, 4px 5px 25px #000;
                  width: 100%;
                  height: 60%;
                  border-radius: 7px;
                  padding: 0 3px;
                  float: left;
                  transition: height 0.7s ease, padding 0.7s ease;
                  perspective: 650px;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                }
          
                .content-wrapper {
                  overflow: hidden;
                  transform-style: preserve-3d;
                }
          
                #segmentTitle,
                #segmentText {
                  text-align: center;
                }
          
                #segmentTitle {
                  transition: padding 0.7s ease;
                  padding: 15px;
                  font-size: calc(19px + (35 - 19) * (100vw - 200px) / (1314 - 200));
                }
                @media screen and (max-width: 200px) {
                  #segmentTitle {
                    font-size: 19px;
                  }
                }
                @media screen and (min-width: 1314px) {
                  #segmentTitle {
                    font-size: 35px;
                  }
                }
          
                #segmentText {
                  width: 100%;
                  transition: padding 0.7s ease, line-height 0.7s ease;
                  padding: 15px 20px 20px;
                  font-size: calc(14px + (21 - 14) * (100vw - 200px) / (1314 - 200));
                  line-height: 28px;
                }
                @media screen and (max-width: 200px) {
                  #segmentText {
                    font-size: 14px;
                  }
                }
                @media screen and (min-width: 1314px) {
                  #segmentText {
                    font-size: 21px;
                  }
                }
          
                @media (max-width: 360px) {
                  div.pie-container {
                    padding: 10px 12%;
                  }
                  div.pie-container #pieChart {
                    counter-reset: break-point 40;
                  }
                  div.pie-container #pieChart svg {
                    width: 100%;
                  }
                  div.pie-container #pieText {
                    margin-top: 15px;
                  }
                  div.pie-container #pieText .panel {
                    height: 230px;
                    padding: 0;
                  }
                  div.pie-container #pieText .panel #segmentTitle {
                    padding: 2px 4px 4px;
                  }
                  div.pie-container #pieText .panel #segmentText {
                    padding: 2px 4px 4px;
                    line-height: 16px;
                  }
                }
                @media (min-width: 360.02px) and (max-width: 442px) {
                  div.pie-container {
                    padding: 10px 10%;
                  }
                  div.pie-container #pieChart {
                    counter-reset: break-point 53;
                  }
                  div.pie-container #pieChart svg {
                    width: 70%;
                  }
                  div.pie-container #pieText {
                    margin-top: 15px;
                  }
                  div.pie-container #pieText .panel {
                    height: 210px;
                    padding: 0;
                  }
                  div.pie-container #pieText .panel #segmentTitle {
                    padding: 4px;
                  }
                  div.pie-container #pieText .panel #segmentText {
                    padding: 4px;
                    line-height: 18px;
                  }
                }
                @media (min-width: 442.02px) and (max-width: 575.98px) {
                  div.pie-container {
                    padding: 10px 10%;
                  }
                  div.pie-container #pieChart {
                    counter-reset: break-point 60;
                  }
                  div.pie-container #pieChart svg {
                    width: 70%;
                  }
                  div.pie-container #pieText {
                    margin-top: 15px;
                  }
                  div.pie-container #pieText .panel {
                    height: 200px;
                    padding: 0;
                  }
                  div.pie-container #pieText .panel #segmentTitle {
                    padding: 4px;
                  }
                  div.pie-container #pieText .panel #segmentText {
                    padding: 0 4px 4px;
                    line-height: 21px;
                  }
                }
                @media (min-width: 576px) and (max-width: 637.98px) {
                  div.pie-container {
                    padding: 10px 12%;
                  }
                  div.pie-container #pieChart {
                    counter-reset: break-point 70;
                  }
                  div.pie-container #pieChart svg {
                    width: 60%;
                  }
                  div.pie-container #pieText {
                    margin-top: 20px;
                  }
                  div.pie-container #pieText .panel {
                    padding: 0;
                    height: 205px;
                  }
                  div.pie-container #pieText .panel #segmentTitle {
                    padding: 8px;
                  }
                  div.pie-container #pieText .panel #segmentText {
                    padding: 0 8px 8px;
                    line-height: 24px;
                  }
                }
                @media (min-width: 638px) and (max-width: 767px) {
                  div.pie-container {
                    padding: 10px 15%;
                  }
                  div.pie-container #pieChart {
                    counter-reset: break-point 80;
                  }
                  div.pie-container #pieChart svg {
                    width: 60%;
                  }
                  div.pie-container #pieText {
                    margin-top: 20px;
                  }
                  div.pie-container #pieText .panel {
                    padding: 0 3px;
                    height: 270px;
                  }
                  div.pie-container #pieText .panel #segmentTitle {
                    padding: 15px;
                  }
                  div.pie-container #pieText .panel #segmentText {
                    padding: 15px;
                    line-height: 27px;
                  }
                }
                @media (min-width: 767.02px) and (max-width: 965px) {
                  div.pie-container {
                    padding: 10px 4%;
                  }
                  div.pie-container #pieChart {
                    counter-reset: break-point 50;
                  }
                  div.pie-container #pieText .panel {
                    height: 82%;
                    padding: 0;
                  }
                  div.pie-container #pieText .panel #segmentTitle {
                    padding: 8px;
                  }
                  div.pie-container #pieText .panel #segmentText {
                    padding: 0 8px 8px;
                    line-height: 23px;
                  }
                }
                @media (min-width: 965.02px) and (max-width: 1314px) {
                  div.pie-container {
                    padding: 10px 5%;
                  }
                  div.pie-container #pieChart {
                    counter-reset: break-point 60;
                  }
                  div.pie-container #pieText .panel {
                    height: 70%;
                  }
                  div.pie-container #pieText .panel #segmentTitle {
                    padding: 10px;
                  }
                  div.pie-container #pieText .panel #segmentText {
                    padding: 5px 10px 10px;
                  }
                }
                @media (min-width: 1314.02px) and (max-width: 1480px) {
                  div.pie-container {
                    padding: 10px 8%;
                  }
                  div.pie-container #pieChart {
                    counter-reset: break-point 70;
                  }
                  div.pie-container #pieText .panel #segmentText {
                    padding-top: 10px;
                  }
                }
              </code></pre>
            </div>
            <div id="tab04" class="tab-contents">
<pre class="line-numbers"><code class="language-javascript">
    var data = [
      {
        Title: "HTML",
        Amount: 100,
        Description:
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent rutrum metus vel odio convallis condimentum. Integer ullamcorper ipsum vel dui varius congue. Nulla facilisi. Morbi molestie tortor libero, ac placerat urna mollis ac. Vestibulum id ipsum mauris.",
      },
      {
        Title: "CSS",
        Amount: 1000,
        Description:
          "In hac habitasse platea dictumst. Curabitur lacus neque, congue ac quam a, sagittis accumsan mauris. Suspendisse et nisl eros. Fusce nulla mi, tincidunt non faucibus vitae, aliquam vel dolor. Maecenas imperdiet, elit eget condimentum fermentum.",
      },
      {
        Title: "Javascript",
        Amount: 1750,
        Description:
          "Aenean faucibus, risus sed eleifend rutrum, leo diam porttitor mauris, a eleifend ipsum ipsum ac ex. Nam scelerisque feugiat augue ac porta. Morbi massa ante, interdum sed nulla nec, finibus cursus augue. Phasellus nunc neque, blandit a nunc ut, mattis elementum arcu.",
      },
      {
        Title: "JSON",
        Amount: 600,
        Description:
          "Laboriosam pariatur recusandae ipsum nisi, saepe doloremque nobis eaque omnis commodi dolor porro? Error, deserunt veritatis officiis porro libero et suscipit ad. Ipsum dolor sit amet consectetur adipisicing elit.",
      },
      {
        Title: "React JSX",
        Amount: 2000,
        Description:
          "Sit amet consectetur adipisicing elit. Nemo totam perspiciatis tenetur quod ipsam voluptas et consequatur labore harum obcaecati alias voluptate id sit, praesentium ratione nostrum maxime reprehenderit. Deserunt veritatis officiis porro libero et suscipit ad.",
      },
      {
        Title: "WebGL",
        Amount: 100,
        Description:
          "Consectetur adipisicing elit. Architecto illum quidem eligendi, consectetur corporis esse enim eveniet distinctio beatae dignissimos recusandae, ipsam aspernatur labore cupiditate, suscipit corrupti accusantium voluptates laborum.",
      },
      {
        Title: "Express",
        Amount: 750,
        Description:
          "Beatae, aperiam voluptas aut atque laborum dolorem fuga. Corporis aperiam, illo nobis suscipit perferendis natus doloremque id ratione modi, veritatis beatae maiores Lorem ipsum dolor sit, amet consectetur.",
      },
      {
        Title: "Pug",
        Amount: 400,
        Description:
          "Amet consectetur, adipisicing elit. Ipsum perferendis rem illo explicabo voluptate, voluptatum id expedita sapiente magni laboriosam maiores molestiae ut accusamus impedit quos ex excepturi porro voluptates. Nunc dolor metus, aliquam vitae felis id, euismod vulputate metus.",
      },
      {
        Title: "Sass",
        Amount: 800,
        Description:
          "Architecto illum quidem eligendi, consectetur corporis esse enim eveniet distinctio beatae dignissimos recusandae, ipsam aspernatur labore cupiditate, suscipit corrupti accusantium voluptates laborum.",
      },
      {
        Title: "MongoDB",
        Amount: 450,
        Description:
          "Dolor sit amet consectetur adipisicing elit. Beatae, aperiam. Voluptas aut atque laborum dolorem fuga? Corporis aperiam, illo nobis suscipit perferendis natus doloremque id ratione modi, veritatis beatae maiores.",
      },
      {
        Title: "Bootstrap",
        Amount: 450,
        Description:
          "Recusandae ipsum nisi, saepe doloremque nobis eaque omnis commodi dolor porro? Error, deserunt veritatis officiis porro libero et suscipit ad. Dolor sit amet consectetur adipisicing elit. Nemo totam perspiciatis tenetur quod ipsam voluptas et consequatur labore harum obcaecati alias voluptate.",
      },
    ];

    var width = parseInt(d3.select("#pieChart").style("width"), 10);
    var height = width;
    var radius = (Math.min(width, height) - 15) / 2;

    var total = 0; // used to calculate %s
    data.forEach((d) => {
      total += d.Amount;
    });

    var title = function getObject(obj) {
      titles = [];
      for (var i = 0; i < obj.length; i++) {
        titles.push(obj[i].Title);
      }
      return titles;
    };

    // grabs the responsive value in 'counter-reset' css value
    let innerRadius = $("#pieChart").css("counter-reset").split(" ")[1];

    var arcOver = d3
      .arc()
      .outerRadius(radius + 10)
      .innerRadius(innerRadius);

    var color = d3.scaleOrdinal();
    color
      .domain(title(data))
      // Comment in/out between ranges below to change colors
      .range([
        "#00b19c",
        "#3bcd3f",
        "#007365",
        "#8dbac4",
        "#002035",
        "#33c1b0",
        "#62d765",
        "#338f84",
        "#a4c8d0",
        "#334d5d",
        "#66d0c4",
        "#89e18c",
        "#66aba3",
        "#bbd6dc",
        "#667986",
      ]);

    // Comment Out Below to Use Diff D3 Color Scheme
    // var color = d3.scaleOrdinal(d3.schemeCategory10);
    // color.domain(title(data))

    var arc = d3
      .arc()
      .outerRadius(radius - 10)
      .innerRadius(innerRadius);

    var pie = d3
      .pie()
      .sort(null)
      .value(function (d) {
        return +d.Amount;
      });

    // direction of the slice angle (for responsiveness)
    let sliceDirection = 90;
    if (window.matchMedia("(max-width: 767px)").matches) {
      sliceDirection = 180;
    }

    var prevSegment = null;
    var change = function (d, i) {
      var angle =
        sliceDirection -
        (d.startAngle * (180 / Math.PI) +
          ((d.endAngle - d.startAngle) * (180 / Math.PI)) / 2);

      svg
        .transition()
        .duration(1000)
        .attr(
          "transform",
          "translate(" + radius + "," + height / 2 + ") rotate(" + angle + ")"
        );
      d3.select(prevSegment).transition().attr("d", arc).style("filter", "");
      prevSegment = i;

      d3.select(i)
        .transition()
        .duration(1000)
        .attr("d", arcOver)
        .style("filter", "url(#drop-shadow)");
    };

    var svg = d3
      .select("#pieChart")
      .append("svg")
      .attr("width", "100%")
      .attr("height", "100%")
      .attr(
        "viewBox",
        "0 0 " + Math.min(width, height) + " " + Math.min(width, height)
      )
      .attr("preserveAspectRatio", "xMinYMin")
      .append("g")
      .attr("transform", "translate(" + radius + "," + height / 2 + ")")
      .style("filter", "url(#drop-shadow)");

    // Create Drop Shadow on Pie Chart
    var defs = svg.append("defs");
    var filter = defs
      .append("filter")
      .attr("id", "drop-shadow")
      .attr("height", "130%");

    filter
      .append("feGaussianBlur")
      .attr("in", "SourceAlpha")
      .attr("stdDeviation", 5.5)
      .attr("result", "blur");

    filter
      .append("feOffset")
      .attr("in", "blur")
      .attr("dx", 0)
      .attr("dy", 0)
      .attr("result", "offsetBlur");

    var feMerge = filter.append("feMerge");
    feMerge.append("feMergeNode").attr("in", "offsetBlur");
    feMerge.append("feMergeNode").attr("in", "SourceGraphic");

    // toggle to allow animation to halfway finish before switching segment again
    var buttonToggle = true;
    var switchToggle = () => {
      setTimeout(() => {
        buttonToggle = true;
      }, 1500);
    };

    var timeline = new TimelineLite();

    var g = svg
      .selectAll("path")
      .data(pie(data))
      .enter()
      .append("path")
      .style("fill", function (d) {
        return color(d.data.Title);
      })
      .attr("d", arc)
      .style("fill", function (d) {
        return color(d.data.Title);
      })
      .on("click", function (d) {
        if (buttonToggle) {
          buttonToggle = false;
          switchToggle();

          change(d, this);

          var timeline = new TimelineLite();

          //TweenMax.set(".panel", {perspective:800});
          //TweenMax.set(".content-wrapper", {transformStyle:"preserve-3d"});

          timeline
            .to(".content-wrapper", 0.5, {
              rotationX: "90deg",
              opacity: 0,
              ease: Linear.easeNone,
              onComplete: () => {
                $(".content-wrapper").hide();
              },
            })
            .to(".panel", 0.5, {
              width: "0%",
              opacity: 0.05,
              ease: Linear.easeNone,
              onComplete: () => {
                $("#segmentTitle").replaceWith(
                  `<h1 id="segmentTitle">${d.data.Title} - ${
                    Math.round((d.data.Amount / total) * 1000) / 10
                  }%</h1>`
                );
                $("#segmentText").replaceWith(
                  '<p id="segmentText">' + d.data.Description + "</p>"
                );
                $(".panel").css(
                  "background-color",
                  `${ColorLuminance(color(d.data.Title), -0.3)}`
                );
              },
            });

          timeline
            .to(".panel", 0.5, {
              width: "100%",
              opacity: 1,
              ease: Linear.easeNone,
              onComplete: () => {
                $(".content-wrapper").show();
              },
            })
            .to(".content-wrapper", 0.5, {
              rotationX: "0deg",
              opacity: 1,
              ease: Linear.easeNone,
            });
        }
      });

    timeline
      .from("#pieChart", 0.5, {
        rotation: "-120deg",
        scale: 0.1,
        opacity: 0,
        ease: Power3.easeOut,
      })
      .from(
        ".panel",
        0.75,
        {
          width: "0%",
          opacity: 0,
          ease: Linear.easeNone,
          onComplete: () => {
            $(".content-wrapper").show();
          },
        },
        "+=.55"
      )
      .from(".content-wrapper", 0.75, {
        rotationX: "-90deg",
        opacity: 0,
        ease: Linear.easeNone,
      });

    // Function to darken Hex colors
    function ColorLuminance(hex, lum) {
      // validate hex string
      hex = String(hex).replace(/[^0-9a-f]/gi, "");
      if (hex.length < 6) {
        hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
      }
      lum = lum || 0;

      // convert to decimal and change luminosity
      var rgb = "#",
        c,
        i;
      for (i = 0; i < 3; i++) {
        c = parseInt(hex.substr(i * 2, 2), 16);
        c = Math.round(Math.min(Math.max(0, c + c * lum), 255)).toString(16);
        rgb += ("00" + c).substr(c.length);
      }

      return rgb;
    }
  </code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
