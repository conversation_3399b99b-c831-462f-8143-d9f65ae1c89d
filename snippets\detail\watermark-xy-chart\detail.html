<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Component Library</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">
      <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Watermark XY Chart</h4>
              <div class="mb-2">
                <a
                  href="./downloads/watermark-xy-chart.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/watermark-xy-chart/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
              <pre class="line-numbers">
<code class="language-markup">&lt;div id="chartdiv"&gt;&lt;/div&gt;</code>
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">#chartdiv {
  width: 100%;
  height: 500px;
}

</code></pre>
            </div>
            <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">var root = am5.Root.new("chartdiv");
                root.setThemes([
                    am5themes_Animated.new(root)
                ]);
                var chart = root.container.children.push(am5xy.XYChart.new(root, {
                    panX: true,
                    panY: true,
                    wheelX: "panX",
                    wheelY: "zoomX",
                    pinchZoomX: true,
                    layout: root.verticalLayout,
                }));
        
                chart.children.unshift(
                    am5.Label.new(root, {
                        text: "Watermark in chart",
                        fontSize: 12,
                        fontWeight: "bold",
                        textAlign: "center",
                        x: am5.percent(50),
                        centerX: am5.percent(50),
                    })
                );
        
                // Generate random data
                var date = new Date();
                date.setHours(0, 0, 0, 0);
                var value = 100;
        
                function generateData() {
                    value = Math.round((Math.random() * 10 - 5) + value);
                    am5.time.add(date, "day", 1);
                    return {
                        date: date.getTime(),
                        value: value
                    };
                }
        
                function generateDatas(count) {
                    var data = [];
                    for (var i = 0; i < count; ++i) {
                        data.push(generateData());
                    }
                    return data;
                }
        
        
                var xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
                    maxDeviation: 0.2,
                    baseInterval: {
                        timeUnit: "day",
                        count: 1
                    },
                    renderer: am5xy.AxisRendererX.new(root, {
                        strokeOpacity: 1,
                        strokeWidth: 1,
                    }),
                    tooltip: am5.Tooltip.new(root, {})
                }));
                var xRenderer = xAxis.get("renderer");
                xRenderer.labels.template.setAll({
                    rotation: -60,
                    centerY: am5.p50,
                });
                xRenderer.grid.template.setAll({
                    stroke: am5.color(0xfff),
                    strokeWidth: 0,
                    strokeOpacity: 0,
                });
                xRenderer.labels.template.setAll({
                    fontSize: "12px",
                });
        
                var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                    renderer: am5xy.AxisRendererY.new(root, {
                        strokeOpacity: 1,
                        strokeWidth: 1,
                    })
                }));
                var yRenderer1 = yAxis.get("renderer");
                yRenderer1.grid.template.setAll({
                    stroke: am5.color(0xfff),
                    strokeWidth: 0,
                    strokeOpacity: 0,
                });
                yRenderer1.labels.template.setAll({
                    fontSize: "12px",
                });
        
                var series = chart.series.push(am5xy.LineSeries.new(root, {
                    name: "Series",
                    xAxis: xAxis,
                    yAxis: yAxis,
                    valueYField: "value",
                    valueXField: "date",
                    strokeWidth: 3,
                    fill: am5.color("#00b19c"),
                    stroke: am5.color("#00b19c"),
                    tooltip: am5.Tooltip.new(root, {
                        labelText: "{valueY}"
                    }),
        
                }));
        
        
        
                // Set data
                var data = generateDatas(1200);
                series.data.setAll(data);
        
                series.appear(1000);
                chart.appear(1000, 100);
        
                // Adding watermarks
                var copyright = chart.plotContainer.children.push(am5.Label.new(root, {
                    text: "Copyright thesmartcube.com",
                    x: 10,
                    y: am5.p100,
                    centerY: am5.p100,
                    dy: -10
                }));
        
                var logo = chart.plotContainer.children.push(am5.Picture.new(root, {
                    src: "data:image/jpeg;base64,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",
                    width: 100,
                    x: am5.p100,
                    centerX: am5.p100,
                    dx: -10,
                    dy: -10,
                    y: am5.p100,
                    centerY: am5.p100
                }));</code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
