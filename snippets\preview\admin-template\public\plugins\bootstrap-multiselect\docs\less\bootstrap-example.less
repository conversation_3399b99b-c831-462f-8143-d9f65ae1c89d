.example {
    position: relative;
    padding: 45px 15px 15px;
    margin: 0 -15px 15px;
    background-color: #fafafa;
    box-shadow: inset 0 3px 6px rgba(0,0,0,.05);
    border-color: #e5e5e5 #eee #eee;
    border-style: solid;
    border-width: 1px 0;
}

.example:after {
    content: "Example";
    position: absolute;
    top:  15px;
    left: 15px;
    font-size: 12px;
    font-weight: bold;
    color: #bbb;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.example + .highlight {
    margin: -15px -15px 15px;
    border-radius: 0;
    border-width: 0 0 1px;
}

@media (min-width: 768px) {
    .example {
        margin-left: 0;
        margin-right: 0;
        background-color: #fff;
        border-width: 1px;
        border-color: #ddd;
        border-radius: 4px 4px 0 0;
        box-shadow: none;
    }
    .example + .highlight {
        margin-top: -16px;
        margin-left: 0;
        margin-right: 0;
        border-width: 1px;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
    }
}

.example > .btn,
.example > .btn-group {
    margin-top: 5px;
    margin-bottom: 5px;
}