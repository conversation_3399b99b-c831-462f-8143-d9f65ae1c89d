
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    
    <meta name="author" content="Vimal Thapliyal">
    
    <title>Component Library</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
    <!-- Bootstrap core CSS -->
<link href="./css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="./css/uikit.css">
<link rel="stylesheet" href="./css/prism.css"/>

<!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet">
    <style>

#tab-button {
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}
#tab-button li {
  display: table-cell;
  width: 20%;
}
#tab-button li a {
  display: block;
  padding: .5em;
  background: #eee;
  border: 1px solid #ddd;
  text-align: center;
  color: #000;
  text-decoration: none;
}
#tab-button li:not(:first-child) a {
  border-left: none;
}
#tab-button li a:hover,
#tab-button .is-active a {
  border-bottom-color: transparent;
  background: #fff;
}
.tab-contents {
  padding: .5em 2em 1em;
  border: 1px solid #ddd;
}



.tab-button-outer {
  display: none;
}
.tab-contents {
  margin-top: 20px;
}
@media screen and (min-width: 768px) {
  .tab-button-outer {
    position: relative;
    z-index: 2;
    display: block;
  }
  .tab-select-outer {
    display: none;
  }
  .tab-contents {
    position: relative;
    top: -1px;
    margin-top: 0;
  }
}




code[class*="language-"],
pre[class*="language-"] {
	color: black;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', monospace;
	direction: ltr;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
	color: #a67f59;
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function {
	color: #DD4A68;
}

.token.regex,
.token.important {
	color: #e90;
}

.token.important {
	font-weight: bold;
}

.token.entity {
	cursor: help;
}

pre.line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
}

pre.line-numbers > code {
	position: relative;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

}

	.line-numbers-rows > span {
		pointer-events: none;
		display: block;
		counter-increment: linenumber;
	}

		.line-numbers-rows > span:before {
			content: counter(linenumber);
			color: #999;
			display: block;
			padding-right: 0.8em;
			text-align: right;
    }
    


    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
        <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
          <i class="fas fa-bars"></i>
        </span>
        <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
          <i class="fas fa-bars"></i>
        </span>
      </div>

<main role="main">
  <div class="container-fluid mt-1">
    <div class="row">
        <script src="./menuNav.js"></script>
    <div id="rightSection"  class="col-sm-10">
      <div class="tabs">
        <div class="tab-button-outer">
          <ul id="tab-button">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
           
            <!-- <li><a href="#tab04">Javascript</a></li> -->
          </ul>
        </div>
        <div class="tab-select-outer">
          <select id="tab-select">
            <li><a href="#tab01">Preview</a></li>
            <li><a href="#tab02">HTML</a></li>
          
          </select>
        </div>
      
        <div id="tab01" class="tab-contents">
          <iframe style="width:100%;border:none;min-height:1006px; height: auto;" width="100%" height="100%" src="./snippets/preview/globe/index.html"></iframe>
        </div>
        <div id="tab02" class="tab-contents">
          
<pre class="line-numbers">
<code class="language-markup">
  &lt;!doctype html&gt;
  &lt;html&gt;
  
  &lt;head&gt;
      &lt;meta charset="utf-8"&gt;
      &lt;title&gt;TheSmartCube&lt;/title&gt;
      &lt;link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css"
          integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous"&gt;
      &lt;style type="text/css"&gt;
          #chartdiv {
              width: 100%;
              height: 580px;
              max-width: 100%;
  
          }
  
          .card-section table td {
              /* border-top: 4px solid #0066cc; */
              border-top: none !important;
  
          }
      &lt;/style&gt;
  &lt;/head&gt;
  
  &lt;body&gt;
      &lt;script src="https://cdn.amcharts.com/lib/4/core.js"&gt;&lt;/script&gt;
      &lt;script src="https://cdn.amcharts.com/lib/4/maps.js"&gt;&lt;/script&gt;
      &lt;script src="https://cdn.amcharts.com/lib/4/geodata/worldHigh.js"&gt;&lt;/script&gt;
      &lt;script src="https://cdn.amcharts.com/lib/4/geodata/worldLow.js"&gt;&lt;/script&gt;
      &lt;script src="https://cdn.amcharts.com/lib/4/themes/animated.js"&gt;&lt;/script&gt;
  
      &lt;div id="chartdiv"&gt;&lt;/div&gt;
      &lt;script&gt;
          var dataJSON = [
              {
                  "color": "#999",
                  "data": [
                      {
                          "title": "Austria",
                          "id": "AT",
                          "databases": "1995",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Australia",
                          "id": "AU",
                          "databases": "1995",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Zambia",
                          "id": "ZM",
                          "databases": "1995",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Algeria",
                          "id": "DZ",
                          "databases": "1995",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Brazil",
                          "id": "BR",
                          "databases": "1995",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "United States",
                          "id": "US",
                          "databases": "1995",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Russia",
                          "id": "RU",
                          "databases": "1995",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Nigeria",
                          "id": "NG",
                          "databases": "1995",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "SUDAN",
                          "id": "SD",
                          "databases": "1995",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Ireland",
                          "id": "IE",
                          "databases": "1973",
                          "therpeuticAreas": "22"
                      }, {
                          "title": "Denmark",
                          "id": "DK",
                          "databases": "1973",
                          "therpeuticAreas": "2221"
                      }, {
                          "title": "Finland",
                          "id": "FI",
                          "databases": "1995",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Sweden",
                          "id": "SE",
                          "databases": "1995",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Great Britain",
                          "id": "GB",
                          "databases": "1973",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Italy",
                          "id": "IT",
                          "databases": "1957",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "France",
                          "id": "FR",
                          "databases": "1957",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Spain",
                          "id": "ES",
                          "databases": "1986",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Greece",
                          "id": "GR",
                          "databases": "1981",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Germany",
                          "id": "DE",
                          "databases": "1957",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Belgium",
                          "id": "BE",
                          "databases": "1957",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Luxembourg",
                          "id": "LU",
                          "databases": "1957",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Netherlands",
                          "id": "NL",
                          "databases": "1957",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Portugal",
                          "id": "PT",
                          "databases": "1986",
                          "therpeuticAreas": "2004"
                      },
                      {
                          "title": "Lithuania",
                          "id": "LT",
  
                          "databases": "2004",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Latvia",
                          "id": "LV",
  
                          "databases": "2004",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Czech Republic ",
                          "id": "CZ",
  
                          "databases": "2004",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Slovakia",
                          "id": "SK",
  
                          "databases": "2004",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Slovenia",
                          "id": "SI",
  
                          "databases": "2004",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Estonia",
                          "id": "EE",
  
                          "databases": "2004",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Hungary",
                          "id": "HU",
  
                          "databases": "2004",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Cyprus",
                          "id": "CY",
  
                          "databases": "2004",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Malta",
                          "id": "MT",
  
                          "databases": "2004",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Poland",
                          "id": "PL",
  
                          "databases": "2004",
                          "therpeuticAreas": "2004"
                      },
                      {
                          "title": "Romania",
                          "id": "RO",
                          "databases": "2007",
                          "therpeuticAreas": "2004"
                      }, {
                          "title": "Bulgaria",
                          "id": "BG",
                          "databases": "2007",
                          "therpeuticAreas": "2004"
                      },
                      {
                          "title": "Croatia",
                          "id": "HR",
                          "databases": "2013",
                          "therpeuticAreas": "2004"
                      },
                      {
                          "title": "INDIA",
                          "id": "IN",
                          "databases": "2013",
                          "therpeuticAreas": "2004"
                      }
                  ]
              }
          ];
  
  
  
  
  
          // Themes begin
          am4core.useTheme(am4themes_animated);
          // Themes end
  
          var chart = am4core.create("chartdiv", am4maps.MapChart);
  
          // Set map definition
          chart.geodata = am4geodata_worldLow;
          // This array will be populated with country IDs to exclude from the world series
          var excludedCountries = ["AQ"];
          // Create map polygon series
          var polygonSeries = chart.series.push(new am4maps.MapPolygonSeries());
  
          // Make map load polygon (like country names) data from GeoJSON
          polygonSeries.useGeodata = true;
  
          // Configure series
          var polygonTemplate = polygonSeries.mapPolygons.template;
          polygonTemplate.tooltipText = "{name}";
          polygonTemplate.fill = am4core.color("#cccccc");
          //polygonTemplate.stroke = am4core.color("#454a58");
          //polygonTemplate.strokeWidth = 0.5;
  
          var graticuleSeries = chart.series.push(new am4maps.GraticuleSeries());
          graticuleSeries.mapLines.template.line.stroke = am4core.color("#ffffff");
          graticuleSeries.mapLines.template.line.strokeOpacity = 0.08;
          graticuleSeries.fitExtent = false;
  
  
          chart.backgroundSeries.mapPolygons.template.polygon.fillOpacity = 1;
          chart.backgroundSeries.mapPolygons.template.polygon.fill = am4core.color("#336091");
          // Create a series for each group, and populate the above array
          createData(dataJSON, '#47c78a');
          function createData(data, color) {
  
              data.forEach(function (group) {
                  var series = chart.series.push(new am4maps.MapPolygonSeries());
                  series.useGeodata = true;
                  var includedCountries = [];
                  group.data.forEach(function (country) {
                      includedCountries.push(country.id);
                      excludedCountries.push(country.id);
                  });
                  series.include = includedCountries;
  
                  series.fill = am4core.color(color);
  
                  // By creating a hover state and setting setStateOnChildren to true, when we
                  // hover over the series itself, it will trigger the hover SpriteState of all
                  // its countries (provided those countries have a hover SpriteState, too!).
                  series.setStateOnChildren = true;
                  series.calculateVisualCenter = true;
  
                  // Country shape properties & behaviors
                  var mapPolygonTemplate = series.mapPolygons.template;
                  // Instead of our custom title, we could also use {name} which comes from geodata  
                  mapPolygonTemplate.fill = am4core.color(color);
                  mapPolygonTemplate.fillOpacity = 0.8;
                  mapPolygonTemplate.nonScalingStroke = true;
                  mapPolygonTemplate.tooltipPosition = "fixed"
  
                  mapPolygonTemplate.events.on("over", function (event) {
                      series.mapPolygons.each(function (mapPolygon) {
                          // mapPolygon.isHover = true;
                      })
                      // event.target.isHover = false;
                      //event.target.isHover = true;
                  })
  
                  mapPolygonTemplate.events.on("out", function (event) {
                      series.mapPolygons.each(function (mapPolygon) {
                          mapPolygon.isHover = false;
                      })
                  })
  
                  // States  
                  var hoverState = mapPolygonTemplate.states.create("hover");
                  hoverState.properties.fill = am4core.color("#0066cc");
  
  
                  // Tooltip
                  mapPolygonTemplate.tooltipHTML = `&lt;div class='card-section' style="color:#000;font-size:12px;"&gt;
      &lt;table class="table table-sm mb-0 mt-0 p-2" style="font-size:10px; background:#fff;"&gt;
          &lt;tbody&gt;
              &lt;tr style="border-bottom:2px solid #0066cc;"&gt;
                  &lt;td colspan="2"  style="font-size:14px;"&gt;&lt;span class="flag-icon flag-icon-{id}"&gt;&lt;/span&gt; &lt;strong&gt;{name}&lt;/strong&gt;&lt;/td&gt;
              &lt;/tr&gt;
              &lt;tr&gt;
                  &lt;td style="font-size:10px;"&gt;DATABASES&lt;/td&gt;
                  &lt;td style="font-size:10px;"&gt;&lt;strong&gt;{databases}&lt;/strong&gt;&lt;/td&gt;
              &lt;/tr&gt;
              &lt;tr&gt;
                  &lt;td style="font-size:10px;"&gt;THERAPEUTIC AREA&lt;/td&gt;
                  &lt;td style="font-size:10px;"&gt;&lt;strong&gt;{therpeuticAreas}&lt;/strong&gt;&lt;/td&gt;
              &lt;/tr&gt;
          &lt;/tbody&gt;
      &lt;/table&gt;
       
    &lt;/div&gt;&lt;/div&gt;
    &lt;a class="text-center" style="display: block;float: right;font-weight: bold;font-size:10px;padding-bottom:5px;" href="map-internal.html?name={name}"&gt; More info &lt;i class="fas fa-external-link-alt"&gt;&lt;/i&gt;&lt;/a&gt;
    `;
  
                  series.calculateVisualCenter = true;
                  series.tooltipPosition = "fixed";
                  series.tooltip.label.interactionsEnabled = true;
                  series.tooltip.keepTargetHover = true;
                  series.tooltip.getFillFromObject = false;
                  series.tooltip.background.fill = am4core.color("#fff");
                  series.tooltip.background.fillOpacity = 1;
                  series.tooltip.background.strokeOpacity = 0;
  
                  am4core.options.autoSetClassName = true;
  
  
                  series.data = JSON.parse(JSON.stringify(group.data));
  
  
  
  
  
              });
          }
          function selectCountry(id) {
              if (id.length &gt; 0) {
                  // our json Data
                  var originalData = dataJSON[0].data;
                  var updatedData = [];
                  var mainArray = [{
                      "color": "#999",
                      "data": []
                  }];
                  var updatedData = [];
  
                  for (var x = 0; x &lt; id.length; x++) {
                      for (var i = 0; i &lt; originalData.length; i++) {
                          if (originalData[i].id == id[x] && originalData[i].id !== undefined) {
                              updatedData.push(originalData[i]);
                          }
                      }
                  }
  
                  mainArray[0].data = updatedData;
                  createData(mainArray, '#add8e6');
              }
  
  
          }
  
          // Set projection
          chart.projection = new am4maps.projections.Orthographic();
          chart.panBehavior = "rotateLong";
          chart.deltaLatitude = -20;
          chart.padding(20, 20, 20, 20);
  
          // limits vertical rotation
          chart.adapter.add("deltaLatitude", function (delatLatitude) {
              return am4core.math.fitToRange(delatLatitude, -90, 90);
          })
  
  
  
          // Create hover state and set alternative fill color
          //var hs = polygonTemplate.states.create("hover");
          //hs.properties.fill = chart.colors.getIndex(0).brighten(-0.5);
  
          let animation;
          setTimeout(function () {
              animation = chart.animate({ property: "deltaLongitude", to: 100000 }, 9999990);
          }, 2000)
  
          chart.seriesContainer.events.on("down", function () {
              if (animation) {
                  animation.stop();
              }
          })
      &lt;/script&gt;
  &lt;/body&gt;
  
  &lt;/html&gt;

</code>
</pre>
           
        </div>
        
        <!-- <div id="tab04" class="tab-contents">
            <pre class="line-numbers"><code class="language-javascript">

            </code></pre>
        </div> -->
       
      </div>
    </div>
  </div></div>




 

</main>

<footer class="text-muted">
  <div class="container"><p class="float-right">
      <a href="#">Back to top</a>
    </p>
   
  
  </div>
</footer>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
      <script>window.jQuery || document.write('<script src="js/jquery.slim.min.js"><\/script>')</script><script src="./js/bootstrap.bundle.js" ></script>
      <script src="./js/prism.js"></script>
      <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
      <script>
        $(function() {
  var $tabButtonItem = $('#tab-button li'),
      $tabSelect = $('#tab-select'),
      $tabContents = $('.tab-contents'),
      activeClass = 'is-active';

  $tabButtonItem.first().addClass(activeClass);
  $tabContents.not(':first').hide();

  $tabButtonItem.find('a').on('click', function(e) {
    var target = $(this).attr('href');

    $tabButtonItem.removeClass(activeClass);
    $(this).parent().addClass(activeClass);
    $tabSelect.val(target);
    $tabContents.hide();
    $(target).show();
    e.preventDefault();
  });

  $tabSelect.on('change', function() {
    var target = $(this).val(),
        targetSelectNum = $(this).prop('selectedIndex');

    $tabButtonItem.removeClass(activeClass);
    $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
    $tabContents.hide();
    $(target).show();
  });
});
      </script>
    </body>
</html>
