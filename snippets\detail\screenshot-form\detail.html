
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <meta name="author" content="Vimal Thapliyal" />

    <title>Feedback form with screenshot</title>
    <link
      href="https://fonts.googleapis.com/css?family=Fira+Sans"
      rel="stylesheet"
    />
    <!-- Bootstrap core CSS -->
    <link href="./css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/uikit.css" />
    <link rel="stylesheet" href="./css/prism.css" />

    <!-- Custom styles for this template -->
    <link href="./css/default.css" rel="stylesheet" />
    <style>
      #tab-button {
        display: table;
        table-layout: fixed;
        width: 100%;
        margin: 0;
        padding: 0;
        list-style: none;
      }
      #tab-button li {
        display: table-cell;
        width: 20%;
      }
      #tab-button li a {
        display: block;
        padding: 0.5em;
        background: #eee;
        border: 1px solid #ddd;
        text-align: center;
        color: #000;
        text-decoration: none;
      }
      #tab-button li:not(:first-child) a {
        border-left: none;
      }
      #tab-button li a:hover,
      #tab-button .is-active a {
        border-bottom-color: transparent;
        background: #fff;
      }
      .tab-contents {
        padding: 0.5em 2em 1em;
        border: 1px solid #ddd;
      }

      .tab-button-outer {
        display: none;
      }
      .tab-contents {
        margin-top: 20px;
      }
      @media screen and (min-width: 768px) {
        .tab-button-outer {
          position: relative;
          z-index: 2;
          display: block;
        }
        .tab-select-outer {
          display: none;
        }
        .tab-contents {
          position: relative;
          top: -1px;
          margin-top: 0;
        }
      }

      code[class*="language-"],
      pre[class*="language-"] {
        color: black;
        text-shadow: 0 1px white;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        direction: ltr;
        text-align: left;
        white-space: pre;
        word-spacing: normal;
        word-break: normal;
        line-height: 1.5;

        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;

        -webkit-hyphens: none;
        -moz-hyphens: none;
        -ms-hyphens: none;
        hyphens: none;
      }

      pre[class*="language-"]::-moz-selection,
      pre[class*="language-"] ::-moz-selection,
      code[class*="language-"]::-moz-selection,
      code[class*="language-"] ::-moz-selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      pre[class*="language-"]::selection,
      pre[class*="language-"] ::selection,
      code[class*="language-"]::selection,
      code[class*="language-"] ::selection {
        text-shadow: none;
        background: #b3d4fc;
      }

      @media print {
        code[class*="language-"],
        pre[class*="language-"] {
          text-shadow: none;
        }
      }

      /* Code blocks */
      pre[class*="language-"] {
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
      }

      :not(pre) > code[class*="language-"],
      pre[class*="language-"] {
        background: #f5f2f0;
      }

      /* Inline code */
      :not(pre) > code[class*="language-"] {
        padding: 0.1em;
        border-radius: 0.3em;
      }

      .token.comment,
      .token.prolog,
      .token.doctype,
      .token.cdata {
        color: slategray;
      }

      .token.punctuation {
        color: #999;
      }

      .namespace {
        opacity: 0.7;
      }

      .token.property,
      .token.tag,
      .token.boolean,
      .token.number,
      .token.constant,
      .token.symbol {
        color: #905;
      }

      .token.selector,
      .token.attr-name,
      .token.string,
      .token.char,
      .token.builtin {
        color: #690;
      }

      .token.operator,
      .token.entity,
      .token.url,
      .language-css .token.string,
      .style .token.string,
      .token.variable {
        color: #a67f59;
        background: hsla(0, 0%, 100%, 0.5);
      }

      .token.atrule,
      .token.attr-value,
      .token.keyword {
        color: #07a;
      }

      .token.function {
        color: #dd4a68;
      }

      .token.regex,
      .token.important {
        color: #e90;
      }

      .token.important {
        font-weight: bold;
      }

      .token.entity {
        cursor: help;
      }

      pre.line-numbers {
        position: relative;
        padding-left: 3.8em;
        counter-reset: linenumber;
      }

      pre.line-numbers > code {
        position: relative;
      }

      .line-numbers .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 0;
        font-size: 100%;
        left: -3.8em;
        width: 3em; /* works for line-numbers below 1000 lines */
        letter-spacing: -1px;
        border-right: 1px solid #999;

        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .line-numbers-rows > span {
        pointer-events: none;
        display: block;
        counter-increment: linenumber;
      }

      .line-numbers-rows > span:before {
        content: counter(linenumber);
        color: #999;
        display: block;
        padding-right: 0.8em;
        text-align: right;
      }
    </style>
  </head>
  <body>
    <script src="../../../header.js"></script>
    <div class="hamSection">
      <span id="closeMenu" class="close-menu-icon" onclick="closeNav()">
        <i class="fas fa-bars"></i>
      </span>
      <span id="openMenu"style="display: none;" class="close-menu-icon" onclick="openNav()">
        <i class="fas fa-bars"></i>
      </span>
    </div>

    <main role="main">  <div class="container-fluid mt-1">
        <div class="row">
          <script src="./menuNav.js"></script>
        <div id="rightSection" class="col-sm-10">
          <div class="col-sm-12 p-0">
            <div class="d-flex justify-content-between">
              <h4 class="size20 mb-0 pt-2">Feedback form with screenshot feature</h4>
              <div class="mb-2">
                <a
                  href="./downloads/screenshot-form.zip"
                  class="btn btn-sm btn-outline-primary btn-dark mr-2"
                  ><i class="fas fa-download"></i> Download zip</a
                >
                <button class="btn btn-sm btn-outline-info" id="fullScreen">
                  <i class="fas fa-expand"></i> View on Fullscreen
                </button>
              </div>
            </div>
          </div>
          <div class="tabs">
            <div class="tab-button-outer">
              <ul id="tab-button">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <li><a href="#tab03">CSS</a></li>
                <li><a href="#tab04">Javascript</a></li>
              </ul>
            </div>
            <div class="tab-select-outer">
              <select id="tab-select">
                <li><a href="#tab01">Preview</a></li>
                <li><a href="#tab02">HTML</a></li>
                <!-- <li><a href="#tab03">CSS</a></li> -->
                <!-- <li><a href="#tab04">Javascript</a></li> -->
              </select>
            </div>

            <div id="tab01" class="tab-contents">
              <iframe
                style="
                  width: 100%;
                  border: none;
                  min-height: 1006px;
                  height: auto;
                "
                width="100%"
                height="100%"
                src="./snippets/preview/screenshot-form/index.html"
              ></iframe>
            </div>
            <div id="tab02" class="tab-contents">
<pre class="line-numbers">
<code class="language-markup">&lt;div class="container"&gt;
  &lt;div class="row"&gt;
    &lt;div class="mx-auto col-6 mt-5 card card-body shadow-sm"&gt;
      &lt;div&gt;
        &lt;div&gt;
          &lt;div&gt;
            &lt;h2&gt;Submit feedback&lt;/h2&gt;
          &lt;/div&gt;
          &lt;div class="form-group"&gt;
            &lt;label&gt;What's your name?&lt;/label&gt;
            &lt;input class="form-control" type="text" id="name" /&gt;
          &lt;/div&gt;
          &lt;div class="form-group"&gt;
            &lt;label&gt;Describe the issue&lt;/label&gt;
            &lt;textarea
              class="form-control"
              type="text"
              rows="3"
              id="describe"
            &gt;&lt;/textarea&gt;
          &lt;/div&gt;
          &lt;div class="form-group"&gt;
            &lt;label
              &gt;A screenshot will help us better understand the issue.&lt;/label
            &gt;
          &lt;/div&gt;
          &lt;div class="form-group"&gt;
            &lt;button id="screenshot-button" class="bg-light py-2"&gt;
              &lt;div id="screenshot-button-notloading"&gt;Take Screenshot&lt;/div&gt;
            &lt;/button&gt;
            &lt;div id="screenshot-result" style="display: none"&gt;
              &lt;button id="screenshot-delete"&gt;
                &lt;i class="fas fa-trash-alt"&gt;&lt;/i&gt;
              &lt;/button&gt;
              &lt;div id="screenshot-edit-layer"&gt;
                &lt;button id="screenshot-edit"&gt;
                  &lt;i class="fas fa-edit"&gt;&lt;/i&gt;
                  Highlight or Hide Info
                &lt;/button&gt;
              &lt;/div&gt;
              &lt;canvas id="screenshot-canvas" style="width: 100%"&gt;&lt;/canvas&gt;
            &lt;/div&gt;
          &lt;/div&gt;

          &lt;a
            class="btn btn-sm btn-primary float-right d-inline-block"
            style="background-color: #00b19c !important; border-radius: 0"
          &gt;
            Submit
          &lt;/a&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;

</code>  
</pre>
            </div>
            <div id="tab03" class="tab-contents">
              <pre class="line-numbers"><code class="language-css">#screenshot-result #screenshot-delete {
                position: absolute;
                top: -10px;
                right: -10px;
                z-index: 1;
                color: red;
                background-color: white;
              }
        
                textarea {
                  resize: none !important;
                }
          
                #screenshot-button {
                  width: 100%;
                }
          
                #screenshot-result {
                  display: block;
                  position: relative;
                }
          
                #screenshot-result #screenshot-edit-layer {
                  position: absolute;
                  top: 0;
                  left: 0;
                  height: 100%;
                  width: 100%;
                  background-color: rgb(0, 0, 0, 0.1);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  border-radius: 0px;
                  border: 2px solid grey;
                  overflow: hidden;
                }
                body {
                  background-color: #f4f4f4;
                }
                .card {
                  border-radius: 0 !important;
                }
                .feedbackplus.feedbackplus-modal {
                  position: fixed;
                  width: 90%;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  z-index: 2147483647;
                  border-radius: 0px;
                  background-color: #fff;
                  box-sizing: border-box;
                }
                .feedbackplus.feedbackplus-canvas-container {
                  overflow: auto;
                  width: 100%;
                  height: calc(90vh - 102px);
                  position: relative;
                }
                .feedbackplus.feedbackplus-highlight {
                  position: absolute;
                  border: 5px solid #00b19c;
                }
                .feedbackplus.feedbackplus-hide {
                  position: absolute;
                  background-color: #000;
                }
                .feedbackplus.feedbackplus-tool-close {
                  display: none;
                  margin-right: 10px;
                  position: absolute;
                  background-color: #fff;
                  border-radius: 0%;
                  border: 1px solid #000;
                  padding: 5px;
                  top: -20px;
                  right: -30px;
                  cursor: pointer;
                }
                .feedbackplus.feedbackplus-hide:hover
                  .feedbackplus.feedbackplus-tool-close,
                .feedbackplus.feedbackplus-highlight:hover
                  .feedbackplus.feedbackplus-tool-close {
                  display: block;
                }
                .feedbackplus.feedbackplus-footer,
                .feedbackplus.feedbackplus-header {
                  padding: 10px;
                  box-sizing: border-box;
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                }
                .feedbackplus.feedbackplus-header {
                  height: 50px;
                  max-height: 50px;
                }
                .feedbackplus.feedbackplus-footer {
                  max-height: 52px;
                }
                .feedbackplus.feedbackplus-header h2 {
                  margin: 5px 20px;
                  font-size: 16px;
                  font-weight: 400;
                }
                .feedbackplus.feedbackplus-footer {
                  display: flex;
                  justify-content: flex-end;
                  padding: 10px 20px;
                  padding-top: 0;
                }
                .feedbackplus.feedbackplus-finish-actions,
                .feedbackplus.feedbackplus-tools {
                  margin-top: 10px;
                }
                .feedbackplus.feedbackplus-backdrop {
                  position: fixed;
                  width: 100%;
                  height: 100%;
                  top: 0;
                  left: 0;
                  z-index: 2147483646;
                  background-color: rgb(0, 0, 0, 0.5);
                }
                .feedbackplus.feedbackplus-tools {
                  display: flex;
                }
                .feedbackplus.feedbackplus-button {
                  padding: 5px 20px;
                  border-radius: 0px;
                  cursor: pointer;
                  user-select: none;
                  display: flex;
                  align-items: center;
                  border: 1px solid grey;
                  color: #00b19c;
                  font-size: 14px;
                }
                .feedbackplus.feedbackplus-button:hover {
                  border-color: #00b19c;
                  background-color: #f5f5f5;
                }
                .feedbackplus.feedbackplus-button:active {
                  background-color: #ededed;
                }
                .feedbackplus.feedbackplus-button.feedbackplus-active {
                  border-color: #00b19c;
                  background-color: #e0e0e0;
                }
                .feedbackplus.feedbackplus-tool-icon {
                  margin-right: 7.6px;
                }
                .feedbackplus.feedbackplus-finish-actions {
                  display: flex;
                }
                .feedbackplus.feedbackplus-close {
                  cursor: pointer;
                }
                @media only screen and (max-width: 600px) {
                  .feedbackplus.feedbackplus-modal {
                    top: 0;
                    left: 0;
                    transform: none;
                    height: 100%;
                    width: 100%;
                    border-radius: 0;
                  }
                  .feedbackplus.feedbackplus-canvas-container {
                    height: calc(100% - 102px);
                  }
                }
            

              </code></pre>
            </div>
            <div id="tab04" class="tab-contents">
<pre class="line-numbers"><code class="language-javascript">(function () {
    const screenshotButton = document.getElementById("screenshot-button");

    const screenshotResult = document.getElementById("screenshot-result");
    const screenshotCanvas = document.getElementById("screenshot-canvas");

    const screenshotEdit = document.getElementById("screenshot-edit");
    const screenshotDelete = document.getElementById("screenshot-delete");
    const feedbackPlus = new FeedbackPlus();

    let screenshot;
    screenshotButton.addEventListener("click", function () {
      if (FeedbackPlus.isSupported()) {
        showScreenshotLoading();
        feedbackPlus
          .capture()
          .then((bitmap) => {
            hideScreenshotLoading();
            screenshot = bitmap;
            updateResultCanvas();
          })
          .catch((e) => hideScreenshotLoading());
      }
    });

    screenshotEdit.addEventListener("click", function () {
      feedbackPlus.showEditDialog(
        screenshot.bitmap,
        function (canvas) {
          FeedbackPlus.canvasToBitmap(canvas).then((bitmap) => {
            screenshot = bitmap;
            updateResultCanvas();
            feedbackPlus.closeEditDialog();
          });
        },
        function () {
          feedbackPlus.closeEditDialog();
        }
      );
    });

    screenshotDelete.addEventListener("click", function () {
      screenshot = null;
      screenshotButton.style.display = "block";
      screenshotResult.style.display = "none";
    });

    function updateResultCanvas() {
      screenshotButton.style.display = "none";
      screenshotResult.style.display = "block";
      const newHeight = (screenshot.height / screenshot.width) * 330;
      screenshotCanvas.width = 330;
      screenshotCanvas.height = newHeight;
      screenshotCanvas
        .getContext("2d")
        .drawImage(screenshot.bitmap, 0, 0, 330, newHeight);
    }

    function showScreenshotLoading() {
      document.getElementById(
        "screenshot-button-notloading"
      ).style.display = "none";
    }

    function hideScreenshotLoading() {
      document.getElementById(
        "screenshot-button-notloading"
      ).style.display = "block";
    }
  })();
</code></pre>
        </div>
          </div>
        </div>
      </div></div>
    </main>

    <footer class="text-muted">
      <div class="container">
        <p class="float-right">
          <a href="#">Back to top</a>
        </p>
      </div>
    </footer>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
      window.jQuery ||
        document.write('<script src="js/jquery.slim.min.js"><\/script>');
    </script>
    <script src="./js/bootstrap.bundle.js"></script>
    <script src="./js/prism.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.22.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-Q3qGP1uJL/B0sEmu57PKXjCirgPKMbg73OLRbTJ6lfHCVU5zkHqmcTI5EV2fSoPV1MHdKsCBE7m/aS6q0pPjRQ==" crossorigin="anonymous"></script> -->
    <script>
      $(function () {
        var $tabButtonItem = $("#tab-button li"),
          $tabSelect = $("#tab-select"),
          $tabContents = $(".tab-contents"),
          activeClass = "is-active";

        $tabButtonItem.first().addClass(activeClass);
        $tabContents.not(":first").hide();

        $tabButtonItem.find("a").on("click", function (e) {
          var target = $(this).attr("href");

          $tabButtonItem.removeClass(activeClass);
          $(this).parent().addClass(activeClass);
          $tabSelect.val(target);
          $tabContents.hide();
          $(target).show();
          e.preventDefault();
        });

        $tabSelect.on("change", function () {
          var target = $(this).val(),
            targetSelectNum = $(this).prop("selectedIndex");

          $tabButtonItem.removeClass(activeClass);
          $tabButtonItem.eq(targetSelectNum).addClass(activeClass);
          $tabContents.hide();
          $(target).show();
        });
      });
    </script>
    <script src="./js/iframe.js"></script>
  </body>
</html>
