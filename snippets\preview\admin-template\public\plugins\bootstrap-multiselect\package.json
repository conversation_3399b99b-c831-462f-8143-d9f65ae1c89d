{"name": "bootstrap-multiselect", "version": "0.9.15", "description": "JQuery multiselect plugin based on Twitter Bootstrap.", "main": "dist/js/bootstrap-multiselect.js", "directories": {"doc": "docs", "test": "tests"}, "repository": {"type": "git", "url": "https://github.com/davidstutz/bootstrap-multiselect"}, "keywords": ["js", "css", "less", "bootstrap", "j<PERSON>y", "multiselect"], "author": "<PERSON>", "license": "Apache License, Version 2.0", "bugs": {"url": "https://github.com/davidstutz/bootstrap-multiselect/issues"}, "homepage": "http://davidstutz.de/bootstrap-multiselect/", "scripts": {"test": "karma start tests/karma.conf.js --single-run"}, "dependencies": {"jquery": "~2.1.3"}, "devDependencies": {"jasmine-core": "*", "karma": "*", "karma-cli": "*", "karma-jasmine": "*", "karma-phantomjs-launcher": "*", "karma-chrome-launcher": "*"}, "types": "./types/index.d.ts"}